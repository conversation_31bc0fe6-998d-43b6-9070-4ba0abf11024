using carparking.BLL.Cache;
using carparking.ChargeModels;
using carparking.Common;
using carparking.Model;
using FastDeepCloner;
using MySql.Data.MySqlClient;
using Newtonsoft.Json.Linq;
using NPOI.Util;
using System;
using System.Collections.Generic;
using System.Data;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using static Aliyun.OSS.Model.ListPartsResult;

namespace carparking.BLL
{
    public class CommonBLL : BaseBLL
    {
        private static DAL.CommonDAL dal = new DAL.CommonDAL();

        /// <summary>
        /// 获取远程数据库订单数据
        /// </summary>
        /// <param name="beginTime"></param>
        /// <param name="endTime"></param>
        /// <param name="connectionString"></param>
        /// <returns></returns>
        public static (List<Model.ParkOrder>, List<Model.OrderDetail>, List<Model.PayOrder>, List<Model.PassRecord>) GetParkOrderList(string beginTime, string endTime, out string msg, string connectionString = "")
        {
            (List<Model.ParkOrder>, List<Model.OrderDetail>, List<Model.PayOrder>, List<Model.PassRecord>) obj = (null, null, null, null);
            msg = string.Empty;
            try
            {
                IDbConnection db = null;
                if (string.IsNullOrWhiteSpace(connectionString)) db = null; else db = new MySqlConnection(connectionString);

                //停车订单数据
                List<Model.ParkOrder> parkOrderList = BLL.ParkOrder.GetAllEntity("*", $"ParkOrder_EnterTime>={beginTime} and ParkOrder_EnterTime<={endTime}", db);
                //停车明细数据
                List<Model.OrderDetail> orderDetailList = BLL.OrderDetail.GetAllEntity("*", $"orderdetail_EnterTime>={beginTime} and orderdetail_EnterTime<={endTime}", db);
                //支付订单数据
                List<Model.PayOrder> payOrderList = BLL.PayOrder.GetAllEntity("*", $"PayOrder_Time>={beginTime} and PayOrder_Time<={endTime}", db);
                //异常订单数据
                List<Model.PassRecord> passRecordList = BLL.PassRecord.GetAllEntity("*", $"PassRecord_AddTime>={beginTime} and PassRecord_AddTime<={endTime}", db);

                obj = (parkOrderList, orderDetailList, payOrderList, passRecordList);

                return obj;

            }
            catch (Exception ex)
            {
                msg = ex.Message;
                BLL.SystemLogs.AddLog(null, "获取远程数据库订单数据", ex.ToString());
                return obj;
            }
        }

        /// <summary>
        /// 获取远程数据并更新本地数据
        /// </summary>
        /// <param name="beginTime"></param>
        /// <param name="endTime"></param>
        /// <param name="connectionString"></param>
        /// <returns></returns>
        public static bool InsertOrderData(string beginTime, string endTime, out string msg, string connectionString = "")
        {
            msg = string.Empty;
            try
            {
                (List<Model.ParkOrder>, List<Model.OrderDetail>, List<Model.PayOrder>, List<Model.PassRecord>) obj = GetParkOrderList(beginTime, endTime, out msg, connectionString);
                if (!string.IsNullOrWhiteSpace(msg)) { return false; }

                return dal.InsertOrderData(obj.Item1, obj.Item2, obj.Item3, obj.Item4);
            }
            catch (Exception ex)
            {
                msg = ex.Message;
                BLL.SystemLogs.AddLog(null, "获取远程数据并更新本地数据", ex.ToString());
                return false;
            }
        }

        /// <summary>
        /// 更新本地数据
        /// </summary>
        /// <param name="beginTime"></param>
        /// <param name="endTime"></param>
        /// <param name="connectionString"></param>
        /// <returns></returns>
        public static bool UpdateOrderData((List<Model.ParkOrder>, List<Model.OrderDetail>, List<Model.PayOrder>, List<Model.PassRecord>) obj)
        {
            return dal.InsertOrderData(obj.Item1, obj.Item2, obj.Item3, obj.Item4);
        }


        public static bool GetParkOrderDeatail(string carNo, string beginTime, out List<object> parkOrderList, out List<object> detailList)
        {
            parkOrderList = null;
            detailList = null;
            try
            {
                parkOrderList = BLL.BaseBLL._Execute($@"SELECT ParkOrder_No,ParkOrder_EnterTime,ParkOrder_OutTime,ParkOrder_TotalAmount from parkorder 
where ParkOrder_CarNo like '%{carNo}%' and ParkOrder_EnterTime >= '{beginTime}' ORDER BY ParkOrder_EnterTime DESC limit 50; ", dal.ReadConnection.ConnectionString);
                detailList = BLL.BaseBLL._Execute($@"SELECT orderdetail_CarNo,orderdetail_ParkAreaName,orderdetail_EnterTime,orderdetail_OutTime,orderdetail_TotalAmount,orderdetail_NextCycleTime , orderdetail_CycleMoney , orderdetail_CycleFreeMin , orderdetail_UseFreeMin, orderdetail_HoursContent ,orderdetail_EnterPasswayName , orderdetail_OutPasswayName  from orderdetail 
where orderdetail_CarNo like '%{carNo}%' and orderdetail_EnterTime >='{beginTime}' order by orderdetail_EnterTime; ", dal.ReadConnection.ConnectionString);
            }
            catch (Exception ex)
            {
                return false;
            }
            return true;
        }


        /// <summary>
        /// 创建支付明细订单号
        /// </summary>
        /// <param name="no">随机编号,不带前缀</param>
        /// <param name="carNo">车牌号</param>
        /// <returns></returns>
        public static string CreatePayPartNo(string no = null, string carNo = null)
        {
            if (string.IsNullOrWhiteSpace(no)) no = "PD" + Utils.CreateNumber; else no = "PD" + no;
            if (!string.IsNullOrWhiteSpace(carNo))
                no = no + "-" + carNo.Substring(1, carNo.Length - 1);
            return no;
        }


        /// <summary>
        /// 创建支付订单号
        /// </summary>
        /// <param name="no">随机编号,不带前缀</param>
        /// <param name="carNo">车牌号</param>
        /// <returns></returns>
        public static string CreatePayOrderNo(string no = null, string parkKey = null)
        {
            if (string.IsNullOrWhiteSpace(no)) no = "PO" + Utils.CreateNumber; else no = "PO" + no;
            if (!string.IsNullOrWhiteSpace(parkKey))
                no = no + parkKey;
            return no;
        }

        #region 支付订单处理
        /// <summary>
        /// 新增支付订单
        /// </summary>
        /// <param name="outTime">出场时间</param>
        /// <param name="payResult">计费结果</param>
        /// <param name="ParkOrderNo">停车订单编号</param>
        /// <param name="lgAdmin">登录账号信息</param>
        /// <param name="sOrderNo"></param>
        /// <param name="PayOrder_PayTypeCode"></param>
        /// <param name="parkAreaNo"></param>
        /// <param name="PayOrder_PayType"></param>
        /// <param name="PayOrder_PayedTime"></param>
        /// <param name="PayOrder_Status"></param>
        /// <param name="sFree">必须是免费放行（支付金额0元）</param>
        /// <param name="writeDataBase"></param>
        /// <param name="parkAreaName"></param>
        /// <param name="ParkOrder_CarType"></param>
        /// <param name="PayOrder_Money"></param>
        /// <param name="PayOrder_PayedMoney"></param>
        /// <returns>新增成功返回支付订单信息，新增失败返回NULL</returns>
        public static Model.PayColl AddPayOrder(DateTime? outTime, ChargeModels.PayResult payResult, string ParkOrderNo, Model.Admins lgAdmin, string sOrderNo = "", string PayOrder_PayTypeCode = "", string parkAreaNo = ""
            , int? PayOrder_PayType = null, DateTime? PayOrder_PayedTime = null, int? PayOrder_Status = 0, string sFree = null, bool writeDataBase = true, string parkAreaName = null
            , string ParkOrder_CarType = null, decimal? PayOrder_Money = null, decimal? PayOrder_PayedMoney = null, ChargeModels.PayResult notModifiedResult = null, Model.ParkOrder parkOrder = null)
        {
            if (!string.IsNullOrWhiteSpace(PayOrder_PayTypeCode) && PayOrder_PayTypeCode != "79001") { lgAdmin = null; }//除了线下现金，都不带操作员

            if (parkOrder == null) parkOrder = BLL.ParkOrder.GetEntity(ParkOrderNo);
            if (parkOrder == null) return null;
            if (string.IsNullOrWhiteSpace(ParkOrder_CarType))
            {
                ParkOrder_CarType = parkOrder.ParkOrder_CarType;
            }
            else
            {
                parkOrder.ParkOrder_CarTypeName = BLL.CarType.GetEntity(ParkOrder_CarType)?.CarType_Name ?? parkOrder.ParkOrder_CarTypeName;
            }

            Model.Parking parking = BLL.Parking.GetEntity(parkOrder.ParkOrder_ParkNo);
            if (parking == null) return null;

            if (notModifiedResult == null && payResult != null) notModifiedResult = payResult.Copy();

            PayOrder_PayedTime = PayOrder_PayedTime ?? DateTime.Now;

            List<Model.PayOrder> payOrderList = new List<Model.PayOrder>();
            List<Model.PayPart> payPartList = new List<Model.PayPart>();

            Model.CarCardType cct = BLL.CarCardType.GetEntity(parkOrder.ParkOrder_CarCardType);

            if (payResult != null)//有计费运算结果
            {
                //无计费明细运算详情的（例如超时缴费，无计费规则，则不会有计费详情，直接返回无需缴费 或者 判断已缴过并且出场时间比缴费时间小于一分钟 或者 多位多车），不创建支付订单
                if (payResult.list == null || payResult.list.Count == 0) { return null; }
                //实付金额为0 && 储值金额为0 && 优惠金额为0 &&应付金额为0（这种情况一般为计费规则未产生费用），不创建支付订单
                if (PayOrder_PayType != 3 && payResult.payedamount == 0 && payResult.chuzhiamount == 0 && payResult.couponamount == 0 && payResult.orderamount == 0)
                {
                    //创建0元支付订单
                    if (BLL.PolicyPark.GetEntity("")?.PolicyPark_CreateZero != 1)
                        return null;
                }

                string PayOrder_Desc = sFree;

                string couponNoArray = "";


                if (payResult.uselist != null && payResult.uselist.Count > 0)
                {
                    couponNoArray = string.Join(",", payResult.uselist.Select(x => x.CouponRecord_No).ToList());
                    if (payResult.payedamount == 0 && (payResult.uselist.Find(c => c.CouponRecord_Other == 1) != null))
                    {
                        PayOrder_PayTypeCode = Model.EnumPayType.chargingPilePayment.ToString();//支付类型
                    }

                }

                List<string> noList = Utils.GetRandomLst(25 + payResult.list.Count * 4);
                Model.PayOrder payModel = new Model.PayOrder();
                payModel.PayOrder_ParkOrderNo = parkOrder.ParkOrder_No;
                payModel.PayOrder_Time = DateTime.Now;
                payModel.PayOrder_CarNo = parkOrder.ParkOrder_CarNo;
                payModel.PayOrder_Status = PayOrder_Status;
                payModel.PayOrder_PayedTime = PayOrder_PayedTime;
                if (!string.IsNullOrWhiteSpace(PayOrder_PayTypeCode))
                {
                    payModel.PayOrder_PayTypeCode = PayOrder_PayTypeCode;
                }
                else
                {
                    payModel.PayOrder_PayTypeCode = Model.EnumPayType.OffLineCash.ToString();
                }
                payModel.PayOrder_PayType = PayOrder_PayType;
                payModel.PayOrder_CouponRecordNo = couponNoArray;
                payModel.PayOrder_Desc = PayOrder_Desc;
                payModel.PayOrder_ParkKey = parking.Parking_Key;
                payModel.PayOrder_ParkNo = parking.Parking_No;
                payModel.PayOrder_OrderTypeNo = cct != null ? CarTypeHelper.GetOrderType(cct.CarCardType_Category, true) : "";
                payModel.PayOrder_Category = cct != null ? cct.CarCardType_Category : "";
                payModel.PayOrder_EnterTime = parkOrder.ParkOrder_EnterTime;
                payModel.PayOrder_TempTimeCount = (int)payResult.parktimemin;
                payModel.PayOrder_TimeCountDesc = Utils.DateDiffStr((int)payResult.parktimemin * 60);
                payModel.PayOrder_CarCardTypeNo = parkOrder.ParkOrder_CarCardType;
                payModel.PayOrder_CarTypeNo = ParkOrder_CarType;
                payModel.PayOrder_DiscountMoney = payResult.couponamount;
                payModel.PayOrder_StoredMoney = 0;
                payModel.PayOrder_ParkAreaNo = string.IsNullOrWhiteSpace(parkAreaNo) ? parkOrder.ParkOrder_ParkAreaNo : parkAreaNo;
                payModel.PayOrder_UserNo = lgAdmin?.Admins_Account;
                payModel.PayOrder_OperatorName = lgAdmin?.Admins_Name;
                payModel.PayOrder_Account = lgAdmin?.Admins_Account;
                payModel.PayOrder_AdminID = lgAdmin?.Admins_ID;
                payModel.PayOrder_OwnerSpace = BLL.Car.GetEntityByCarNo(payModel.PayOrder_CarNo)?.Car_OwnerSpace;

                //免费放行
                if (!string.IsNullOrWhiteSpace(sFree))
                {
                    payModel = BLL.PayOrder.CreatePayOrder(true, payModel, parking.Parking_Key, null, lgAdmin);
                    payModel.PayOrder_PayedMoney = 0;
                    payModel.PayOrder_Money = payResult?.orderamount ?? 0;
                    payModel.PayOrder_DiscountMoney = payModel.PayOrder_Money;
                    payModel.PayOrder_SelfMoney = 0;
                    payModel.PayOrder_StoredMoney = 0;
                    payOrderList.Add(payModel);

                    Model.PayPart paypart = new Model.PayPart();
                    paypart.PayPart_No = CreatePayPartNo(null, payModel.PayOrder_CarNo);
                    paypart.PayPart_AddID = lgAdmin == null ? payModel.PayOrder_AdminID : lgAdmin.Admins_ID;
                    paypart.PayPart_AddTime = DateTime.Now;
                    paypart.PayPart_OrderTypeNo = payModel.PayOrder_OrderTypeNo;
                    paypart.PayPart_ParkKey = payModel.PayOrder_ParkKey;
                    paypart.PayPart_ParkNo = payModel.PayOrder_ParkNo;
                    paypart.PayPart_Status = payModel.PayOrder_Status;
                    paypart.PayPart_PayOrderNo = payModel.PayOrder_No;
                    paypart.PayPart_PayType = payModel.PayOrder_PayType;
                    paypart.PayPart_PayTypeCode = payModel.PayOrder_PayTypeCode;
                    paypart.PayPart_OrderMoney = payModel.PayOrder_Money;
                    paypart.PayPart_CouponMoney = payModel.PayOrder_DiscountMoney;
                    paypart.PayPart_PayedMoney = payModel.PayOrder_PayedMoney;
                    paypart.PayPart_BeginTime = payModel.PayOrder_EnterTime;
                    paypart.PayPart_EndTime = payModel.PayOrder_Time;
                    paypart.PayPart_TimeCount = payModel.PayOrder_TempTimeCount;
                    paypart.PayPart_Category = payModel.PayOrder_Category;
                    paypart.PayPart_CarCardTypeNo = payModel.PayOrder_CarCardTypeNo;
                    paypart.PayPart_CarCardTypeName = BLL.CarCardType.GetEntity(payModel.PayOrder_CarCardTypeNo)?.CarCardType_Name;
                    paypart.PayPart_CarTypeNo = payModel.PayOrder_CarTypeNo;
                    paypart.PayPart_CarTypeName = BLL.CarType.GetEntity(payModel.PayOrder_CarTypeNo)?.CarType_Name;
                    paypart.PayPart_CarNo = payModel.PayOrder_CarNo;
                    paypart.PayPart_ParkOrderNo = payModel.PayOrder_ParkOrderNo;
                    paypart.PayPart_EditTime = payModel.PayOrder_PayedTime;
                    paypart.PayPart_Desc = payModel.PayOrder_TimeCountDesc;
                    paypart.PayPart_ParkAreaNo = string.IsNullOrWhiteSpace(parkAreaNo) ? payModel.PayOrder_ParkAreaNo : parkAreaNo;
                    paypart.PayPart_ParkAreaName = string.IsNullOrWhiteSpace(parkAreaName) ? parkOrder == null ? BLL.ParkArea.GetEntity(payModel.PayOrder_ParkAreaNo)?.ParkArea_Name : parkOrder.ParkOrder_ParkAreaName : parkAreaName;
                    paypart.PayPart_Desc = sFree;
                    payPartList.Add(paypart);
                }
                else
                {
                    var selfAmount = payResult.cashrobotamount - payResult.zlamount;//自主缴费金额
                    if (payResult.payedamount > 0                                                                                     //支付金额大于0
                        || (payResult.orderamount > 0 && payResult.chuzhiamount == 0)                                                 //订单金额大于0，储值金额等于0
                        || (payResult.orderamount > 0 && payResult.chuzhiamount > 0 && payResult.orderamount >= payResult.chuzhiamount)//订单金额大于0，储值金额大于0，订单金额大于或等于储值金额
                        || (payResult.orderamount > 0 && payResult.couponamount > 0)
                        || (payResult.orderamount > 0 && selfAmount > 0) || true)                                                             //订单金额大于0，自主缴费金额大于0                        
                    {
                        //if (cct?.CarCardType_Category == Model.EnumCarType.Prepaid.ToString())
                        //    payModel.PayOrder_OrderTypeNo = Convert.ToString((int)EnumOrderType.Temp);

                        if (selfAmount > 0)
                        {
                            if (payResult.payedamount == 0)
                                payModel.PayOrder_PayTypeCode = Convert.ToString(Model.EnumPayType.owinDeviceCashMoney);

                            payModel.PayOrder_OutReduceMoney = payResult.zlamount;
                            payModel.PayOrder_SelfMoney = payResult.cashrobotamount;
                        }

                        if (payResult.chuzhiamount > 0) payModel.PayOrder_StoredMoney = payResult.chuzhiamount;

                        payModel.PayOrder_Money = payResult.orderamount;
                        payModel.PayOrder_PayedMoney = payResult.payedamount + selfAmount;

                        var orderMoney = payResult.orderamount - (payResult.chuzhiamount + payResult.couponamount + selfAmount);
                        if (orderMoney > payResult.payedamount)//应付金额 -（存储抵扣金额+优惠金额+自助缴费金额）> 实收金额，一般属于手动改价（手动改价部分累加到优惠金额里）
                        {
                            payModel.PayOrder_DiscountMoney += (orderMoney - payResult.payedamount);
                        }

                        if (!string.IsNullOrWhiteSpace(sOrderNo))
                        {
                            payModel.PayOrder_No = sOrderNo;
                        }
                        else
                        {
                            payModel.PayOrder_No = CreatePayOrderNo(noList.Last(), parking.Parking_Key);
                            noList.Remove(noList.Last());
                        }
                        payModel = BLL.PayOrder.CreatePayOrder(true, payModel, parking.Parking_Key, null, lgAdmin);

                        var paymsg = (payResult.payedmsg?.Contains("累计") ?? false) ? payResult.payedmsg : "";
                        if (!string.IsNullOrEmpty(paymsg))
                        {
                            payModel.PayOrder_Desc += paymsg;
                        }

                        if (payResult.carfeesamount > 0)
                        {
                            payModel.PayOrder_Desc += $" 限额减免 {payResult.carfeesamount} 元";
                        }

                        var dlst = BLL.CommonBLL.CreatePayPartList(outTime, payModel, 0, payResult, parkOrder, lgAdmin, noList.GetRange(0, payResult.list.Count + 5));
                        payPartList.AddRange(dlst);
                        noList.RemoveRange(0, payResult.list.Count + 5);
                        payOrderList.Add(payModel);
                    }

                    //if (payResult.chuzhiamount > 0)
                    //{
                    //    Model.PayOrder payModel2 = TyziTools.Json.ToModel<Model.PayOrder>(TyziTools.Json.ToString(payModel));
                    //    payModel2.PayOrder_OrderTypeNo = Convert.ToString((int)EnumOrderType.StoreCharge);
                    //    payModel2.PayOrder_PayTypeCode = Convert.ToString(Model.EnumPayType.owinDeviceCashMoney);
                    //    payModel2.PayOrder_No = CreatePayOrderNo(noList.Last(), parking.Parking_Key);
                    //    noList.Remove(noList.Last());
                    //    payModel2.PayOrder_Money = payResult.chuzhiamount;
                    //    payModel2.PayOrder_StoredMoney = payResult.chuzhiamount;
                    //    payModel2.PayOrder_PayedMoney = 0;
                    //    payModel2.PayOrder_DiscountMoney = 0;
                    //    payModel2.PayOrder_CouponRecordNo = null;
                    //    payModel2 = BLL.PayOrder.CreatePayOrder(true, payModel2, parking.Parking_Key, null, lgAdmin);
                    //    payModel2.PayOrder_Desc = "储值金额抵扣";
                    //    payModel2.PayOrder_TimeCountDesc = "储值金额抵扣";

                    //    //payPartList.AddRange(BLL.CommonBLL.CreatePayPartList(outTime, payModel2, 1, payResult, parkOrder, lgAdmin, noList.GetRange(0, payResult.list.Count + 5)));
                    //    //noList.RemoveRange(0, payResult.list.Count + 5);
                    //    payOrderList.Add(payModel2);
                    //}
                }


                //计费详情
                CreateCalcDetail(notModifiedResult != null ? notModifiedResult : payResult, parkOrder);

                if (writeDataBase)
                {
                    var result = BLL.PayOrder.AddPayOrderList(payOrderList, payPartList);
                    if (result)
                    {
                        return new Model.PayColl() { payOrderList = payOrderList, payPartList = payPartList };
                    }
                    else
                    {
                        return null;
                    }
                }
                else
                {
                    return new Model.PayColl() { payOrderList = payOrderList, payPartList = payPartList };
                }
            }
            else//无计费运算结果
            {
                Model.PayOrder payModel = new Model.PayOrder();
                payModel.PayOrder_ParkOrderNo = parkOrder.ParkOrder_No;
                payModel.PayOrder_Time = DateTime.Now;
                payModel.PayOrder_CarNo = parkOrder.ParkOrder_CarNo;
                payModel.PayOrder_Status = PayOrder_Status;
                payModel.PayOrder_PayedTime = PayOrder_PayedTime;
                if (!string.IsNullOrWhiteSpace(PayOrder_PayTypeCode))
                {
                    payModel.PayOrder_PayTypeCode = PayOrder_PayTypeCode;
                }
                else
                {
                    payModel.PayOrder_PayTypeCode = Model.EnumPayType.OffLineCash.ToString();
                }
                payModel.PayOrder_PayType = PayOrder_PayType;
                //payModel.PayOrder_CouponRecordNo = couponNoArray;
                payModel.PayOrder_Desc = sFree;
                payModel.PayOrder_ParkKey = parking.Parking_Key;
                payModel.PayOrder_ParkNo = parking.Parking_No;
                payModel.PayOrder_OrderTypeNo = cct != null ? CarTypeHelper.GetOrderType(cct.CarCardType_Category, true) : "";
                payModel.PayOrder_Category = cct != null ? cct.CarCardType_Category : "";
                payModel.PayOrder_EnterTime = parkOrder.ParkOrder_EnterTime;
                payModel.PayOrder_TempTimeCount = Convert.ToInt32((outTime - parkOrder.ParkOrder_EnterTime).Value.TotalMinutes);
                payModel.PayOrder_TimeCountDesc = Utils.DateDiffStr(payModel.PayOrder_TempTimeCount.Value * 60);
                payModel.PayOrder_CarCardTypeNo = parkOrder.ParkOrder_CarCardType;
                payModel.PayOrder_CarTypeNo = string.IsNullOrEmpty(ParkOrder_CarType) ? parkOrder.ParkOrder_CarType : ParkOrder_CarType;
                //payModel.PayOrder_DiscountMoney = payResult.couponamount;
                payModel.PayOrder_ParkAreaNo = string.IsNullOrWhiteSpace(parkAreaNo) ? parkOrder.ParkOrder_ParkAreaNo : parkAreaNo;
                payModel.PayOrder_UserNo = lgAdmin?.Admins_Account;
                payModel.PayOrder_OperatorName = lgAdmin?.Admins_Name;
                payModel.PayOrder_Account = lgAdmin?.Admins_Account;
                payModel.PayOrder_AdminID = lgAdmin?.Admins_ID;

                payModel.PayOrder_Money = PayOrder_Money;
                payModel.PayOrder_PayedMoney = PayOrder_PayedMoney;
                payModel.PayOrder_DiscountMoney = PayOrder_Money - PayOrder_PayedMoney;
                payModel.PayOrder_OwnerSpace = BLL.Car.GetEntityByCarNo(payModel.PayOrder_CarNo)?.Car_OwnerSpace;

                List<string> noList = Utils.GetRandomLst(10);//预生成订单编号
                if (!string.IsNullOrWhiteSpace(sOrderNo))
                {
                    payModel.PayOrder_No = sOrderNo;
                }
                else
                {
                    payModel.PayOrder_No = CreatePayOrderNo(noList.Last(), parking.Parking_Key);
                    noList.Remove(noList.Last());
                }
                payModel = BLL.PayOrder.CreatePayOrder(true, payModel, parking.Parking_Key, null, lgAdmin);

                payPartList.AddRange(BLL.CommonBLL.CreatePayPartList(outTime, payModel, 2, null, parkOrder, lgAdmin, noList));
                payOrderList.Add(payModel);

                if (writeDataBase)
                {
                    var result = BLL.PayOrder.AddPayOrderList(payOrderList, payPartList);
                    if (result)
                    {
                        return new Model.PayColl() { payOrderList = payOrderList, payPartList = payPartList };
                    }
                    else
                    {
                        return null;
                    }
                }
                else
                {
                    return new Model.PayColl() { payOrderList = payOrderList, payPartList = payPartList };
                }

            }
        }

        /// <summary>
        /// 创建计费详情
        /// </summary>
        /// <param name="payResult">计费结果</param>
        /// <param name="parkOrder">停车订单</param>
        /// <returns></returns>
        public static bool CreateCalcDetail(ChargeModels.PayResult payResult, Model.ParkOrder parkOrder)
        {
            if (payResult != null && payResult.list != null)
            {
                List<ChargeModels.PayDetail> detailList = payResult.list;
                detailList = detailList.OrderBy(x => x.starttime).ToList();

                List<Model.CalcDetail> calcDetaiList = new List<Model.CalcDetail>();
                detailList.ForEach(x =>
                {
                    calcDetaiList.Add(new Model.CalcDetail()
                    {
                        CalcDetail_CarNo = parkOrder?.ParkOrder_CarNo,
                        CalcDetail_StartTime = x.starttime,
                        CalcDetail_EndTime = x.endtime,
                        CalcDetail_AreaNo = x.areano,
                        CalcDetail_AreaName = x.areaname,
                        CalcDetail_ParkOrderNo = x.parkorderno,
                        CalcDetail_OrderDetailID = x.orderdetailid,
                        CalcDetail_OrderDetailNo = x.orderdetailno,
                        CalcDetail_PreCycleTime = x.preNextcycletime,
                        CalcDetail_PreCycleMoney = x.preNextCyclePaidFees,
                        CalcDetail_PreCycleMin = x.preNextcyclefreemin,
                        CalcDetail_CycleTime = x.nextcycletime,
                        CalcDetail_CycleMoney = x.NextCyclePaidFees,
                        CalcDetail_CycleMin = x.nextcyclefreemin,
                        CalcDetail_Hourstime = x.nexthourstime,
                        CalcDetail_HoursContent = x.nexthourscontent,
                        CalcDetail_IsOverTime = x.isovertime ? 1 : 0,
                        CalcDetail_IsCarExpire = x.iscarexpire ? 1 : 0,
                        CalcDetail_Payed = x.payed,
                        CalcDetail_PayedMsg = x.payedmsg,
                        CalcDetail_PayedAmount = x.payedamount,
                        CalcDetail_CouponAmount = x.couponamount,
                        CalcDetail_Parktimemin = x.parktimemin,
                        CalcDetail_UseMin = x.currentfreemin,
                        CalcDetail_BeginTime = x.calcbegintime,
                        CalcDetail_CalcContent = x.CalcResult ?? ""
                    }); ;
                });
                return BLL.BaseBLL._Insert(calcDetaiList) >= 0;
                //var calcJson = parkOrder.ParkOrder_CalcDetail;
                //if (!string.IsNullOrWhiteSpace(calcJson))
                //{
                //    detailList.AddRange(TyziTools.Json.ToObject<List<ChargeModels.PayDetail>>(calcJson));
                //    detailList = detailList.Distinct().ToList();
                //}
                //if (detailList.Count > 30) { detailList.RemoveRange(0, detailList.Count - 30); }
                //parkOrder.ParkOrder_CalcDetail = TyziTools.Json.ToString(detailList);
                //BLL.ParkOrder._UpdateByModelByNo(parkOrder);
            }
            return false;
        }



        /// <summary>
        /// 创建计费详情
        /// </summary>
        /// <param name="payResult">计费结果</param>
        /// <param name="parkOrder">停车订单</param>
        /// <returns></returns>
        public static List<Model.CalcDetail> GetCalcDetail(ChargeModels.PayResult payResult, Model.ParkOrder parkOrder, bool isUnpaidresult = false)
        {
            if (payResult != null && payResult.list != null)
            {
                List<ChargeModels.PayDetail> detailList = payResult.list;
                detailList = detailList.OrderBy(x => x.starttime).ToList();

                List<Model.CalcDetail> calcDetaiList = new List<Model.CalcDetail>();
                detailList.ForEach(x =>
                {
                    calcDetaiList.Add(new Model.CalcDetail()
                    {
                        CalcDetail_CarNo = parkOrder?.ParkOrder_CarNo ?? "",
                        CalcDetail_StartTime = x.starttime,
                        CalcDetail_EndTime = x.endtime,
                        CalcDetail_AreaNo = x.areano,
                        CalcDetail_AreaName = x.areaname,
                        CalcDetail_ParkOrderNo = x.parkorderno,
                        CalcDetail_OrderDetailID = x.orderdetailid,
                        CalcDetail_OrderDetailNo = x.orderdetailno,
                        CalcDetail_PreCycleTime = x.preNextcycletime,
                        CalcDetail_PreCycleMoney = x.preNextCyclePaidFees,
                        CalcDetail_PreCycleMin = x.preNextcyclefreemin,
                        CalcDetail_CycleTime = x.nextcycletime,
                        CalcDetail_CycleMoney = x.NextCyclePaidFees,
                        CalcDetail_CycleMin = x.nextcyclefreemin,
                        CalcDetail_Hourstime = x.nexthourstime,
                        CalcDetail_HoursContent = x.nexthourscontent,
                        CalcDetail_IsOverTime = x.isovertime ? 1 : 0,
                        CalcDetail_IsCarExpire = x.iscarexpire ? 1 : 0,
                        CalcDetail_Payed = x.payed,
                        CalcDetail_PayedMsg = isUnpaidresult ? "追缴费用：" + x.payedmsg : x.payedmsg,
                        CalcDetail_PayedAmount = x.payedamount,
                        CalcDetail_CouponAmount = x.couponamount,
                        CalcDetail_Parktimemin = x.parktimemin,
                        CalcDetail_UseMin = x.currentfreemin,
                        CalcDetail_BeginTime = x.calcbegintime,
                        CalcDetail_CalcContent = x.CalcResult ?? ""
                    }); ;
                });
                return calcDetaiList;
            }
            return null;
        }

        /// <summary>
        /// 新增微信无感支付订单（比如：订单金额2元，优惠1元，则生成的支付订单是应收1元，优惠1元，实收0元，用于无感支付）
        /// </summary>
        /// <param name="outTime">出场时间</param>
        /// <param name="parking">车场</param>
        /// <param name="parkOrder">停车订单</param>
        /// <param name="payResult">计费结果</param>
        /// <param name="admins">登录账号信息</param>
        /// <param name="PayOrder_Desc">支付订单描述</param>
        /// <returns>新增成功返回支付订单信息，新增失败返回NULL</returns>
        public static Model.PayColl AddWXPayOrder(DateTime? outTime, ChargeModels.PayResult payResult, string ParkOrderNo, Model.Admins lgAdmin, string sOrderNo = "", string PayOrder_PayTypeCode = "", string parkAreaNo = ""
            , int? PayOrder_PayType = null, DateTime? PayOrder_PayedTime = null, int? PayOrder_Status = 0, string sFree = null, bool writeDataBase = true, string parkAreaName = null
            , string ParkOrder_CarType = null, decimal? PayOrder_Money = null, decimal? PayOrder_PayedMoney = null)
        {
            if (!string.IsNullOrWhiteSpace(PayOrder_PayTypeCode) && PayOrder_PayTypeCode != "79001") { lgAdmin = null; }//除了线下现金，都不带操作员

            Model.ParkOrder parkOrder = BLL.ParkOrder.GetEntity(ParkOrderNo);
            if (parkOrder == null) return null;
            if (!string.IsNullOrWhiteSpace(ParkOrder_CarType))
            {
                parkOrder.ParkOrder_CarType = ParkOrder_CarType;
            }

            Model.Parking parking = BLL.Parking.GetEntity(parkOrder.ParkOrder_ParkNo);
            if (parking == null) return null;

            List<Model.PayOrder> payOrderList = new List<Model.PayOrder>();
            List<Model.PayPart> payPartList = new List<Model.PayPart>();

            Model.CarCardType cct = BLL.CarCardType.GetEntity(parkOrder.ParkOrder_CarCardType);

            if (payResult != null)
            {
                //无计费明细运算详情的（例如超时缴费，无计费规则，则不会有计费详情，直接返回无需缴费 或者 判断已缴过并且出场时间比缴费时间小于一分钟 或者 多位多车），不创建支付订单
                if (payResult.list == null || payResult.list.Count == 0) { return null; }
                //实付金额为0 && 储值金额为0 && 优惠金额为0 &&应付金额为0（这种情况一般为计费规则未产生费用），不创建支付订单
                if (payResult.payedamount == 0 && payResult.chuzhiamount == 0 && payResult.couponamount == 0 && payResult.orderamount == 0) { return null; }

                string PayOrder_Desc = sFree;

                string couponNoArray = "";
                if (payResult.uselist != null && payResult.uselist.Count > 0)
                {
                    couponNoArray = string.Join(",", payResult.uselist.Select(x => x.CouponRecord_No).ToList());
                }

                List<string> noList = Utils.GetRandomLst(25 + payResult.list.Count * 3);
                Model.PayOrder payModel = new Model.PayOrder();
                payModel.PayOrder_ParkOrderNo = parkOrder.ParkOrder_No;
                payModel.PayOrder_Time = DateTime.Now;
                payModel.PayOrder_CarNo = parkOrder.ParkOrder_CarNo;
                payModel.PayOrder_Status = PayOrder_Status;
                payModel.PayOrder_PayedTime = PayOrder_PayedTime;
                if (!string.IsNullOrWhiteSpace(PayOrder_PayTypeCode))
                {
                    payModel.PayOrder_PayTypeCode = PayOrder_PayTypeCode;
                }
                payModel.PayOrder_PayType = PayOrder_PayType;
                payModel.PayOrder_CouponRecordNo = couponNoArray;
                payModel.PayOrder_Desc = PayOrder_Desc;
                payModel.PayOrder_ParkKey = parking.Parking_Key;
                payModel.PayOrder_ParkNo = parking.Parking_No;
                payModel.PayOrder_OrderTypeNo = cct != null ? CarTypeHelper.GetOrderType(cct.CarCardType_Category, true) : "";
                payModel.PayOrder_Category = cct != null ? cct.CarCardType_Category : "";
                payModel.PayOrder_EnterTime = parkOrder.ParkOrder_EnterTime;
                payModel.PayOrder_TempTimeCount = (int)payResult.parktimemin;
                payModel.PayOrder_TimeCountDesc = Utils.DateDiffStr((int)payResult.parktimemin * 60);
                payModel.PayOrder_CarCardTypeNo = parkOrder.ParkOrder_CarCardType;
                payModel.PayOrder_CarTypeNo = parkOrder.ParkOrder_CarType;
                payModel.PayOrder_DiscountMoney = payResult.couponamount;
                payModel.PayOrder_StoredMoney = payResult.chuzhiamount;
                payModel.PayOrder_ParkAreaNo = string.IsNullOrWhiteSpace(parkAreaNo) ? parkOrder.ParkOrder_ParkAreaNo : parkAreaNo;
                payModel.PayOrder_UserNo = lgAdmin?.Admins_Account;
                payModel.PayOrder_OperatorName = lgAdmin?.Admins_Name;
                payModel.PayOrder_Account = lgAdmin?.Admins_Account;
                payModel.PayOrder_AdminID = lgAdmin?.Admins_ID;
                payModel.PayOrder_OwnerSpace = BLL.Car.GetEntityByCarNo(payModel.PayOrder_CarNo)?.Car_OwnerSpace;

                if (payResult.orderamount > 0 && payResult.couponamount > 0) //订单金额大于0，优惠金额大于0
                {
                    payModel.PayOrder_Money = payResult.couponamount;
                    payModel.PayOrder_PayedMoney = 0;
                    payModel.PayOrder_DiscountMoney = payResult.couponamount;
                    payModel.PayOrder_StoredMoney = 0;
                    payModel.PayOrder_Desc = "无感支付优惠订单";

                    if (!string.IsNullOrWhiteSpace(sOrderNo))
                    {
                        payModel.PayOrder_No = sOrderNo;
                    }
                    else
                    {
                        payModel.PayOrder_No = CreatePayOrderNo(noList.Last(), parking.Parking_Key);
                        noList.Remove(noList.Last());
                    }
                    payModel = BLL.PayOrder.CreatePayOrder(true, payModel, parking.Parking_Key, null, lgAdmin);

                    payPartList.AddRange(BLL.CommonBLL.CreatePayPartList(outTime, payModel, 0, payResult, parkOrder, lgAdmin, noList.GetRange(0, payResult.list.Count + 5)));
                    noList.RemoveRange(0, payResult.list.Count + 5);
                    payOrderList.Add(payModel);
                }

                //if (payResult.chuzhiamount > 0)
                //{
                //    Model.PayOrder payModel2 = TyziTools.Json.ToModel<Model.PayOrder>(TyziTools.Json.ToString(payModel));
                //    payModel2.PayOrder_OrderTypeNo = Convert.ToString((int)EnumOrderType.StoreCharge);
                //    payModel2.PayOrder_No = CreatePayOrderNo(noList.Last(), parking.Parking_Key);
                //    noList.Remove(noList.Last());
                //    payModel2.PayOrder_Money = payResult.chuzhiamount;
                //    payModel2.PayOrder_PayedMoney = 0;
                //    payModel2.PayOrder_DiscountMoney = 0;
                //    payModel2.PayOrder_CouponRecordNo = null;
                //    payModel2 = BLL.PayOrder.CreatePayOrder(true, payModel2, parking.Parking_Key, null, lgAdmin);
                //    payModel2.PayOrder_Desc = "储值金额抵扣";
                //    payModel2.PayOrder_TimeCountDesc = "储值金额抵扣";

                //    payPartList.AddRange(BLL.CommonBLL.CreatePayPartList(outTime, payModel2, 1, payResult, parkOrder, lgAdmin, noList.GetRange(0, payResult.list.Count + 5)));
                //    noList.RemoveRange(0, payResult.list.Count + 5);
                //    payOrderList.Add(payModel2);
                //}

                //计费详情
                CreateCalcDetail(payResult, parkOrder);

                if (writeDataBase)
                {
                    var result = BLL.PayOrder.AddPayOrderList(payOrderList, payPartList);
                    if (result)
                    {
                        return new Model.PayColl() { payOrderList = payOrderList, payPartList = payPartList };
                    }
                    else
                    {
                        return null;
                    }
                }
                else
                {
                    return new Model.PayColl() { payOrderList = payOrderList, payPartList = payPartList };
                }
            }

            return null;

        }

        /// <summary>
        /// 支付成功(更新停车明细状态、支付状态、优惠券状态)
        /// </summary>
        /// <param name="pdlist">停车明细计费集合</param>
        /// <param name="odList">停车明细记录集合（停车明细记录编号、出场信息、缴费类型、出口类型等信息由T16必填）</param>
        /// <param name="couponRecordUseList">优惠券使用情况</param>
        /// <returns></returns>
        public static bool PaySuccess(ChargeModels.PayResult payResult, Model.ParkOrder po, Model.PayColl payColl, Model.Car car)
        {
            List<Model.HoursTotal> hoursTotals = null;
            //停车明细
            List<Model.OrderDetail> odList = BLL.OrderDetail.GetAllEntity(po.ParkOrder_No);
            if (odList != null && odList.Count > 0 && payResult != null && payResult.list != null)
            {
                odList.ForEach(detail =>
                {
                    var pdItem = payResult.list.Where(x => x.orderdetailno == detail.OrderDetail_No).FirstOrDefault();
                    if (pdItem != null && !string.IsNullOrWhiteSpace(pdItem.orderdetailno))
                    {
                        detail.OrderDetail_NextCycleTime = pdItem.nextcycletime;//下一次周期生效时间
                        detail.Orderdetail_CycleMoney = pdItem.NextCyclePaidFees;//下一次停车，周期已累积支付总金额
                        detail.Orderdetail_CycleFreeMin = pdItem.nextcyclefreemin;//下一次停车，周期已累积免费分钟

                        if (!string.IsNullOrEmpty(pdItem.nexthourscontent) && pdItem.nexthourstime != null)
                        {
                            //detail.orderdetail_HoursBeginTime = pdItem.nexthourstime;
                            //detail.orderdetail_HoursContent = pdItem.nexthourscontent;
                            hoursTotals = hoursTotals ?? new List<Model.HoursTotal>();
                            hoursTotals.Add(new Model.HoursTotal()
                            {
                                HoursTotal_No = detail.OrderDetail_No,
                                HoursTotal_CarNo = detail.OrderDetail_CarNo,
                                HoursTotal_ParkOrderNo = detail.OrderDetail_ParkOrderNo,
                                HoursTotal_CarType = detail.OrderDetail_CarType,
                                HoursTotal_CarCardType = detail.OrderDetail_CarCardType,
                                HoursTotal_ParkAreaNo = detail.OrderDetail_ParkAreaNo,
                                HoursTotal_EnterTime = detail.OrderDetail_EnterTime,
                                HoursTotal_PayTime = DateTime.Now,
                                HoursTotal_BeginTime = pdItem.nexthourstime,
                                HoursTotal_Content = pdItem.nexthourscontent,
                            });
                        }

                        detail.Orderdetail_UseFreeMin = Utils.ObjectToInt(detail.Orderdetail_UseFreeMin, 0) + Utils.ObjectToInt(pdItem.currentfreemin, 0);
                        detail.OrderDetail_IsSettle = 1;//结算状态
                        if (pdItem.calcbegintime != null)
                        {
                            detail.OrderDetail_CurrCalcTime = pdItem.calcbegintime;//当前计费开始时间
                        }
                        detail.OrderDetail_TotalAmount = pdItem.payedamount;//出场总金额
                    }
                });
            }
            //支付订单
            if (payColl != null)
            {
                decimal sumMoney = 0;
                decimal sumPayedMoney = 0;
                payColl.payOrderList?.ForEach(payModel =>
                {

                    payModel.PayOrder_PayedTime = DateTime.Now;
                    payModel.PayOrder_Status = 1;//已支付
                    sumMoney += Utils.ObjectToDecimal(payModel.PayOrder_Money, 0);//更新停车订单总金额
                    sumPayedMoney += Utils.ObjectToDecimal(payModel.PayOrder_PayedMoney, 0);//更新停车订单实收总金额
                });
                po.ParkOrder_TotalAmount = sumMoney;
                po.ParkOrder_TotalPayed = sumPayedMoney;

                payColl.payPartList?.ForEach(payModel =>
                {
                    payModel.PayPart_EditTime = DateTime.Now;
                    payModel.PayPart_Status = 1;//已支付
                });
            }
            //优惠券
            List<Model.CouponRecord> couponPayList = null;
            if (payColl != null && payResult.uselist != null && payResult.uselist.Count > 0)
            {
                string couponIDArray = string.Join(",", payResult.uselist.Select(x => x.CouponRecord_No).ToList());
                List<Model.CouponRecord> couponList = BLL.CouponRecord._GetAllEntity(new Model.CouponRecord(), "CouponRecord_ID,CouponRecord_No", $"CouponRecord_ParkOrderNo='{po.ParkOrder_No}'");
                if (couponList != null && couponList.Count > 0)
                {
                    List<string> idList = payResult.uselist.Select(x => x.CouponRecord_No).ToList();
                    couponPayList = couponList.Where(x => idList.Contains(x.CouponRecord_ID.ToString())).ToList();
                    couponPayList.ForEach(x =>
                    {
                        var couponUse = payResult.uselist.Where(y => y.CouponRecord_No == x.CouponRecord_No).FirstOrDefault();
                        x.CouponRecord_UsedTime = DateTime.Now;
                        x.CouponRecord_Status = 1;
                        x.CouponRecord_Paid = couponUse == null ? 0 : couponUse.DiscountMoney;
                    });
                }
            }

            if (car != null && payResult != null)
            {
                if (payResult.payed == 1 && payResult.chuzhiamount > 0 && car.Car_Balance > 0)
                {
                    car.Car_Balance = car.Car_Balance - payResult.chuzhiamount;
                    if (car.Car_Balance < 0) car.Car_Balance = 0;
                }
            }

            return BLL.OrderDetail.UpdateByList(new List<Model.ParkOrder>() { po }, odList, payColl?.payOrderList, couponPayList, new List<Model.Car>() { car }, null, payColl?.payPartList, hoursToals: hoursTotals);
        }

        /// <summary>
        /// 支付后更新订单
        /// </summary>
        /// <returns></returns>
        public static bool PaySuccess(Model.ParkOrder parkOrder, Model.PayColl payColl, List<Model.CouponRecord> couponPayList, Model.Car car, Model.Owner owner = null, List<Model.OrderDetail> odList = null, Model.ControlEvent cev = null
            , bool isUpdateIncar = true, bool createLedger = false, List<Model.HoursTotal> hoursTotals = null, List<ChargeModels.PayResult> payResults = null)
        {
            return PaySuccess2(parkOrder == null ? null : new List<Model.ParkOrder>() { parkOrder }, payColl, couponPayList, car, owner, odList, cev != null ? new List<Model.ControlEvent>() { cev } : null, isUpdateIncar, createLedger, hoursTotals, payResults);
        }

        public static bool PaySuccess2(List<Model.ParkOrder> parkOrderList, Model.PayColl payColl, List<Model.CouponRecord> couponPayList, Model.Car car, Model.Owner owner = null, List<Model.OrderDetail> odList = null, List<Model.ControlEvent> cevList = null, bool isUpdateIncar = true, bool createLedger = false, List<Model.HoursTotal> hoursTotals = null, List<ChargeModels.PayResult> payResults = null)
        {

            if (parkOrderList == null) return false;

            //【每月最高限额】
            var carfees = CreateCarFees(parkOrderList, payColl, payResults, cevList);

            parkOrderList.ForEach(parkOrder =>
            {
                if (!string.IsNullOrEmpty(parkOrder.ParkOrder_No))
                {
                    if (parkOrder.ParkOrder_OutTime != null)
                    {
                        BLL.UnpaidRecord.UpdateStatus(parkOrder.ParkOrder_OutTime, parkOrder.ParkOrder_No, parkOrder.ParkOrder_CarNo, parkOrder.ParkOrder_IsNoInRecord == 1);
                    }
                }
            });

            //停车明细
            if (odList == null || odList.Count == 0)
            {
                odList = new List<Model.OrderDetail>();
                parkOrderList.ForEach(parkOrder =>
                {
                    odList.AddRange(BLL.OrderDetail.GetAllEntity(parkOrder.ParkOrder_No));
                });
            }
            odList.ForEach(detail =>
            {
                detail.OrderDetail_IsSettle = 1;//结算状态
            });

            if (owner != null)
            {
                if (!string.IsNullOrWhiteSpace(owner.Owner_Phone) && owner.Owner_Phone?.Length > 4)
                    owner.Owner_PhoneLastFour = owner.Owner_Phone.Substring(owner.Owner_Phone.Length - 4);
            }

            List<Model.Ledger> leadgerList = null;
            //true-生成储值车储值余额明细
            if (createLedger && car != null && owner != null)
            {
                Model.Owner owner1 = AppBasicCache.GetElement(AppBasicCache.GetOwner, car.Car_OwnerNo); ;
                if (owner1 != null && owner1.Owner_Balance != owner.Owner_Balance)
                {
                    Model.Ledger ledger = null;
                    var remainingMoney = Utils.ObjectToDecimal(owner1.Owner_Balance, 0) - owner.Owner_Balance;
                    if (remainingMoney > 0)
                    {
                        ledger = new Model.Ledger()
                        {
                            Ledger_CarNo = car.Car_CarNo,
                            Ledger_Space = owner.Owner_Space,
                            Ledger_Type = 2,
                            Ledger_CardType = Common.CarTypeHelper.GetCarTypeIndex(car.Car_Category),
                            Ledger_Code = 2,
                            Ledger_Money = remainingMoney,
                            Ledger_BeforeMoeny = owner1.Owner_Balance,
                            Ledger_AfterMoeny = owner.Owner_Balance,
                            Ledger_Time = DateTime.Now,
                            Ledger_ParkOrderNo = parkOrderList?.FirstOrDefault()?.ParkOrder_No,
                        };
                    }
                    if (ledger != null)
                    {
                        leadgerList = new List<Ledger> { ledger };
                    }
                }
            }

            return BLL.OrderDetail.UpdateByList2(parkOrderList, odList, payColl?.payOrderList, couponPayList, new List<Model.Car>() { car }, new List<Model.Owner>() { owner }, payColl?.payPartList, null, null, payColl?.penaltyList, cevList, isUpdateIncar, leadgerList, hoursTotals, carfees.Item1, carfees.Item2);
        }

        /// <summary>
        /// 创建支付明细
        /// </summary>
        /// <param name="payModel">支付订单</param>
        /// <param name="Type">0：有计费结果，1：充值类支付，2：其它</param>
        /// <param name="payResult">计费结果</param>
        /// <param name="parkOrder"></param>
        /// <param name="lgAdmin"></param>
        /// <param name="noeArray"></param>
        /// <param name="PayPart_PasswayNo"></param>
        /// <param name="remark">备注</param>
        /// <returns></returns>
        public static List<Model.PayPart> CreatePayPartList(DateTime? outTime, Model.PayOrder payModel, int Type = 0, ChargeModels.PayResult payResult = null, Model.ParkOrder parkOrder = null, Model.Admins lgAdmin = null, List<string> noeArray = null, string PayPart_PasswayNo = null,
            List<Model.OrderDetail> orderDetailList = null, string parkAreaNo = null, string parkAreaName = null, string remark = null, bool appendUnpaidOrder = false)
        {
            List<Model.CouponRecordUse> cuModel = null;
            decimal? payedMoeny = null;

            if (payResult != null) payedMoeny = payResult.payedamount + (payResult.cashrobotamount - payResult.zlamount);
            if (payResult != null && payResult.uselist != null) cuModel = TyziTools.Json.ToModel<List<Model.CouponRecordUse>>(TyziTools.Json.ToString(payResult.uselist));

            var list = payResult?.list != null ? DeepCloner.Clone(payResult.list) : null;

            return CreatePayPartSubList(outTime, payModel, Type, parkAreaNo, parkAreaName, list, cuModel, parkOrder, lgAdmin, noeArray, PayPart_PasswayNo,
                orderDetailList, payResult?.chuzhiamount, payResult?.orderamount, payedMoeny, remark, appendUnpaidOrder: appendUnpaidOrder);
        }

        /// <summary>
        /// 创建支付明细
        /// </summary>
        /// <param name="payModel">支付订单</param>
        /// <param name="Type">0：有计费结果，1：充值类支付，2：其它</param>
        /// <param name="parkOrder"></param>
        /// <param name="lgAdmin"></param>
        /// <param name="noeArray"></param>
        /// <param name="PayPart_PasswayNo"></param>
        /// <param name="remark">备注</param>
        /// <returns></returns>
        public static List<Model.PayPart> CreatePayPartSubList(DateTime? outTime, Model.PayOrder payModel, int Type = 0, string parkAreaNo = null, string parkAreaName = null,
            List<ChargeModels.PayDetail> list = null, List<Model.CouponRecordUse> uselist = null, Model.ParkOrder parkOrder = null, Model.Admins lgAdmin = null, List<string> noeArray = null,
            string PayPart_PasswayNo = null, List<Model.OrderDetail> orderDetailList = null, decimal? chuzhiamount = null, decimal? orderMoney = null, decimal? payedMoney = null, string remark = null, bool appendUnpaidOrder = false)
        {
            List<Model.PayPart> payPartList = null;
            chuzhiamount = Utils.ObjectToDecimal(chuzhiamount, 0);
            var newChuzhiamount = chuzhiamount;
            if (payModel != null)
            {
                payModel.PayOrder_DiscountMoney = payModel.PayOrder_DiscountMoney ?? 0;
                payModel.PayOrder_Money = payModel.PayOrder_Money ?? 0;
                payModel.PayOrder_PayedMoney = payModel.PayOrder_PayedMoney ?? 0;
                payModel.PayOrder_StoredMoney = payModel.PayOrder_StoredMoney ?? 0;
                payModel.PayOrder_SelfMoney = payModel.PayOrder_SelfMoney ?? 0;
                payModel.PayOrder_OutReduceMoney = payModel.PayOrder_OutReduceMoney ?? 0;

                if ((chuzhiamount == null || chuzhiamount == 0) && payModel.PayOrder_StoredMoney > 0) { chuzhiamount = payModel.PayOrder_StoredMoney; }

                payPartList = new List<Model.PayPart>();
                Model.PayPart paypart = new Model.PayPart();
                int count = list == null ? 8 : list.Count * 4;
                if (noeArray == null) noeArray = Utils.GetRandomLst(count);
                string no = noeArray.Last();
                noeArray.Remove(no);
                paypart.PayPart_No = CreatePayPartNo(no, payModel.PayOrder_CarNo);
                paypart.PayPart_AddID = lgAdmin == null ? payModel.PayOrder_AdminID : lgAdmin.Admins_ID;
                paypart.PayPart_AddTime = DateTime.Now;
                paypart.PayPart_OrderTypeNo = payModel.PayOrder_OrderTypeNo;
                paypart.PayPart_ParkKey = payModel.PayOrder_ParkKey;
                paypart.PayPart_ParkNo = payModel.PayOrder_ParkNo;
                paypart.PayPart_Status = payModel.PayOrder_Status;
                paypart.PayPart_PasswayNo = PayPart_PasswayNo;
                paypart.PayPart_PayOrderNo = payModel.PayOrder_No;
                paypart.PayPart_PayType = payModel.PayOrder_PayType;
                paypart.PayPart_PayTypeCode = payModel.PayOrder_PayTypeCode;
                paypart.PayPart_OrderMoney = payModel.PayOrder_Money;
                paypart.PayPart_CouponMoney = payModel.PayOrder_DiscountMoney;
                paypart.PayPart_PayedMoney = payModel.PayOrder_PayedMoney;
                paypart.PayPart_BeginTime = payModel.PayOrder_EnterTime;
                paypart.PayPart_EndTime = payModel.PayOrder_Time;
                paypart.PayPart_TimeCount = payModel.PayOrder_TempTimeCount;
                paypart.PayPart_Category = payModel.PayOrder_Category;
                paypart.PayPart_CarCardTypeNo = payModel.PayOrder_CarCardTypeNo;
                paypart.PayPart_CarTypeNo = payModel.PayOrder_CarTypeNo;
                if (parkOrder != null && string.IsNullOrEmpty(paypart.PayPart_CarCardTypeName))
                {
                    paypart.PayPart_CarCardTypeName = parkOrder?.ParkOrder_CarCardTypeName;
                    paypart.PayPart_CarTypeName = parkOrder?.ParkOrder_CarTypeName;
                }
                paypart.PayPart_CarNo = payModel.PayOrder_CarNo;
                paypart.PayPart_ParkOrderNo = payModel.PayOrder_ParkOrderNo;
                paypart.PayPart_EditTime = payModel.PayOrder_PayedTime;
                paypart.PayPart_ParkAreaNo = string.IsNullOrWhiteSpace(parkAreaNo) ? payModel.PayOrder_ParkAreaNo : parkAreaNo;
                paypart.PayPart_CarCardTypeNo = payModel.PayOrder_CarCardTypeNo;
                paypart.PayPart_CarTypeNo = payModel.PayOrder_CarTypeNo;

                paypart.PayPart_ParkAreaName = string.IsNullOrWhiteSpace(parkAreaName) ? parkOrder == null ? BLL.ParkArea.GetEntity(payModel.PayOrder_ParkAreaNo)?.ParkArea_Name : parkOrder.ParkOrder_ParkAreaName : parkAreaName;

                if (string.IsNullOrWhiteSpace(paypart.PayPart_CarCardTypeName))
                {
                    paypart.PayPart_CarCardTypeName = BLL.CarCardType._GetEntityByWhere(new Model.CarCardType(), "*", $" CarCardType_No='{paypart.PayPart_CarCardTypeNo}'")?.CarCardType_Name;
                }
                if (string.IsNullOrWhiteSpace(paypart.PayPart_CarTypeName))
                {
                    paypart.PayPart_CarTypeName = BLL.CarType._GetEntityByWhere(new Model.CarType(), "*", $" CarType_No='{paypart.PayPart_CarTypeNo}'")?.CarType_Name;
                }

                //有计费结果
                if (Type == 0 && parkOrder != null && list != null)
                {
                    if (orderDetailList == null) orderDetailList = BLL.OrderDetail.GetAllEntity(parkOrder.ParkOrder_No);
                    else
                        orderDetailList = TyziTools.Json.ToModel<List<Model.OrderDetail>>(TyziTools.Json.ToString(orderDetailList));
                    orderDetailList = orderDetailList == null ? new List<Model.OrderDetail>() : orderDetailList;
                    int index = 0;
                    decimal? sumRuleCouponMoney = 0;//计费规则运算的优惠金额
                    decimal? sumCouponMoney = 0;//总优惠金额
                    decimal? sumPayedMoneyMoney = 0;//
                    decimal? sumOrderMoney = 0;//


                    var clacOrderAmount = Convert.ToDecimal(ToResultString(list.Sum(x => (Utils.ObjectToDecimal(x.payedamount, 0) + Utils.ObjectToDecimal(x.couponamount, 0))), 2));
                    var clacPayedAmount = Convert.ToDecimal(ToResultString(list.Sum(x => Utils.ObjectToDecimal(x.payedamount, 0)), 2));

                    foreach (var x in list)
                    {
                        if (index > 0)
                        {
                            no = noeArray.Last();
                            noeArray.Remove(no);

                        }
                        var orderDetail = orderDetailList.Find(y => y.OrderDetail_No == x.orderdetailno);
                        orderDetail = orderDetail == null ? new Model.OrderDetail() : orderDetail;
                        orderDetail.OrderDetail_EnterTime = orderDetail.OrderDetail_EnterTime == null ? DateTime.Now : orderDetail.OrderDetail_EnterTime;
                        orderDetail.OrderDetail_OutTime = orderDetail.OrderDetail_OutTime == null ? (outTime ?? DateTime.Now) : orderDetail.OrderDetail_OutTime;

                        Model.PayPart subPay = TyziTools.Json.ToModel<Model.PayPart>(TyziTools.Json.ToString(paypart));
                        subPay.PayPart_No = CreatePayPartNo(no, payModel.PayOrder_CarNo);
                        subPay.PayPart_BeginTime = x.starttime ?? (orderDetail.OrderDetail_EnterTime); /*orderDetail.OrderDetail_EnterTime;*/
                        subPay.PayPart_EndTime = x.endtime ?? (orderDetail.OrderDetail_OutTime == null ? DateTime.Now : orderDetail.OrderDetail_OutTime); //orderDetail.OrderDetail_OutTime == null ? DateTime.Now : orderDetail.OrderDetail_OutTime;
                        subPay.PayPart_CarCardTypeName = orderDetail.OrderDetail_CarCardTypeName ?? parkOrder?.ParkOrder_CarCardTypeName;
                        subPay.PayPart_CarCardTypeNo = orderDetail.OrderDetail_CarCardType ?? parkOrder?.ParkOrder_CarCardType;
                        subPay.PayPart_CarNo = string.IsNullOrEmpty(orderDetail.OrderDetail_CarNo) ? payModel.PayOrder_CarNo : orderDetail.OrderDetail_CarNo;
                        subPay.PayPart_CarTypeNo = parkOrder?.ParkOrder_CarType ?? orderDetail.OrderDetail_CarType;
                        subPay.PayPart_CarTypeName = parkOrder?.ParkOrder_CarTypeName ?? orderDetail.OrderDetail_CarTypeName;
                        subPay.PayPart_OrderDetailNo = orderDetail.OrderDetail_No;
                        subPay.PayPart_ParkAreaName = string.IsNullOrEmpty(orderDetail.OrderDetail_ParkAreaName) ? paypart.PayPart_ParkAreaName : orderDetail.OrderDetail_ParkAreaName;
                        subPay.PayPart_ParkAreaNo = orderDetail.OrderDetail_ParkAreaNo;
                        subPay.PayPart_StoredMoney = subPay.PayPart_StoredMoney ?? 0;

                        //double totalTime = Math.Floor((orderDetail.OrderDetail_OutTime - orderDetail.OrderDetail_EnterTime).Value.TotalSeconds / 60);
                        //subPay.PayPart_TimeCount = carparking.Common.Utils.ObjectToInt(totalTime, 0);

                        double totalTime = Math.Floor((subPay.PayPart_EndTime - subPay.PayPart_BeginTime).Value.TotalSeconds / 60);
                        subPay.PayPart_TimeCount = carparking.Common.Utils.ObjectToInt(totalTime, 0);

                        x.couponamount = Utils.ObjectToDecimal(x.couponamount, 0);
                        subPay.PayPart_PayedMoney = x.payedamount;

                        //储值金额处理（用储值金额抵消实收金额）
                        if (chuzhiamount > 0 && subPay.PayPart_PayedMoney > 0)
                        {
                            if (chuzhiamount > subPay.PayPart_PayedMoney)
                            {
                                chuzhiamount -= subPay.PayPart_PayedMoney;
                                subPay.PayPart_StoredMoney = subPay.PayPart_PayedMoney;
                                subPay.PayPart_PayedMoney = 0;
                                x.payedamount = 0;

                            }
                            else
                            {
                                subPay.PayPart_StoredMoney = chuzhiamount;
                                subPay.PayPart_PayedMoney -= chuzhiamount;
                                x.payedamount -= chuzhiamount;
                                chuzhiamount = 0;
                            }
                        }
                        subPay.PayPart_OrderMoney = x.payedamount + x.couponamount + subPay.PayPart_StoredMoney;

                        //判断应付总金额
                        if (clacOrderAmount != orderMoney)//计费时的总金额 不等于 支付时的订单金额
                        {
                            if (orderMoney != null && sumOrderMoney >= orderMoney)
                            {
                                subPay.PayPart_OrderMoney = 0;
                            }
                            else
                            {
                                if (sumOrderMoney + subPay.PayPart_OrderMoney > orderMoney)
                                {
                                    var currentPayMoeny = sumOrderMoney + subPay.PayPart_OrderMoney - orderMoney;
                                    subPay.PayPart_OrderMoney = subPay.PayPart_OrderMoney - currentPayMoeny;
                                    if (subPay.PayPart_PayedMoney >= currentPayMoeny)
                                    {
                                        subPay.PayPart_PayedMoney -= currentPayMoeny;
                                        x.payedamount -= currentPayMoeny;
                                    }
                                }
                            }
                        }

                        //判断实付总金额
                        string pDesc = null;
                        if (clacPayedAmount != payedMoney)//计费时的需要支付金额 不等于 支付时的实际金额
                        {
                            if (subPay.PayPart_PayedMoney > 0 && payedMoney != null)
                            {
                                if (sumPayedMoneyMoney >= payedMoney)
                                {
                                    decimal? couponMoney = x.couponamount;
                                    if ((sumPayedMoneyMoney == 0 && payedMoney == 0 && (x.payedamount + x.couponamount + subPay.PayPart_StoredMoney) > orderMoney) || sumPayedMoneyMoney == payedMoney)
                                    {
                                        if (sumPayedMoneyMoney == 0 && payedMoney == 0 && (x.payedamount + x.couponamount + subPay.PayPart_StoredMoney) > orderMoney)
                                            x.couponamount = orderMoney;
                                        else
                                            x.couponamount = x.payedamount + x.couponamount;
                                    }
                                    else
                                        x.couponamount = subPay.PayPart_PayedMoney;

                                    couponMoney = x.couponamount - couponMoney;
                                    subPay.PayPart_PayedMoney = 0;
                                    if (x.couponamount != 0) pDesc = $" 优惠改价{couponMoney}元";
                                }
                                else
                                {
                                    if (sumPayedMoneyMoney + subPay.PayPart_PayedMoney > payedMoney)
                                    {
                                        var couponMoney = sumPayedMoneyMoney + subPay.PayPart_PayedMoney - payedMoney;
                                        x.couponamount = subPay.PayPart_OrderMoney - (subPay.PayPart_PayedMoney - couponMoney) - subPay.PayPart_StoredMoney;
                                        subPay.PayPart_PayedMoney = subPay.PayPart_PayedMoney - couponMoney;
                                        if (x.couponamount != 0) pDesc = $" 优惠改价{couponMoney}元";
                                    }
                                }
                            }
                        }

                        sumPayedMoneyMoney += subPay.PayPart_PayedMoney;
                        sumRuleCouponMoney += x.couponamount;

                        subPay.PayPart_CouponMoney = x.couponamount;
                        sumCouponMoney += (subPay.PayPart_OrderMoney - subPay.PayPart_PayedMoney);
                        subPay.PayPart_Desc = pDesc;
                        sumOrderMoney += subPay.PayPart_OrderMoney;

                        if (!string.IsNullOrEmpty(remark) && !subPay.PayPart_Desc.Contains(remark)) subPay.PayPart_Desc += (" | " + remark);
                        payPartList.Add(subPay);
                        index++;
                    };

                    var diffCouponMoeny = sumPayedMoneyMoney - payModel.PayOrder_PayedMoney;//计费金额 - 实收金额 = 少收的金额
                    if (diffCouponMoeny != 0)//可能由手动改价或线上优惠导致金额差异
                    {
                        no = noeArray.Last();
                        noeArray.Remove(no);

                        Model.PayPart subPay = TyziTools.Json.ToModel<Model.PayPart>(TyziTools.Json.ToString(paypart));
                        subPay.PayPart_No = CreatePayPartNo(no, payModel.PayOrder_CarNo);

                        subPay.PayPart_OrderMoney = 0;
                        subPay.PayPart_PayedMoney = 0;
                        subPay.PayPart_CouponMoney = diffCouponMoeny;
                        subPay.PayPart_Desc = "优惠改价";

                        subPay.PayPart_PayMode = 1;

                        if (!string.IsNullOrEmpty(remark) && !subPay.PayPart_Desc.Contains(remark)) subPay.PayPart_Desc += (" | " + remark);
                        payPartList.Add(subPay);
                    }
                }
                //1：充值类支付，2：其它
                else if (Type == 1 || Type == 2)
                {
                    decimal? orderMoney2 = paypart.PayPart_OrderMoney - paypart.PayPart_PayedMoney - paypart.PayPart_CouponMoney;
                    if (orderMoney2 != 0)
                    {
                        no = noeArray.Last();
                        noeArray.Remove(no);

                        Model.PayPart subPay = TyziTools.Json.ToModel<Model.PayPart>(TyziTools.Json.ToString(paypart));
                        subPay.PayPart_No = CreatePayPartNo(no, payModel.PayOrder_CarNo); ;
                        subPay.PayPart_OrderMoney = 0;
                        subPay.PayPart_PayedMoney = 0;
                        subPay.PayPart_CouponMoney = orderMoney2;
                        subPay.PayPart_Desc = "优惠改价";
                        subPay.PayPart_PayMode = 1;

                        if (!string.IsNullOrEmpty(remark) && !subPay.PayPart_Desc.Contains(remark)) subPay.PayPart_Desc += (" | " + remark);
                        payPartList.Add(subPay);
                    }

                    paypart.PayPart_No = CreatePayPartNo(no, payModel.PayOrder_CarNo); ;
                    paypart.PayPart_BeginTime = payModel.PayOrder_MonthBeginTime;
                    paypart.PayPart_EndTime = payModel.PayOrder_MonthEndTime;
                    if (paypart.PayPart_Desc == "储值金额抵扣") { paypart.PayPart_PayTypeCode = Convert.ToString((int)Model.EnumPayType.OffLineCash); }

                    var payDesc = "";
                    if (paypart.PayPart_BeginTime != null && paypart.PayPart_EndTime != null)
                    {
                        payDesc = Utils.DateDiff(paypart.PayPart_EndTime.Value, paypart.PayPart_BeginTime.Value);
                    }

                    if (string.IsNullOrWhiteSpace(paypart.PayPart_Desc))
                    {
                        if (!string.IsNullOrEmpty(payModel?.PayOrder_Desc))
                            paypart.PayPart_Desc = payModel.PayOrder_Desc;
                        else
                            paypart.PayPart_Desc = Type == 1 ? "储值金额" : "月租充值" + payDesc;
                    }

                    if (!string.IsNullOrEmpty(remark) && !paypart.PayPart_Desc.Contains(remark)) paypart.PayPart_Desc += (" | " + remark);

                    payPartList.Add(paypart);
                }
                else
                {
                    paypart.PayPart_StoredMoney = chuzhiamount;
                    decimal? orderMoney2 = paypart.PayPart_OrderMoney - paypart.PayPart_PayedMoney - paypart.PayPart_CouponMoney - chuzhiamount;
                    if (orderMoney2 != 0)
                    {
                        no = noeArray.Last();
                        noeArray.Remove(no);

                        Model.PayPart subPay = TyziTools.Json.ToModel<Model.PayPart>(TyziTools.Json.ToString(paypart));
                        subPay.PayPart_No = CreatePayPartNo(no, payModel.PayOrder_CarNo); ;
                        subPay.PayPart_OrderMoney = 0;
                        subPay.PayPart_PayedMoney = 0;
                        subPay.PayPart_CouponMoney = orderMoney2;
                        if (appendUnpaidOrder) subPay.PayPart_Desc = "追缴金额"; else subPay.PayPart_Desc = "优惠改价";
                        subPay.PayPart_PayMode = 1;

                        if (!string.IsNullOrEmpty(remark) && !subPay.PayPart_Desc.Contains(remark)) subPay.PayPart_Desc += (" | " + remark);
                        payPartList.Add(subPay);
                    }

                    paypart.PayPart_No = CreatePayPartNo(no, payModel.PayOrder_CarNo);
                    if (paypart.PayPart_TimeCount == null && paypart.PayPart_BeginTime != null && paypart.PayPart_EndTime != null)
                    {
                        double totalTime = Math.Floor((paypart.PayPart_EndTime - paypart.PayPart_BeginTime).Value.TotalSeconds / 60);
                        paypart.PayPart_TimeCount = carparking.Common.Utils.ObjectToInt(totalTime, 0);
                    }

                    if (!string.IsNullOrEmpty(remark) && !paypart.PayPart_Desc.Contains(remark)) paypart.PayPart_Desc += (" | " + remark);
                    payPartList.Add(paypart);
                }
            }

            return payPartList;
        }

        /// <summary>
        /// 修改计费的周期累计金额（手动改价后，要调整每个计费明细的周期累计金额）
        /// </summary>
        /// <param name="payDetailList">计费明细</param>
        /// <param name="amount">要分摊的金额</param>
        public static List<ChargeModels.PayDetail> ApportionedAmount(List<ChargeModels.PayDetail> payDetailList, decimal? amount)
        {
            if (payDetailList == null || payDetailList.Count == 0 || amount == null || amount == 0) return payDetailList;
            decimal? sumMoney = payDetailList.Where(x => x.payed == 1)?.Sum(x => x.payedamount);
            if (sumMoney == null || sumMoney == 0) return payDetailList;
            if (amount > sumMoney) amount = sumMoney;
            if (amount == sumMoney)
            {
                payDetailList.ForEach(x =>
                {
                    if (x.payed == 1 && x.payedamount > 0)
                    {
                        x.NextCyclePaidFees = Utils.ObjectToDecimal(x.NextCyclePaidFees, 0) - x.payedamount;
                        if (x.NextCyclePaidFees < 0) x.NextCyclePaidFees = 0;
                    }
                });
                return payDetailList;
            }

            decimal? ratio = amount / sumMoney;

            decimal? sumRatioAmount = 0;
            payDetailList.ForEach(x =>
            {
                if (x.payed == 1 && x.payedamount > 0)
                {
                    var ratioAmount = x.payedamount * ratio;
                    sumRatioAmount += ratioAmount;

                    x.NextCyclePaidFees = Utils.ObjectToDecimal(x.NextCyclePaidFees, 0) - ratioAmount;
                    if (x.NextCyclePaidFees < 0) x.NextCyclePaidFees = 0;
                }
            });

            return payDetailList;
        }

        /// <summary>
        /// 修改计费的周期累计金额（手动改价后，要调整每个计费明细的周期累计金额）
        /// </summary>
        /// <param name="payDetailList">计费明细</param>
        /// <param name="payColl">支付订单集合</param>
        /// <param name="payDetailes">支付明细集合</param>
        /// <param name="ParkOrder_No">停车订单号</param>
        /// <returns></returns>
        public static List<ChargeModels.PayDetail> ApportionedAmountByPayPart(List<ChargeModels.PayDetail> payDetailList, Model.PayColl payColl = null, List<Model.PayPart> payDetailes = null, string ParkOrder_No = null)
        {
            if (payDetailList != null)
            {
                if (payDetailes == null && !string.IsNullOrWhiteSpace(ParkOrder_No)) payDetailes = BLL.BaseBLL._GetAllEntity(new Model.PayPart(), "PayPart_No,PayPart_PayedMoney,PayPart_CouponMoney,PayPart_PayMode,PayPart_Status", $"PayPart_ParkOrderNo='{ParkOrder_No}' AND PayPart_Status=1 AND PayPart_PayMode=1");
                payDetailes = payDetailes ?? new List<Model.PayPart>();
                if (payColl != null)
                {
                    payColl.payPartList?.ForEach(payPart =>
                    {
                        if (payPart.PayPart_PayMode == 1)
                        {
                            var item = payDetailes.Where(x => x.PayPart_No == payPart.PayPart_No).FirstOrDefault();
                            if (item == null) payDetailes.Add(payPart);
                        }
                    });
                }

                payDetailList = BLL.CommonBLL.ApportionedAmountByPayPart(payDetailList, payDetailes, ParkOrder_No);
            }
            return payDetailList;
        }

        /// <summary>
        /// 修改计费的周期累计金额（手动改价后，要调整每个计费明细的周期累计金额）
        /// </summary>
        /// <param name="payDetailList">计费明细</param>
        /// <param name="payDetailes">支付明细集合</param>
        /// <param name="ParkOrder_No">停车订单号</param>
        /// <returns></returns>
        public static List<ChargeModels.PayDetail> ApportionedAmountByPayPart(List<ChargeModels.PayDetail> payDetailList, List<Model.PayPart> payDetailes = null, string ParkOrder_No = null)
        {
            if (payDetailList != null)
            {
                if (payDetailes == null && !string.IsNullOrWhiteSpace(ParkOrder_No)) payDetailes = BLL.BaseBLL._GetAllEntity(new Model.PayPart(), "PayPart_No,PayPart_PayedMoney,PayPart_CouponMoney,PayPart_PayMode,PayPart_Status", $"PayPart_ParkOrderNo='{ParkOrder_No}' AND PayPart_Status=1 AND PayPart_PayMode=1");
                if (payDetailes != null)
                {
                    payDetailes = payDetailes.FindAll(x => x.PayPart_PayMode == 1 & x.PayPart_Status == 1);
                    if (payDetailes != null && payDetailes.Count > 0)
                    {
                        //实际支付
                        var handChagePayedMoney = payDetailes.Sum(x => x.PayPart_PayedMoney);
                        if (handChagePayedMoney < 0) handChagePayedMoney = -handChagePayedMoney;
                        //优惠金额
                        var handChageCouponMoney = payDetailes.Sum(x => x.PayPart_CouponMoney);
                        if (handChageCouponMoney < 0) handChageCouponMoney = -handChageCouponMoney;
                        //手动改价的总金额
                        var handChagePrice = handChagePayedMoney + handChageCouponMoney;
                        if (handChagePrice > 0)
                        {
                            payDetailList = BLL.CommonBLL.ApportionedAmount(payDetailList, handChagePrice);
                        }
                    }
                }
            }
            return payDetailList;
        }
        #endregion


        #region 将支付金额按比例平摊给每一个区域生成缴费明细

        /// <summary>
        /// 创建支付明细(将支付金额按比例平摊给每一个区域生成缴费明细)
        /// </summary>
        /// <param name="payModel">支付订单</param>
        /// <param name="Type">0：有计费结果，1：充值类支付，2：其它</param>
        /// <param name="parkOrder"></param>
        /// <param name="lgAdmin"></param>
        /// <param name="noeArray"></param>
        /// <param name="PayPart_PasswayNo"></param>
        /// <returns></returns>
        public static List<Model.PayPart> CreatePayPartListByRatio(List<ChargeModels.PayDetail> list, decimal? payedMoney, List<Model.OrderDetail> orderDetailList,
            Model.PayOrder payModel, Model.ParkOrder po, Model.Admins lgAdmin = null, bool appendUnpaidOrder = false)
        {
            List<Model.PayPart> payPartList = new List<Model.PayPart>();
            if (list != null && list.Count > 1 && payedMoney > 0 && orderDetailList != null && orderDetailList.Count > 0)
            {
                var totalMoney = list.Sum(x => x.payedamount);//计费结果的总实付

                decimal? ratio = (totalMoney == 0) ? 0 : (payedMoney / totalMoney);//扫码缴费金额/总实付 = 比例

                float? sumRatioAmount = 0;

                int count = 0;
                List<string> noList = Utils.GetRandomLst(5 + list.Count * 3);
                foreach (var payDetail in list)
                {
                    count++;
                    float? ratioAmount = Utils.ObjectToFloat(payDetail.payedamount * ratio, 0);//按比例后的金额
                    if (count == list.Count) { ratioAmount = Utils.ObjectToFloat(payedMoney, 0) - sumRatioAmount; if (ratioAmount < 0) ratioAmount = 0; }
                    sumRatioAmount += Utils.ObjectToFloat(Utils.ObjectToDecimal(ratioAmount, 0));

                    var orderDetail = orderDetailList.Find(x => x.OrderDetail_No == payDetail.orderdetailno);
                    Model.PayPart paypart = new Model.PayPart();
                    paypart.PayPart_No = CreatePayPartNo(noList.Last(), orderDetail?.OrderDetail_CarNo);
                    noList.Remove(noList.Last());
                    paypart.PayPart_AddID = lgAdmin == null ? payModel.PayOrder_AdminID : lgAdmin.Admins_ID;
                    paypart.PayPart_AddTime = DateTime.Now;
                    paypart.PayPart_OrderTypeNo = payModel.PayOrder_OrderTypeNo;
                    paypart.PayPart_ParkKey = payModel.PayOrder_ParkKey;
                    paypart.PayPart_ParkNo = payModel.PayOrder_ParkNo;
                    paypart.PayPart_Status = payModel.PayOrder_Status;
                    paypart.PayPart_PasswayNo = payModel.PayOrder_PassWayNo;
                    paypart.PayPart_PayOrderNo = payModel.PayOrder_No;
                    paypart.PayPart_PayType = payModel.PayOrder_PayType;
                    paypart.PayPart_PayTypeCode = payModel.PayOrder_PayTypeCode;
                    paypart.PayPart_OrderMoney = payModel.PayOrder_Money;
                    paypart.PayPart_CouponMoney = payModel.PayOrder_DiscountMoney;
                    paypart.PayPart_PayedMoney = payModel.PayOrder_PayedMoney;
                    paypart.PayPart_BeginTime = payModel.PayOrder_EnterTime;
                    paypart.PayPart_EndTime = payModel.PayOrder_Time;
                    paypart.PayPart_Category = payModel.PayOrder_Category;
                    paypart.PayPart_CarCardTypeNo = payModel.PayOrder_CarCardTypeNo;
                    paypart.PayPart_CarTypeNo = payModel.PayOrder_CarTypeNo;
                    if (po != null && string.IsNullOrEmpty(paypart.PayPart_CarCardTypeName))
                    {
                        paypart.PayPart_CarCardTypeName = po?.ParkOrder_CarCardTypeName;
                        paypart.PayPart_CarTypeName = po?.ParkOrder_CarTypeName;
                    }
                    paypart.PayPart_CarNo = payModel.PayOrder_CarNo;
                    paypart.PayPart_ParkOrderNo = payModel.PayOrder_ParkOrderNo;
                    paypart.PayPart_EditTime = payModel.PayOrder_PayedTime;
                    paypart.PayPart_ParkAreaNo = payDetail.areano;
                    paypart.PayPart_ParkAreaName = payDetail.areaname;
                    paypart.PayPart_TimeCount = payModel.PayOrder_TempTimeCount;
                    if (orderDetail != null)
                    {
                        var outTime = po.ParkOrder_OutTime == null ? payModel.PayOrder_PayedTime : po.ParkOrder_OutTime;
                        orderDetail.OrderDetail_OutTime = orderDetail.OrderDetail_OutTime == null ? (outTime ?? DateTime.Now) : orderDetail.OrderDetail_OutTime;
                        double totalTime = Math.Floor((orderDetail.OrderDetail_OutTime - orderDetail.OrderDetail_EnterTime).Value.TotalSeconds / 60);
                        paypart.PayPart_TimeCount = carparking.Common.Utils.ObjectToInt(totalTime, 0);
                    }

                    var money = Utils.ObjectToDecimal(ratioAmount, 0);
                    if (money > payDetail.payedamount) money = payDetail.payedamount.Value;

                    paypart.PayPart_OrderMoney = money;
                    paypart.PayPart_PayedMoney = Utils.ObjectToDecimal(ratioAmount, 0);
                    paypart.PayPart_CouponMoney = 0;
                    paypart.PayPart_PayMode = 0;
                    payPartList.Add(paypart);

                    if (sumRatioAmount >= Utils.ObjectToFloat(payedMoney, 0)) break;
                }
            }
            else
            {
                return BLL.CommonBLL.CreatePayPartList(po?.ParkOrder_OutTime == null ? payModel.PayOrder_PayedTime.Value : po?.ParkOrder_OutTime, payModel, 0, null, po, null, null, null, orderDetailList, appendUnpaidOrder: appendUnpaidOrder);
            }
            return payPartList;
        }

        #endregion


        #region 场内清缴

        public static (Model.ParkOrder, Model.PayColl, List<Model.CouponRecord>, Model.Car, Model.Owner, List<Model.OrderDetail>) InParkToPay(Model.Parking parking, ref Model.ParkOrder po, decimal PayOrder_Money, decimal PayOrder_PayedMoney, decimal chuzhiMoney, string remark,
            Model.AdminSession lgAdmins)
        {
            string paytypNo = Model.EnumPayType.OffLineCash.ToString();//支付类型

            //查询车辆信息、车牌类型、车牌颜色
            Model.CarCardType cct = null;
            Model.Owner owner = null;
            List<Model.CouponRecord> couponList = null;
            List<Model.HoursTotal> hoursTotals = null;

            List<Model.OrderDetail> orderDetailList = BLL.OrderDetail.GetAllEntity(po.ParkOrder_No);
            Model.Car car = BLL.Car.GetEntityByCarNo(po.ParkOrder_CarNo);
            if (car != null)
            {
                cct = BLL.CarCardType.GetEntity(car.Car_TypeNo);
                owner = BLL.Owner.GetEntity(car.Car_OwnerNo);
            }

            List<string> noList = Utils.GetRandomLst(2);
            PayOrder_Money = PayOrder_Money - chuzhiMoney;

            ChargeModels.PayResult payResult = BLL.ConfirmRelease.GetComfirmOrderByOrderNo(po.ParkOrder_No)?.payres;

            //支付订单
            Model.PayColl paycOLL = null;
            List<Model.PayOrder> payOrderList = new List<Model.PayOrder>();
            List<Model.PayPart> payPartList = new List<Model.PayPart>();

            if (payResult == null)
            {
                Model.PayOrder payModel = new Model.PayOrder();
                payModel.PayOrder_ParkOrderNo = po.ParkOrder_No;
                payModel.PayOrder_Time = DateTime.Now;
                payModel.PayOrder_CarNo = po.ParkOrder_CarNo;
                payModel.PayOrder_Status = 0;
                payModel.PayOrder_PayedTime = null;
                payModel.PayOrder_PayTypeCode = paytypNo;
                payModel.PayOrder_PayType = 0;
                payModel.PayOrder_CouponRecordNo = "";
                payModel.PayOrder_UserNo = "";
                payModel.PayOrder_ParkKey = parking.Parking_Key;
                payModel.PayOrder_ParkNo = parking.Parking_No;
                payModel.PayOrder_OrderTypeNo = cct == null ? Convert.ToString((int)Common.EnumOrderType.Temp) : CarTypeHelper.GetOrderType(cct.CarCardType_Category, true);
                payModel.PayOrder_EnterTime = po.ParkOrder_EnterTime;
                payModel.PayOrder_TempTimeCount = Convert.ToInt32((DateTime.Now - po.ParkOrder_EnterTime).Value.TotalMinutes);
                payModel.PayOrder_TimeCountDesc = Utils.DateDiffStr(payModel.PayOrder_TempTimeCount.Value * 60);
                payModel.PayOrder_CarCardTypeNo = po.ParkOrder_CarCardType;
                payModel.PayOrder_CarTypeNo = po.ParkOrder_CarType;
                payModel.PayOrder_DiscountMoney = 0;
                payModel.PayOrder_ParkAreaNo = po.ParkOrder_ParkAreaNo;
                payModel.PayOrder_PayedTime = DateTime.Now;
                payModel.PayOrder_Status = 1;//已支付
                payModel.PayOrder_Category = cct != null ? cct.CarCardType_Category : "";
                payModel.PayOrder_OperatorName = lgAdmins?.Admins_Name;
                payModel.PayOrder_Account = lgAdmins?.Admins_Account;
                payModel.PayOrder_AdminID = lgAdmins?.Admins_ID;
                payModel.PayOrder_Desc = remark;

                if (chuzhiMoney > 0)
                {
                    payModel.PayOrder_No = "PO" + noList[0] + parking.Parking_Key;
                    payModel.PayOrder_Money = chuzhiMoney;
                    payModel.PayOrder_PayedMoney = chuzhiMoney;
                    payModel.PayOrder_DiscountMoney = null;
                    payModel = BLL.PayOrder.CreatePayOrder(true, payModel, parking.Parking_Key, null, lgAdmins, null, null, null, null, null, null, null, null, null, null, null, null, null, remark);
                    payOrderList.Add(payModel);
                    payPartList.AddRange(BLL.CommonBLL.CreatePayPartList(null, payModel));
                }

                //手动改价后，支付总金额不等于 实收金额+优惠金额
                payModel.PayOrder_No = "PO" + noList[1] + parking.Parking_Key;
                payModel.PayOrder_Money = PayOrder_Money;
                payModel.PayOrder_PayedMoney = PayOrder_PayedMoney;
                payModel.PayOrder_DiscountMoney = 0;
                payModel = BLL.PayOrder.CreatePayOrder(true, payModel, parking.Parking_Key, null, lgAdmins, null, null, null, null, null, null, null, null, null, null, null, null, null, remark);
                //手动改价后，生成手动改价明细
                payPartList.AddRange(BLL.CommonBLL.CreatePayPartList(DateTime.Now, payModel, 0, payResult, po, lgAdmins, null, null, orderDetailList));
                //生成手动改价明细后，重新调整优惠金额
                payModel.PayOrder_DiscountMoney = payModel.PayOrder_Money - payModel.PayOrder_PayedMoney;
                payOrderList.Add(payModel);
            }
            else
            {
                payResult.payedamount = PayOrder_PayedMoney;
                paycOLL = BLL.CommonBLL.AddPayOrder(DateTime.Now, payResult, po.ParkOrder_No, lgAdmins, "", paytypNo, po.ParkOrder_ParkAreaNo, 2, DateTime.Now, 1, (PayOrder_PayedMoney > 0 ? "" : remark), false);
                if (paycOLL != null)
                {
                    if (paycOLL.payOrderList != null && paycOLL.payOrderList.Count > 0) paycOLL.payOrderList[0].PayOrder_Desc = remark;
                    if (paycOLL.payPartList != null) payPartList.AddRange(paycOLL.payPartList);
                    payOrderList = paycOLL.payOrderList;
                    if (payOrderList.Count > 1 && payOrderList[1].PayOrder_Money == payResult.chuzhiamount)
                    {
                        payOrderList[1].PayOrder_PayTypeCode = Model.EnumPayType.OffLineCash.ToString();
                    }
                }
            }

            //周期处理
            if (payResult != null && payResult.list != null && orderDetailList != null && orderDetailList.Count > 0)
            {
                payResult.list = BLL.CommonBLL.ApportionedAmountByPayPart(payResult.list, payPartList, po.ParkOrder_No);
                orderDetailList.ForEach(item =>
                {
                    var pdItem = payResult.list.Where(x => x.orderdetailno == item.OrderDetail_No).FirstOrDefault();
                    if (pdItem != null && !string.IsNullOrWhiteSpace(pdItem.orderdetailno))
                    {
                        item.OrderDetail_NextCycleTime = pdItem.nextcycletime;//下一次周期生效时间
                        item.Orderdetail_CycleMoney = pdItem.NextCyclePaidFees;//下一次停车，周期已累积支付总金额
                        item.Orderdetail_CycleFreeMin = pdItem.nextcyclefreemin;//下一次停车，周期已累积免费分钟
                                                                                //item.Orderdetail_UseFreeMin = pdItem.;//下一次停车，周期已累积免费分钟
                                                                                //item.orderdetail_HoursBeginTime = pdItem.nexthourstime;
                                                                                //item.orderdetail_HoursContent = pdItem.nexthourscontent;
                        if (!string.IsNullOrEmpty(pdItem.nexthourscontent) && pdItem.nexthourstime != null)
                        {
                            hoursTotals = hoursTotals ?? new List<Model.HoursTotal>();
                            hoursTotals.Add(new Model.HoursTotal()
                            {
                                HoursTotal_No = item.OrderDetail_No,
                                HoursTotal_CarNo = item.OrderDetail_CarNo,
                                HoursTotal_ParkOrderNo = item.OrderDetail_ParkOrderNo,
                                HoursTotal_CarType = item.OrderDetail_CarType,
                                HoursTotal_CarCardType = item.OrderDetail_CarCardType,
                                HoursTotal_ParkAreaNo = item.OrderDetail_ParkAreaNo,
                                HoursTotal_EnterTime = item.OrderDetail_EnterTime,
                                HoursTotal_PayTime = DateTime.Now,
                                HoursTotal_BeginTime = pdItem.nexthourstime,
                                HoursTotal_Content = pdItem.nexthourscontent,
                            });
                        }

                        item.Orderdetail_UseFreeMin = Utils.ObjectToInt(item.Orderdetail_UseFreeMin, 0) + Utils.ObjectToInt(pdItem.currentfreemin, 0);
                        item.OrderDetail_IsSettle = 1;//结算状态
                        if (pdItem.calcbegintime != null)
                        {
                            item.OrderDetail_CurrCalcTime = pdItem.calcbegintime;//当前计费开始时间
                        }
                        item.OrderDetail_TotalAmount = pdItem.payedamount;//出场总金额
                    }
                });
            }

            //车辆储值金额处理
            if (chuzhiMoney > 0)
            {
                if (owner != null)
                {
                    owner.Owner_Balance = Utils.ObjectToDecimal(owner.Owner_Balance, 0) - chuzhiMoney;
                    if (owner.Owner_Balance < 0) owner.Owner_Balance = 0;
                }
            }

            if (po.ParkOrder_TotalAmount == null) po.ParkOrder_TotalAmount = payOrderList.Sum(x => x.PayOrder_Money); else po.ParkOrder_TotalAmount += payOrderList.Sum(x => x.PayOrder_Money);
            if (po.ParkOrder_TotalPayed == null) po.ParkOrder_TotalPayed = payOrderList.Sum(x => x.PayOrder_PayedMoney); else po.ParkOrder_TotalPayed += payOrderList.Sum(x => x.PayOrder_PayedMoney);

            Model.PayColl payColl = new Model.PayColl() { payOrderList = payOrderList, payPartList = payPartList };

            bool isFollow = false;
            if (po.ParkOrder_StatusNo == Model.EnumParkOrderStatus.Follow)
            {
                isFollow = true;
                po.ParkOrder_StatusNo = Model.EnumParkOrderStatus.Out;
            }

            po.ParkOrder_PayScene = (int)Model.EnumPayScene.Centre;

            //充电滞留数据更新为已支付

            if (payResult != null && payResult.penaltylist != null && payResult.penaltyamount > 0)
            {
                List<Model.DetentionPenalty> penaltyList = TyziTools.Json.ToObject<List<Model.DetentionPenalty>>(TyziTools.Json.ToString(payResult.penaltylist));
                var parkorderno = po.ParkOrder_No;
                penaltyList.ForEach(item => { item.DetentionPenalty_PayStatus = 1; item.DetentionPenalty_ParkOrderNo = parkorderno; });
                payColl.penaltyList = penaltyList;
            }

            //更新数据
            var result = BLL.CommonBLL.PaySuccess(po, payColl, couponList, car, owner, orderDetailList, hoursTotals: hoursTotals, payResults: new List<PayResult>() { payResult });
            if (result)
            {
                try
                {
                    if (isFollow || po?.ParkOrder_StatusNo == Model.EnumParkOrderStatus.Close)
                    {
                        var cev = BLL.ControlEvent.GetEntityByOrderNo(po.ParkOrder_No);
                        if (cev != null)
                        {
                            //上报已追缴事件到云平台
                            BLL.PushEvent.CarFollowUpdate(
                                parking.Parking_Key
                                , cev.ControlEvent_No
                                , cev.ControlEvent_CarNo
                                , cev.ControlEvent_ParkOrderNo
                                , cev.ControlEvent_Time.Value.ToString("yyyy-MM-dd HH:mm:ss")
                                , cev.ControlEvent_OkTime.Value.ToString("yyyy-MM-dd HH:mm:ss")
                                , "2"
                                , cev.ControlEvent_AdminName ?? ""
                                , cev.ControlEvent_BigImg ?? ""
                                , cev.ControlEvent_Video ?? ""
                                , cev.ControlEvent_Remark
                                , cev.ControlEvent_Money?.ToString() ?? "0",
                                1
                            );

                            cev.ControlEvent_Status = 4;
                            BLL.ControlEvent._UpdateByModelByNo(cev);
                        }


                    }
                }
                catch (Exception e)
                {
                    BLL.SystemLogs.AddLog(lgAdmins, LogEnum.Backend, "停车记录", "平台支付后，上报已追缴事件发生异常:" + e.ToString());
                }

                return (po, payColl, couponList, car, owner, orderDetailList);
            }

            return default(dynamic);
        }

        #endregion



        /// <summary>
        /// decimal保留指定位数小数
        /// </summary>
        /// <param name="num">原始数量</param>
        /// <param name="scale">保留小数位数</param>
        /// <returns>截取指定小数位数后的数量字符串</returns>
        public static string ToResultString(decimal num, int scale)
        {
            string numToString = num.ToString();

            int index = numToString.IndexOf(".");
            int length = numToString.Length;

            if (index != -1)
            {
                return string.Format("{0}.{1}",
                    numToString.Substring(0, index),
                    numToString.Substring(index + 1, Math.Min(length - index - 1, scale)));
            }
            else
            {
                return num.ToString();
            }
        }

        /// <summary>
        /// 保存历史报表
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="no"></param>
        /// <param name="filePath"></param>
        /// <param name="start"></param>
        /// <param name="end"></param>
        /// <param name="data"></param>
        /// <param name="groupType">统计方式:0-操作员,1-区域,2-操作员+区域</param>
        public static void SaveHisReport<T>(string no, string filePath, DateTime start, DateTime end, T data, int type, int dataType = 0, int groupType = 0)
        {
            //创建历史报表
            Model.HistoryReport historyReport = new Model.HistoryReport();
            historyReport.HistoryReport_No = no;
            historyReport.HistoryReport_FilePath = Uri.EscapeDataString(filePath);
            historyReport.HistoryReport_AddTime = DateTimeHelper.GetNowTime();
            historyReport.HistoryReport_Type = dataType == 1 ? type + 1000 : type; ;
            historyReport.HistoryReport_BeginTime = start;
            historyReport.HistoryReport_EndTime = end;
            historyReport.HistoryReport_GroupType = groupType;

            //保存到文件
            FileHelper.SaveFile(filePath, data);

            //保存到数据库
            BLL.BaseBLL._Insert(historyReport);
        }

        /// <summary>
        /// 配置文件备份与还原
        /// </summary>
        /// <param name="configPath"></param>
        /// <param name="backupPath"></param>
        public static void CopyManualSentryBoxConfig(string configPath, string backupPath)
        {
            try
            {
                if (File.Exists(configPath))
                {
                    //判断文件内容是否为空，如果为空则读取备份文件内容
                    var sc = File.ReadAllText(configPath);
                    if (string.IsNullOrWhiteSpace(sc))
                    {
                        if (File.Exists(backupPath)) File.Copy(backupPath, configPath, true);
                    }
                    else
                    {
                        if (AppBasicCache.IsWindows) File.Copy(configPath, backupPath, true);
                    }
                }
                else if (File.Exists(backupPath))
                {
                    File.Copy(backupPath, configPath, true);
                }
            }
            catch (Exception e)
            {
                LogManagementMap.WriteToFile(LoggerEnum.MainErrorLog, $"配置文件复制失败：" + e.ToString(), LogLevel.Info);
            }
        }

        /// <summary>
        /// 更新白名单记录
        /// </summary>
        /// <param name="status"></param>
        /// <param name="carno"></param>
        /// <param name="deviceno"></param>
        /// <param name="remark"></param>
        public static bool UpdateWhiteStatus(string status, string carno, string deviceno, string remark, bool onlyUpdateStatus = false)
        {
            try
            {
                Model.WhiteRecord whiteRecord = new Model.WhiteRecord();

                switch (status)
                {
                    case "1": //添加成功
                        whiteRecord.WhiteRecord_Status = 1;
                        whiteRecord.WhiteRecord_DeviceStatus = 1;
                        break;
                    case "2": //添加失败
                        whiteRecord = BLL.BaseBLL._GetEntityByWhere(new Model.WhiteRecord(), "*", $"WhiteRecord_CarNo='{carno}' and WhiteRecord_DeviceNo='{deviceno}'");
                        if (whiteRecord != null)
                        {
                            if (whiteRecord.WhiteRecord_DeviceStatus != 1) whiteRecord.WhiteRecord_Status = 2;
                        }
                        else
                        {
                            whiteRecord = new Model.WhiteRecord();
                            whiteRecord.WhiteRecord_Status = 2;
                        }

                        break;
                    case "4": //删除失败
                    case "6": //清空失败
                        whiteRecord.WhiteRecord_Status = Utils.ObjectToInt(status, 0);
                        if (onlyUpdateStatus)
                        {
                            var ret = BLL.BaseBLL._ExceteBySql($"UPDATE whiterecord SET WhiteRecord_Status='{status}' WHERE WhiteRecord_DeviceNo='{deviceno}'  and WhiteRecord_CarNo='{carno}';");
                            if (ret < 0)
                            {
                                LogManagementMap.WriteToFile(LoggerEnum.MainErrorLog, $"更新白名单状态[{carno}][{deviceno}][{status}]执行SQL失败");
                                return false;
                            }
                            else
                            {
                                return true;
                            }
                        }
                        else
                        {
                            if (status == "6" && string.IsNullOrEmpty(carno) && !string.IsNullOrEmpty(deviceno))
                            {
                                var ret = BLL.BaseBLL._ExceteBySql($"UPDATE whiterecord SET WhiteRecord_Status='{status}' WHERE WhiteRecord_DeviceNo='{deviceno}';");
                                if (ret < 0)
                                {
                                    LogManagementMap.WriteToFile(LoggerEnum.MainErrorLog, $"更新白名单状态[{carno}][{deviceno}][{status}]执行SQL失败");
                                }
                                else
                                {
                                    return true;
                                }

                                //return ResOk(true, "成功", reqBody.parkno);
                            }
                        }
                        break;
                    case "3": //删除成功
                    case "5": //清空成功
                        whiteRecord.WhiteRecord_Status = Utils.ObjectToInt(status, 0);
                        whiteRecord.WhiteRecord_DeviceStatus = 2;
                        if (onlyUpdateStatus)
                        {
                            var ret = BLL.BaseBLL._ExceteBySql($"UPDATE whiterecord SET WhiteRecord_Status='{status}',WhiteRecord_DeviceStatus='2',WhiteRecord_Remark='删除名单' WHERE WhiteRecord_DeviceNo='{deviceno}' and WhiteRecord_CarNo='{carno}';");
                            if (ret < 0)
                            {
                                LogManagementMap.WriteToFile(LoggerEnum.MainErrorLog, $"更新白名单状态[{carno}][{deviceno}][{status}]执行SQL失败");
                                return false;
                            }
                            else
                            {
                                return true;
                            }
                        }
                        else
                        {
                            if (status == "5" && string.IsNullOrEmpty(carno) && !string.IsNullOrEmpty(deviceno))
                            {
                                var ret = BLL.BaseBLL._ExceteBySql($"UPDATE whiterecord SET WhiteRecord_Status='{status}',WhiteRecord_DeviceStatus='2',WhiteRecord_Remark='车道清空名单' WHERE WhiteRecord_DeviceNo='{deviceno}';");
                                if (ret < 0)
                                {
                                    LogManagementMap.WriteToFile(LoggerEnum.MainErrorLog, $"更新白名单状态[{carno}][{deviceno}][{status}]执行SQL失败");
                                }
                                else
                                {
                                    return true;
                                }

                                //return ResOk(true, "成功", reqBody.parkno);

                            }
                        }

                        break;
                }

                bool isWhite = AppBasicCache.GetCar.Values.Where(x => x.Car_CarNo == carno).Count() > 0;
                bool isBlack = isWhite ? false : AppBasicCache.GetBlackList.Values.Where(x => x.BlackList_CarNo == carno).Count() > 0;

                whiteRecord = whiteRecord ?? new Model.WhiteRecord();
                whiteRecord.WhiteRecord_CarNo = carno;
                whiteRecord.WhiteRecord_DeviceNo = deviceno;
                whiteRecord.WhiteRecord_Remark = remark;
                whiteRecord.WhiteRecord_Time = DateTime.Now;
                whiteRecord.WhiteRecord_Type = isWhite ? 0 : (isBlack ? 1 : 0);

                if (!string.IsNullOrEmpty(whiteRecord.WhiteRecord_CarNo) && !string.IsNullOrEmpty(whiteRecord.WhiteRecord_DeviceNo))
                {
                    var res = BLL.BaseBLL._AddOrUpdateModel<Model.WhiteRecord>(whiteRecord);
                    if (res < 0)
                    {
                        LogManagementMap.WriteToFile(LoggerEnum.MainErrorLog, $"更新白名单状态[{carno}][{deviceno}][{status}]执行SQL失败");
                    }
                    else
                    {
                        return true;
                    }
                }
            }
            catch (Exception ex)
            {
                LogManagementMap.WriteToFile(LoggerEnum.MainErrorLog, $"更新白名单状态[{carno}][{deviceno}][{status}]执行异常：" + ex.ToString());
            }

            return false;
        }


        /// <summary>
        /// 获取离线支付二维码
        /// </summary>
        /// <param name="passwayno">车道编码</param>
        /// <param name="blueNo">蓝牙编号</param>
        /// <param name="carno">车牌号</param>
        /// <param name="money">出场订单金额</param>
        /// <returns></returns>
        public static string GetBlueQrCode(string passwayno, string blueNo, string carno = "", string money = "")
        {

            if (string.IsNullOrEmpty(AppBasicCache.CurrentSysConfigContent?.SiteDomain_WeixinSmall))
            {
                //AppBasicCache.CurrentSysConfigContent.SiteDomain_WeixinSmall = "https://wxs.szymzh.com/";
                return "";
            }
            //AppBasicCache.CurrentSysConfigContent.SiteDomain_WeixinSmall = "http://192.168.2.160.8011/";

            var gate = BLL.Passway.GetPasswayGateType(passwayno);
            var inout = (gate == 0 || gate == 3) ? "1" : "0";//出入口类型
            var parkkey = AppBasicCache.GetParking?.Parking_Key;//车场Key
            var key = "";//为空

            if (!string.IsNullOrEmpty(carno) && carno.StartsWith("临")) { carno = carno.Substring(1); }

            var newCarNo = "";
            if (!string.IsNullOrEmpty(carno))
            {
                foreach (char c in carno)
                {
                    if (Utils.IsChinese(c.ToString()))
                    {
                        byte[] bytes = System.Text.Encoding.UTF8.GetBytes(c.ToString()); // 将字符串转换为字节数组
                        newCarNo += Convert.ToBase64String(bytes); // 使用 Base64 编码
                    }
                    else
                    {
                        newCarNo += c;
                    }
                }
            }

            string vsn = Common.Utils.MD5("qr" + blueNo + carno.ToUpper() + DateTime.Now.ToString("yyyyMMddHH")).Substring(28).ToLower();

            var qrCode = $"{AppBasicCache.CurrentSysConfigContent.SiteDomain_WeixinSmall.Trim('/')}/dr/{parkkey}/{inout}/{passwayno}/{key}/bt/{newCarNo}/{blueNo}/{money}/{vsn}";

            return qrCode;
        }

        /// <summary>
        /// 检查数据库是否正常连接
        /// </summary>
        /// <returns></returns>
        public static async Task<bool> CheckMySQLConnection()
        {
            try
            {
                var objList = BLL.BaseBLL._ExecuteByTimeOut<object>("show tables;", 5);
                if (objList?.Count > 0)
                {
                    LogManagementMap.WriteToFile(LoggerEnum.MainInfoLog, "检查数据库是否正常连接：已连接成功！！！");
                    return true;
                }
            }
            catch (Exception ex)
            {
                LogManagementMap.WriteToFile(LoggerEnum.MainErrorLog, "MYSQL连接失败：" + ex.ToString());
                await Task.Delay(500);
            }
            return false;
        }


        private static DateTime GetDBTableTime { get; set; } = default;
        private static List<string> _DBTables { get; set; } = new List<string>();

        /// <summary>
        /// 获取数据表名称
        /// </summary>
        /// <returns></returns>
        public static bool ExistDBTable(string tableName)
        {
            try
            {
                if (_DBTables.Count == 0 || GetDBTableTime == default)
                {
                    _DBTables = BLL.BaseBLL._Execute<string>("SHOW TABLES");
                    GetDBTableTime = DateTime.Now;
                }
                else
                {
                    if (Math.Abs(GetDBTableTime.Subtract(DateTime.Now).TotalMinutes) > 10)
                    {
                        _DBTables = BLL.BaseBLL._Execute<string>("SHOW TABLES");
                        GetDBTableTime = DateTime.Now;
                    }
                }

                if (_DBTables.Count > 0)
                {
                    return _DBTables.Where(x => x.ToLower() == tableName.ToLower()).ToList().Count > 0;
                }
            }
            catch (Exception ex)
            {
                LogManagementMap.WriteToFile(LoggerEnum.MainErrorLog, "获取数据表名称异常：" + ex.ToString());
            }

            return false;
        }

        /// <summary>
        /// 【每月最高限额】创建CarFees
        /// </summary>
        /// <param name="payColl"></param>
        /// <param name="param"></param>
        /// <returns></returns>
        /// <summary>
        /// 【每月最高限额】创建CarFees
        /// </summary>
        /// <param name="payColl"></param>
        /// <param name="param"></param>
        /// <returns></returns>
        public static (List<CarFees>, List<CarFeesOrder>) CreateCarFees(List<Model.ParkOrder> parkOrders, Model.PayColl payColl, List<ChargeModels.PayResult> payResults = null, List<Model.ControlEvent> cevList = null)
        {
            List<CarFees> carfees = null;
            List<CarFeesOrder> feesOrders = null;
            if (payColl != null && payColl.payOrderList?.Count > 0 && parkOrders != null)
            {
                foreach (var parkOrder in parkOrders.Where(x => x != null && !string.IsNullOrEmpty(x.ParkOrder_No) && x.ParkOrder_StatusNo != 204 && !(cevList?.Any(y => y.ControlEvent_ParkOrderNo == x.ParkOrder_No) ?? false)))
                {
                    if (string.IsNullOrEmpty(parkOrder?.ParkOrder_CarCardType)) continue;

                    var cct = AppBasicCache.GetElement(AppBasicCache.GetCarcardTypes, parkOrder.ParkOrder_CarCardType);
                    if (cct != null)
                    {
                        var policycct = BLL.PolicyCarCard.GetEntityByCarCard(cct.CarCardType_No);
                        if (policycct?.PolicyCarCard_MonthMaxMoney > 0)
                        {
                            carfees = carfees ?? new List<CarFees>();
                            feesOrders = feesOrders ?? new List<CarFeesOrder>();

                            var payorderList = payColl.payOrderList.FindAll(x => x.PayOrder_ParkOrderNo == parkOrder.ParkOrder_No);
                            if (payorderList == null || payorderList.Count == 0) continue;

                            // 检查是否已经累计过该支付订单
                            var carfeesOrders = BLL.BaseBLL._GetAllEntity(new Model.CarFeesOrder(), "CarFeesOrder_PayOrderNo", $"CarFeesOrder_PayOrderNo in @CarFeesOrder_PayOrderNo",
                                new { CarFeesOrder_PayOrderNo = payorderList.Select(x => x.PayOrder_No).ToList() });
                            if (carfeesOrders.Count > 0)
                            {
                                // 获取已累计的支付订单号列表
                                var accumulatedPayOrderNos = new HashSet<string>(carfeesOrders.Select(x => x.CarFeesOrder_PayOrderNo));
                                // 剔除已累计的支付订单
                                payorderList = payorderList
                                    .Where(x => !accumulatedPayOrderNos.Contains(x.PayOrder_No))
                                    .ToList();
                            }
                            if (payorderList.Count == 0) { continue; }

                            DateTime latestOutTime = DateTime.MinValue;

                            var payResult = payResults?.Find(x => x != null && x.orderNo == parkOrder.ParkOrder_No);

                            // 记录每个月的累计金额，使用 "yyyyMM" 作为键
                            Dictionary<int, decimal> monthWisePayedMoney = new Dictionary<int, decimal>();

                            decimal payedMoney = 0;
                            foreach (var item in payorderList)
                            {
                                var currmoney = ((item.PayOrder_PayedMoney ?? 0) + (item.PayOrder_SelfMoney ?? 0) + (item.PayOrder_StoredMoney ?? 0));
                                if (currmoney > item.PayOrder_Money)//一般支付金额大于应付金额，是平台下发， 合并了追缴金额
                                {
                                    if (item.PayOrder_Money >= 0) currmoney = item.PayOrder_Money.Value;
                                }

                                payedMoney += currmoney;
                                var detail_enterTime = item.PayOrder_EnterTime.Value;
                                var detail_outTime = item.PayOrder_PayedTime.Value;

                                // 记录最新的离场时间，以确定最新的计费月份
                                if (detail_outTime > latestOutTime)
                                {
                                    latestOutTime = detail_outTime;
                                }
                            }

                            // 处理跨月情况
                            if (payResult != null)
                            {
                                if (payResult.list != null && payResult.list.Count > 0)
                                {
                                    // 遍历 payResult.list 根据 starttime 分摊
                                    foreach (var payItem in payResult.list)
                                    {
                                        // 判断是否属于当前月份
                                        if (payItem.starttime.HasValue)
                                        {
                                            DateTime payItemStartTime = payItem.starttime.Value;
                                            // 在同一月份内，分摊金额
                                            int monthKey = int.Parse(payItemStartTime.ToString("yyyyMM"));
                                            if (!monthWisePayedMoney.ContainsKey(monthKey))
                                            {
                                                monthWisePayedMoney[monthKey] = 0;
                                            }
                                            monthWisePayedMoney[monthKey] += payItem.payedamount.Value;
                                        }
                                    }
                                }
                                else
                                {
                                    // 没有 payResult.list 的情况，直接保存到支付时间对应的月份
                                    int monthKey = int.Parse(latestOutTime.ToString("yyyyMM"));
                                    if (!monthWisePayedMoney.ContainsKey(monthKey))
                                    {
                                        monthWisePayedMoney[monthKey] = 0;
                                    }
                                    monthWisePayedMoney[monthKey] += payedMoney;
                                }
                            }
                            else
                            {
                                // 没有 payResult 的情况，直接保存到支付时间对应的月份
                                int monthKey = int.Parse(latestOutTime.ToString("yyyyMM"));
                                if (!monthWisePayedMoney.ContainsKey(monthKey))
                                {
                                    monthWisePayedMoney[monthKey] = 0;
                                }
                                monthWisePayedMoney[monthKey] += payedMoney;
                            }



                            #region 均摊逻辑
                            //payedMoney是累计金额的标准，monthWisePayedMoney统计的金额与payedMoney相比，必须保持一致，不一致则均摊每个月的累计金额

                            // 获取 monthWisePayedMoney 的总金额
                            decimal totalMonthWisePayedMoney = monthWisePayedMoney.Values.Sum();

                            // 比较 totalMonthWisePayedMoney 和 payedMoney
                            decimal remainingExcess = totalMonthWisePayedMoney - payedMoney;

                            // 计算需要均摊的金额
                            if (remainingExcess != 0)
                            {
                                List<int> validIndexes = monthWisePayedMoney.Keys.ToList(); // 用于存储可以参与均摊的月份索引
                                if (remainingExcess > 0)
                                {
                                    // 倒序排列
                                    validIndexes.Sort((x, y) => y.CompareTo(x));
                                }
                                else
                                {
                                    //正序排列
                                    validIndexes.Sort((x, y) => x.CompareTo(y));
                                }

                                // 第一次均分扣减
                                for (int i = 0; i < validIndexes.Count; i++)
                                {
                                    int monthKey = validIndexes[i];
                                    decimal monthAmount = monthWisePayedMoney[monthKey];

                                    // 计算平均减少金额
                                    decimal avgReduce = Math.Round(remainingExcess / (validIndexes.Count - i), 2); // 动态计算均分金额
                                    decimal maxCanReduce = monthAmount; // 该项最多能扣多少
                                    decimal actualReduce = Math.Min(avgReduce, maxCanReduce); // 取最小值

                                    monthWisePayedMoney[monthKey] -= actualReduce;
                                    remainingExcess -= actualReduce;

                                    // 如果当前月份已经扣完了，就从 validIndexes 移除
                                    if (monthWisePayedMoney[monthKey] == 0)
                                    {
                                        validIndexes.RemoveAt(i);
                                        i--;
                                    }

                                    // 如果已经没有剩余差额，则跳出
                                    if (remainingExcess <= 0)
                                    {
                                        break;
                                    }
                                }

                                // 修正误差，确保完全扣除
                                while (remainingExcess > 0 && validIndexes.Count > 0)
                                {
                                    for (int i = 0; i < validIndexes.Count; i++)
                                    {
                                        int monthKey = validIndexes[i];
                                        decimal monthAmount = monthWisePayedMoney[monthKey];

                                        decimal maxCanReduce = monthAmount; // 该项最多能扣多少
                                        decimal actualReduce = Math.Min(remainingExcess, maxCanReduce); // 取最小值

                                        monthWisePayedMoney[monthKey] -= actualReduce;
                                        remainingExcess -= actualReduce;

                                        // 如果某项被扣完了，它就不再参与后续分配
                                        if (monthWisePayedMoney[monthKey] == 0)
                                        {
                                            validIndexes.RemoveAt(i);
                                            i--;
                                        }

                                        if (remainingExcess <= 0)
                                        {
                                            break; // 完全扣完，结束
                                        }
                                    }
                                }
                            }

                            #endregion

                            // 查询该车牌号当月是否已有累计记录
                            foreach (var monthPair in monthWisePayedMoney)
                            {
                                int monthKey = monthPair.Key;
                                decimal totalMonthPayedMoney = monthPair.Value;

                                var currCarfees = carfees.Find(x => x.CarFees_CarNo == parkOrder.ParkOrder_CarNo && x.CarFees_Month == monthKey);
                                if (currCarfees == null)
                                {
                                    var oldcarfee = BLL.BaseBLL._GetEntityByWhere(new Model.CarFees(),
                                   "CarFees_No,CarFees_Money",
                                   "CarFees_Month=@CarFees_Month and CarFees_CarNo=@CarFees_CarNo",
                                   new
                                   {
                                       CarFees_Month = monthKey, // 使用 yyyyMM 格式
                                       CarFees_CarNo = parkOrder.ParkOrder_CarNo
                                   });
                                    // 计算最终的累计费用
                                    var newfees = new Model.CarFees()
                                    {
                                        CarFees_No = oldcarfee?.CarFees_No ?? Utils.CreateNumber_SnowFlake,
                                        CarFees_CarNo = parkOrder.ParkOrder_CarNo,
                                        CarFees_CarCardTypeNo = cct.CarCardType_No,
                                        CarFees_CarTypeNo = parkOrder.ParkOrder_CarType,
                                        CarFees_Month = monthKey,
                                        CarFees_UpdateTime = DateTime.Now,
                                        CarFees_Money = totalMonthPayedMoney + (oldcarfee?.CarFees_Money ?? 0),
                                    };
                                    carfees.Add(newfees);

                                    payorderList.ForEach(x =>
                                    {
                                        var newFeesOrder = new Model.CarFeesOrder()
                                        {
                                            CarFeesOrder_CarFessNo = newfees.CarFees_No,
                                            CarFeesOrder_ParkOrderNo = parkOrder.ParkOrder_No,
                                            CarFeesOrder_PayOrderNo = x.PayOrder_No,
                                            CarFeesOrder_Money = (x.PayOrder_PayedMoney ?? 0 + (x.PayOrder_SelfMoney ?? 0 - x.PayOrder_OutReduceMoney ?? 0) + x.PayOrder_StoredMoney ?? 0)
                                        };
                                        feesOrders.Add(newFeesOrder);
                                    });
                                }
                                else
                                {
                                    currCarfees.CarFees_Money += totalMonthPayedMoney;
                                }
                            }
                        }
                    }
                }
            }

            return (carfees, feesOrders);
        }


        /// <summary>
        /// 对一组项按照原始金额的比例，分摊总的扣减金额（如减免费用），并确保尾差处理后扣减总额精确一致。
        /// </summary>
        /// <typeparam name="T">项的类型</typeparam>
        /// <param name="items">要进行扣减分摊的项集合</param>
        /// <param name="totalToReduce">需要分摊的总金额（例如：超出部分需减免的金额）</param>
        /// <param name="getOriginalAmount">用于获取每个项的原始金额的委托（如：item => item.payedamount）</param>
        /// <param name="reduceAmountAction">用于对每个项应用最终扣减金额的委托（如：item, reduce => item.payedamount -= reduce）</param>
        /// <param name="tailStrategy">尾差处理策略（默认为多项平摊），可选值：SingleItem 或 MultiItem</param>
        /// <returns>最终实际分摊扣减的总金额</returns>
        public static decimal DistributeReduction<T>(
           IEnumerable<T> items,
           decimal totalToReduce,
           Func<T, decimal> getOriginalAmount,
           Action<T, decimal> reduceAmountAction,
           ReductionTailStrategy tailStrategy = ReductionTailStrategy.MultiItem)
        {
            if (items == null || !items.Any() || totalToReduce <= 0)
                return 0;

            var itemList = items.ToList();
            decimal totalOriginal = itemList.Sum(getOriginalAmount);

            if (totalOriginal <= 0)
                return 0;

            var reduceMap = new Dictionary<T, decimal>();
            decimal totalReduced = 0;

            // Step 1: 初步按比例分配扣减金额
            foreach (var item in itemList)
            {
                decimal original = getOriginalAmount(item);
                if (original <= 0)
                {
                    reduceMap[item] = 0;
                    continue;
                }

                decimal proportion = original / totalOriginal;
                decimal reduce = Math.Round(proportion * totalToReduce, 2);// 四舍五入到分
                reduce = Math.Min(reduce, original); // 不超过原金额

                reduceMap[item] = reduce;
                totalReduced += reduce;
            }

            // Step 2: 处理尾差，确保最终扣减金额一致
            decimal delta = totalToReduce - totalReduced;
            if (Math.Abs(delta) >= 0.01m)// 误差超过1分才处理
            {
                switch (tailStrategy)
                {
                    case ReductionTailStrategy.SingleItem:
                        // 找一个可以承担误差的 item
                        foreach (var item in itemList.OrderByDescending(getOriginalAmount))
                        {
                            decimal original = getOriginalAmount(item);
                            decimal currentReduce = reduceMap[item];
                            decimal maxCanAdjust = original - currentReduce;

                            if (maxCanAdjust <= 0)
                                continue;

                            decimal adjust = Math.Min(Math.Abs(delta), maxCanAdjust);
                            reduceMap[item] += (delta > 0 ? adjust : -adjust);
                            totalReduced += (delta > 0 ? adjust : -adjust);
                            break;
                        }
                        break;

                    case ReductionTailStrategy.MultiItem:// 多项轮流承担误差
                        var remainMap = reduceMap
                            .ToDictionary(
                                kvp => kvp.Key,
                                kvp => getOriginalAmount(kvp.Key) - kvp.Value
                            );

                        FixRemainingReduction(remainMap, delta);

                        foreach (var kvp in remainMap)
                        {
                            reduceMap[kvp.Key] += delta > 0 ? kvp.Value : -kvp.Value;
                        }

                        totalReduced = reduceMap.Values.Sum();
                        break;
                }
            }

            // Step 3: 应用扣减结果
            foreach (var item in itemList)
            {
                reduceAmountAction(item, reduceMap[item]);
            }

            return totalReduced;
        }

        /// <summary>
        /// 尾差修正：在多个项之间平均扣差
        /// </summary>
        public static void FixRemainingReduction<T>(
            Dictionary<T, decimal> remainingCanReduceMap,
            decimal remainingExcess)
        {
            if (remainingCanReduceMap == null || remainingCanReduceMap.Count == 0 || Math.Abs(remainingExcess) < 0.01m)
                return;

            var keys = remainingCanReduceMap.Where(x => x.Value > 0).Select(x => x.Key).ToList();

            while (Math.Abs(remainingExcess) >= 0.01m && keys.Count > 0)
            {
                for (int i = 0; i < keys.Count; i++)
                {
                    var key = keys[i];
                    decimal canReduce = remainingCanReduceMap[key];
                    if (canReduce <= 0)
                    {
                        keys.RemoveAt(i--);
                        continue;
                    }

                    decimal reduce = Math.Min(Math.Abs(remainingExcess), canReduce);
                    remainingCanReduceMap[key] -= reduce;
                    remainingExcess -= (remainingExcess > 0 ? reduce : -reduce);

                    if (remainingCanReduceMap[key] <= 0.0001m)
                        keys.RemoveAt(i--);

                    if (Math.Abs(remainingExcess) < 0.01m)
                        break;
                }
            }

            if (Math.Abs(remainingExcess) >= 0.01m)
            {
                //throw new Exception($"尾差仍有 {remainingExcess:F2} 元未扣完！");
            }
        }

        /// <summary>
        /// 发放时段全免优惠券
        /// </summary>
        /// <param name="order"></param>
        /// <returns></returns>
        public static (List<Model.CouponRecord>, List<Model.CouponPlan>) GetCouponAllTime(Model.ParkOrder order)
        {
            if (order == null) return (null, null);
            if (order.ParkOrder_EnterTime == null) return (null, null);
            if (AppBasicCache.CurrentSysConfigContent.SysConfig_TimeCouponValidity == null) return (null, null);

            try
            {
                List<Model.CouponRecord> couponList = null;
                List<Model.CouponPlan> couponPlanList = null;

                #region 车辆入场，绑定时段全免优惠券

                couponPlanList = BLL.BaseBLL._GetAllEntity(new Model.CouponPlan(), "CouponPlan_ID,CouponPlan_No,CouponPlan_Value,CouponPlan_StartTime,CouponPlan_EndTime,CouponPlan_VaildTime,CouponPlan_MerchantId,CouponPlan_MerchantName,CouponPlan_UseCount,CouponPlan_MaxCount", $"CouponPlan_IssueCarNo='{order.ParkOrder_CarNo}' " +
                     $"AND CouponPlan_VaildTime>'{order.ParkOrder_EnterTime.Value.ToString("yyyy-MM-dd HH:mm:ss")}' AND CouponPlan_Status=1 AND (CouponPlan_MaxCount = 0 OR CouponPlan_UseCount < CouponPlan_MaxCount) limit 10");
                if (couponPlanList != null && couponPlanList.Count > 0)
                {
                    couponList = new List<Model.CouponRecord>();
                    couponPlanList.ForEach(x =>
                    {
                        //组装优惠券
                        Model.CouponRecord model = new Model.CouponRecord();
                        model.CouponRecord_IssueCarNo = order?.ParkOrder_CarNo;
                        model.CouponRecord_No = Utils.CreateNumber_SnowFlake;
                        model.CouponRecord_ParkDiscountSetNo = x.CouponPlan_No;
                        model.CouponRecord_ParkNo = order?.ParkOrder_ParkNo;
                        model.CouponRecord_Status = 0;
                        model.CouponRecord_Value = Utils.StrToDecimal(x.CouponPlan_Value.ToString(), 0);
                        model.CouponRecord_StartTime = x.CouponPlan_StartTime;
                        model.CouponRecord_EndTime = x.CouponPlan_VaildTime;
                        model.CouponRecord_ParkNo = order?.ParkOrder_ParkNo;
                        model.CouponRecord_ParkOrderNo = order?.ParkOrder_No;
                        model.CouponRecord_VaildTime = x.CouponPlan_EndTime;
                        model.CouponRecord_AddTime = DateTimeHelper.GetNowTime();
                        model.CouponRecord_OnLine = 2;
                        model.CouponRecord_MerchantId = x.CouponPlan_MerchantId;
                        model.CouponRecord_MerchantName = x.CouponPlan_MerchantName;
                        model.CouponRecord_ParkOrderNo = order.ParkOrder_No;
                        model.CouponRecord_CouponCode = ((int)Common.EnumCouponType.HourFree).ToString();

                        DateTime calculatedEnd = DateTime.MinValue;
                        if (model.CouponRecord_StartTime != null)
                        {
                            // 优化时间判断逻辑
                            var now = order.ParkOrder_EnterTime.Value;
                            var startTime = x.CouponPlan_StartTime.Value;
                            var endTime = x.CouponPlan_VaildTime.Value;
                            var deciValue = model.CouponRecord_Value;


                            // ✅ 优先判断 endTime 是否过期
                            bool endTimeExpired = endTime < now;

                            if (!endTimeExpired)
                            {
                                if (startTime < now)
                                    startTime = now;

                                // ✅ 如果有 couponValue，计算优惠券结束时间
                                if (deciValue > 0)
                                {
                                    calculatedEnd = startTime.AddMinutes((double)deciValue);
                                    // 限制 endTime 不超过原 endTime
                                    endTime = endTime < calculatedEnd ? endTime : calculatedEnd;
                                }

                                model.CouponRecord_StartTime = startTime;
                                model.CouponRecord_EndTime = endTime;
                            }
                            else
                            {
                                //已过期
                                model = null;
                            }
                        }
                        else
                        {
                            model = null;
                        }

                        if (model != null)
                        {
                            couponList.Add(model);
                            x.CouponPlan_UseCount += 1;
                            if (x.CouponPlan_Value > 0 && calculatedEnd != DateTime.MinValue && x.CouponPlan_VaildTime > calculatedEnd)
                            {
                                x.CouponPlan_VaildTime = calculatedEnd;
                            }
                        }
                    });
                }

                return (couponList, couponPlanList);
                #endregion
            }
            catch (Exception ex)
            {
                LogManagementMap.WriteToFile(LoggerEnum.MainErrorLog, "时段全免优惠发放失败：" + ex.ToString());
            }

            return (null, null);
        }
    }

    public enum ReductionTailStrategy
    {
        /// <summary>
        /// 使用单个 item 承担尾差（最常用）
        /// </summary>
        SingleItem,

        /// <summary>
        /// 使用多个 item 平摊尾差（适合更精细的分配）
        /// </summary>
        MultiItem
    }
}
