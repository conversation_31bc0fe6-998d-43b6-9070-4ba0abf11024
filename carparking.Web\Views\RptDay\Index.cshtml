
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>车场日报表</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=0">
    <link href="~/Static/admin/layui/css/layui.css" rel="stylesheet">
    <link href="~/Static/css/font-awesome.min93e3.css" rel="stylesheet" />
    <link href="~/Static/css/animate.min.css" rel="stylesheet">
    <link href="~/Static/admin/style/admin.css" rel="stylesheet" />
    <style>
        html, body { margin: 0; padding: 0; }
        .fa { margin: 10px 4px; float: left; font-size: 16px; }
        .layui-form-select .layui-input { width: 182px; }
        @@media screen and (max-width: 1920px) {
            #table { width: 1620px; }
            th, td { width: 108px; font-size: 14px; height: 35px; }
        }
        @@media screen and (max-width: 1440px) {
            #table { width: 1155px; }
            th, td { width: 77px; font-size: 12px; height: 25px; }
        }
        @@page { size: landscape; }
        td { padding: 0px; margin: 0px; margin-bottom: 0mm; margin-top: 0mm; }
        th, td { text-align: center; }
        td { font-family: FangSong; }
        th.sumt { font-family: 'Microsoft YaHei'; text-align: left; text-indent: 15px; }
        .desc { font-size: 14px; font-weight: bolder; color: #f18042; text-align: left; width: 850px; position: absolute; }
        .searchdesc { text-align: right; position: absolute; top: -20px; width: 98%; }
    </style>
<link href="~/Static/css/theme.css" rel="stylesheet" /><script src="~/Static/js/theme.js"></script></head>
<body>

    <div class="layui-fluid animated fadeInRight">
        <div class="layui-card layadmin-header height0"></div>
        <div class="layui-card layadmin-header">
            <div class="layui-breadcrumb" lay-filter="breadcrumb">
                <a><cite>汇总报表</cite></a>
                <a><cite>车场日报表</cite></a>
            </div>
        </div>
        <div class="layui-row layui-col-space30">
            <div class="layui-col-md12">
                <div class="layui-card">
                    <div class="layui-card-header">
                        <div class="test-table-reload-btn layui-form" id="searchForm">
                            <div class="layui-inline form-group">
                                <input class="layui-input " name="start" id="start" autocomplete="off" placeholder="开始时间" value="@DateTime.Now.AddDays(-1).ToString("yyyy-MM-dd 00:00:00")" />
                            </div>
                            <div class="layui-inline form-group">
                                <input class="layui-input " name="end" id="end" autocomplete="off" placeholder="结束时间" value="@DateTime.Now.AddDays(-1).ToString("yyyy-MM-dd 23:59:59")" />
                            </div>
                            <div class="layui-inline form-group">
                                <select data-placeholder="默认数据" class="form-control chosen-select " id="dataType" name="dataType" lay-search>
                                    <option value="0">默认数据</option>
                                    <option value="1">历史数据</option>
                                </select>
                            </div>
                            <div class="layui-inline form-group">
                                <button class="layui-btn" id="Search"><i class="layui-icon layui-icon-search inbtn"></i><t>搜索</t></button>
                            </div>
                            <div class="layui-inline form-group">
                                <button class="layui-btn" id="Print" style="line-height:normal !important;"><i class="layui-icon layui-icon-print inbtn"></i><t>打印</t></button>
                            </div>
                            <div class="layui-inline form-group">
                                <button class="layui-btn" id="Export" style="line-height:normal !important;"><i class="layui-icon layui-icon-export inbtn"></i><t>导出</t></button>
                            </div>
                            <div class="layui-inline form-group">
                                <button class="layui-btn" id="Delete" style="line-height:normal !important;"><i class="layui-icon layui-icon-refresh inbtn"></i><t>清除历史统计</t></button>
                            </div>
                        </div>
                        <!-- 统计方式选择器 - 低调融入 -->
                        <div class="layui-form" style="margin-top: 12px; padding: 8px 0; border-top: 1px solid #eee;">
                            <div class="group-type-container">
                                <label class="group-type-label">统计方式：</label>
                                <div class="radio-group-horizontal">
                                    <label class="radio-item">
                                        <input type="radio" name="groupType" value="0" checked>
                                        <span class="radio-text">操作员</span>
                                    </label>
                                    <label class="radio-item">
                                        <input type="radio" name="groupType" value="1">
                                        <span class="radio-text">区域</span>
                                    </label>
                                    <label class="radio-item">
                                        <input type="radio" name="groupType" value="2">
                                        <span class="radio-text">操作员+区域</span>
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="layui-card-body" style="padding: 5px 15px!important;">
                        <div class="searchdesc"></div>
                        <div class="tabs-container">
                            <table border="1" id="table">
                                <thead id="tableHeader">
                                    <tr>
                                        <th rowspan="2" id="firstColumnHeader">操作员</th>
                                        <th colspan="2">月租车</th>
                                        <th colspan="2">免费车</th>
                                        <th colspan="2">储值车</th>
                                        <th colspan="2">临时车</th>
                                        <th colspan="1">免费放行</th>
                                        <th colspan="2">人工开闸</th>
                                        <th colspan="4">现金合计</th>
                                    </tr>
                                    <tr>
                                        <th>出场车辆</th>
                                        <th>现金金额</th>
                                        <th>出场车辆</th>
                                        <th>现金金额</th>
                                        <th>出场车辆</th>
                                        <th>现金金额</th>
                                        <th>出场车辆</th>
                                        <th>现金金额</th>
                                        <th>出场车辆</th>
                                        <th>出场</th>
                                        <th>入场</th>
                                        <th>总车数</th>
                                        <th>总应收</th>
                                        <th>减免金额</th>
                                        <th>总实收</th>
                                    </tr>
                                </thead>
                                <tbody id="CarContent">
                                </tbody>
                                <tbody>
                                    <tr>
                                        <td colspan="17"></td>
                                    </tr>
                                </tbody>
                                <tbody id="payHtml">
                                </tbody>
                                <tbody>
                                    <tr>
                                        <th colspan="1">支付方式</th>
                                        <td colspan="1">总应收</td>
                                        <td colspan="1" data-key="OnlineYingShou"></td>
                                        <td colspan="1">总减免</td>
                                        <td colspan="1" data-key="OnlineJianMian"></td>
                                        <td colspan="1">总实收</td>
                                        <td colspan="1" data-key="TotalMoney"></td>
                                        <th colspan="1">平台现金</th>
                                        <td colspan="1">总应收</td>
                                        <td colspan="1" data-key="CashYingShou"></td>
                                        <td colspan="1">总减免</td>
                                        <td colspan="1" data-key="CashJianMian"></td>
                                        <td colspan="1">总实收</td>
                                        <td colspan="1" data-key="CashOnline"></td>
                                        <td colspan="1"></td>
                                    </tr>
                                    <tr>
                                        <th colspan="12"></th>
                                        <th colspan="2">收费金额合计</th>
                                        <td colspan="2" data-key="AllTotalMoney"></td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                        <div class="desc">温馨提示：系统在凌晨会自动执行统计前一天报表的数据（00：00 ~ 23：59），若对停车信息手动变更，则需手动清除历史统计数据</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <style>
        /* 统计方式选择器 - 精致分段控制器 */
        .group-type-container {
            display: flex;
            align-items: center;
            gap: 18px;
            padding: 10px 0;
        }

        .group-type-label {
            font-weight: 500;
            color: #4a5568;
            margin: 0;
            white-space: nowrap;
            font-size: 14px;
            letter-spacing: 0.3px;
        }

        .radio-group-horizontal {
            display: inline-flex;
            background: linear-gradient(145deg, #f7f8fc, #edf2f7);
            border-radius: 8px;
            padding: 3px;
            border: 1px solid #e2e8f0;
            box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.06);
            position: relative;
        }

        .radio-item {
            position: relative;
            cursor: pointer;
            margin: 0;
            padding: 9px 18px;
            border-radius: 6px;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            background: transparent;
            border: none;
            font-size: 13px;
            color: #718096;
            font-weight: 450;
            z-index: 1;
        }

        .radio-item:hover {
            color: #4a5568;
            transform: translateY(-0.5px);
        }

        .radio-item input[type="radio"] {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
            margin: 0;
            padding: 0;
            border: none;
            outline: none;
            visibility: hidden;
            pointer-events: none;
        }

        /* 隐藏所有可能的layui相关图标和动画 */
        .radio-item .layui-icon,
        .radio-item .layui-anim,
        .radio-item::before,
        .radio-item::after {
            display: none !important;
        }

        /* 确保radio按钮相关的伪元素也被隐藏 */
        .radio-item input[type="radio"]::before,
        .radio-item input[type="radio"]::after {
            display: none !important;
        }

        .radio-item:has(input[type="radio"]:checked) {
            background: linear-gradient(145deg, #ffffff, #f8fafc);
            color: #2d3748;
            font-weight: 550;
            box-shadow:
                0 2px 8px rgba(0, 0, 0, 0.08),
                0 1px 3px rgba(0, 0, 0, 0.06),
                inset 0 1px 0 rgba(255, 255, 255, 0.7);
            transform: translateY(-0.5px);
        }

        .radio-text {
            font-size: 13px;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            user-select: none;
            white-space: nowrap;
            letter-spacing: 0.2px;
        }

        /* 微妙的动画效果 */
        .radio-group-horizontal::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            border-radius: 8px;
            background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.3), transparent);
            opacity: 0;
            transition: opacity 0.3s ease;
            pointer-events: none;
        }

        .radio-group-horizontal:hover::before {
            opacity: 1;
        }

        /* 响应式设计 */
        @@media (max-width: 768px) {
            .group-type-container {
                flex-direction: column;
                align-items: flex-start;
                gap: 12px;
            }

            .radio-group-horizontal {
                width: 100%;
                justify-content: space-between;
                padding: 2px;
            }

            .radio-item {
                flex: 1;
                text-align: center;
                padding: 7px 10px;
                font-size: 11px;
                margin: 0 1px;
            }

            .group-type-label {
                font-size: 12px;
            }
        }

        /* 高分辨率屏幕优化 */
        @@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
            .radio-group-horizontal {
                border-width: 0.5px;
            }

            .radio-item:has(input[type="radio"]:checked) {
                box-shadow:
                    0 1px 4px rgba(0, 0, 0, 0.06),
                    0 0.5px 2px rgba(0, 0, 0, 0.04),
                    inset 0 0.5px 0 rgba(255, 255, 255, 0.8);
            }
        }
    </style>

    <script src="~/Static/js/jquery-3.3.1.min.js" asp-append-version="true"></script>
    <script src="~/Static/admin/layui/layui.js" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.common.js?t=@DateTime.Now.Ticks" asp-append-version="true"></script>
    <script src="~/Static/js/pm.utils.js?1" asp-append-version="true"></script>
    <script>
        layui.use(['form'], function () {
            pager.init();
        });
    </script>
    <script>
        var mychart;
        var pager = {
            dataType: 0,
            groupType: 0,
            dataTime: "",
            init: function () {
                this.bindSelect();
                this.bindEvent();
                //this.bindData();
                this.bindPower();
                this.updateTableHeader();
            },
            bindSelect: function () {
                _DATE.bind(layui.laydate, ["start", "end"], { type: "datetime", range: true });
            },
            updateTableHeader: function () {
                var groupType = $("input[name='groupType']:checked").val();
                var headerText = "";
                switch (groupType) {
                    case "0":
                        headerText = "操作员";
                        break;
                    case "1":
                        headerText = "区域";
                        break;
                    case "2":
                        headerText = "操作员+区域";
                        break;
                    default:
                        headerText = "操作员";
                        break;
                }
                $("#firstColumnHeader").text(headerText);
            },
            bindData: function () {
                var sdt = $("#start").val();
                var edt = $("#end").val();
                pager.dataType = $("#dataType").val();
                pager.groupType = $("input[name='groupType']:checked").val();
                pager.dataTime = sdt;
                layer.msg("查询中...", { icon: 16, time: 0 });
                $.post("/RptDay/RptData", { sdt: sdt, edt: edt, checkhis: true, dataType: pager.dataType, groupType: pager.groupType }, function (json) {
                    layer.closeAll();
                    if (json.success) {
                        if (json.msg.length > 15) $(".searchdesc").html(json.msg); else $(".searchdesc").html("");
                        localStorage.setItem("rptdaydata", encodeURIComponent(JSON.stringify(json.data)));
                        pager.bindShow(json.data);
                    } else {
                        if (json.data == "0") {
                            layer.confirm('统计需要耗费较长时间，是否继续？', function (index) {
                                layer.msg("查询中...", { icon: 16, time: 0 });
                                $.post("/RptDay/RptData", { sdt: sdt, edt: edt, checkhis: false, dataType: pager.dataType, groupType: pager.groupType }, function (json) {
                                    layer.msg("查询完成")
                                    if (json.success) {
                                        if (json.msg.length > 15) $(".searchdesc").html(json.msg); else $(".searchdesc").html("");
                                        localStorage.setItem("rptdaydata", encodeURIComponent(JSON.stringify(json.data)));
                                        pager.bindShow(json.data);
                                    } else {
                                        layer.msg(json.msg, { icon: 0, time: 2000 });
                                    }
                                }, "json");
                                layer.close(index);
                            });
                        } else {
                            layer.msg(json.msg, { icon: 0, time: 2000 });
                        }
                    }
                }, "json");
            },
            bindEvent: function () {
                $("#Search").click(function () {
                    pager.bindData();
                });

                $("#Print").click(function () {
                    win_open("/RptDay/RptView?sdt=" + $("#start").val() + "&edt=" + $("#end").val() + "&datatype=" + $("#dataType").val() + "&grouptype=" + $("input[name='groupType']:checked").val() + "&t=" + Math.random());
                });
                //Export
                $("#Export").click(function () {
                    win_open("/RptDay/RptView?sdt=" + $("#start").val() + "&edt=" + $("#end").val() + "&export=1" + "&datatype=" + $("#dataType").val() + "&grouptype=" + $("input[name='groupType']:checked").val() + "&t=" + Math.random());
                });

                $("#Delete").click(function () {
                    layer.open({
                        type: 0,
                        title: "消息提示",
                        btn: ["确定", "取消"],
                        content: "确定清除当前报表的所有历史统计吗?",
                        area: ["300px"],
                        yes: function (res) {
                            layer.msg("处理中", { icon: 16, time: 0 });
                            $.post("DeleteCache?sdt=" + $("#start").val() + "&edt=" + $("#end").val() + "&export=1", {}, function (json) {
                                if (json.success)
                                    layer.msg("清除成功", { icon: 1, time: 1500 }, function () { pager.bindData(); });
                                else
                                    layer.msg(json.msg, { icon: 0, btn: ['确定'], time: 0, end: function () {  } });
                            }, "json");
                        },
                        btn2: function () { }
                    })
                });

                layui.form.on("select", function (data) {
                    if (data.elem.id == "dataType" && data.value == "1") {
                        var layerDataType = layer.open({
                            id: 2,
                            type: 0,
                            title: "查询数据须知",
                            btn: ["知道了"],
                            content: "默认查询范围：当您未指定时间条件时，系统将自动查询当前年份的全部数据。<br/>跨年查询限制：若选择历史数据，查询时间范围需在同一年内。<br/>例如，若开始时间设定为2024年，则仅能查询2024年的相关数据，无法跨年查询2023年或2025年的数据。",
                            yes: function (res) {
                                layer.close(layerDataType)
                            },
                            btn2: function () { }
                        })
                    }
                })

                // 监听统计方式单选按钮变化
                $("input[name='groupType']").change(function() {
                    pager.updateTableHeader();
                });
            },
            bindShow: function (data) {
                $("#CarContent").html('');
                data.RptPrintList.forEach((item, index) => {
                    var trHtml = '<tr>'
                        + '<td>' + (item.Account == null ? "平台" : item.Account) + '</td>'
                        + '<td>' + item.MonthCount + '</td>'
                        + '<td>' + item.MonthMoney + '</td>'
                        + '<td>' + item.FreeCount + '</td>'
                        + '<td>' + item.FreeMoney + '</td>'
                        + '<td>' + item.StoreCount + '</td>'
                        + '<td>' + item.StoreMoney + '</td>'
                        + '<td>' + item.TempCount + '</td>'
                        + '<td>' + item.TempMoney + '</td>'
                        + '<td>' + item.FreeOutCount + '</td>'
                        + '<td>' + item.OpenGateCount + '</td>'
                        + '<td>' + item.OpenGateInCount + '</td>'
                        + '<td>' + item.TotalCount + '</td>'
                        + '<td>' + item.TotalYsMoney + '</td>'
                        + '<td>' + item.TotalJmMoney + '</td>'
                        + '<td>' + item.TotalSsMoney + '</td>'
                        + '</tr>';
                    $("#CarContent").append(trHtml);
                });

                var trHtml = '<tr>'
                    + '<th>合计</th>'
                    + '<td>' + data.RptPrintListTotal.MonthCount + '</td>'
                    + '<td>' + data.RptPrintListTotal.MonthMoney + '</td>'
                    + '<td>' + data.RptPrintListTotal.FreeCount + '</td>'
                    + '<td>' + data.RptPrintListTotal.FreeMoney + '</td>'
                    + '<td>' + data.RptPrintListTotal.StoreCount + '</td>'
                    + '<td>' + data.RptPrintListTotal.StoreMoney + '</td>'
                    + '<td>' + data.RptPrintListTotal.TempCount + '</td>'
                    + '<td>' + data.RptPrintListTotal.TempMoney + '</td>'
                    + '<td>' + data.RptPrintListTotal.FreeOutCount + '</td>'
                    + '<td>' + data.RptPrintListTotal.OpenGateCount + '</td>'
                    + '<td>' + data.RptPrintListTotal.OpenGateInCount + '</td>'
                    + '<td>' + data.RptPrintListTotal.TotalCount + '</td>'
                    + '<td>' + data.RptPrintListTotal.TotalYsMoney + '</td>'
                    + '<td>' + data.RptPrintListTotal.TotalJmMoney + '</td>'
                    + '<td>' + data.RptPrintListTotal.TotalSsMoney + '</td>'
                    + '</tr>';
                $("#CarContent").append(trHtml);

                $("#payHtml").html(data.payHtml);
                $("td").each(function () {
                    var key = $(this).attr("data-key");

                    if (key != null && data.onlineData[key] != null) {
                        var dst = data.onlineData[key];
                        if (dst > 0) dst = dst.toFixed(2);
                        $(this).text(dst);
                    }

                    if (key == "AllTotalMoney")
                        $(this).text((data.onlineData["TotalMoney"] + data.onlineData["CashOnline"] + data.RptPrintListTotal["TotalSsMoney"]).toFixed(2));
                });

                //window.print();
            },
            bindPower: function () {
                window.parent.global.getBtnPower(window, function (pagePower) {
                    console.log(pagePower)
                });
            }
        }
    </script>
</body>
</html>
