-- 为HistoryReport表添加统计方式字段
-- 执行时间：2024年
-- 说明：添加HistoryReport_GroupType字段用于区分不同统计方式的历史数据

-- 检查字段是否已存在，如果不存在则添加
SET @sql = '';
SELECT @sql := CASE 
    WHEN COUNT(*) = 0 THEN 
        'ALTER TABLE historyreport ADD COLUMN HistoryReport_GroupType INT(11) DEFAULT 0 COMMENT ''统计方式:0-操作员,1-区域,2-操作员+区域'';'
    ELSE 
        'SELECT ''字段 HistoryReport_GroupType 已存在'' AS message;'
END
FROM information_schema.COLUMNS 
WHERE TABLE_SCHEMA = DATABASE() 
  AND TABLE_NAME = 'historyreport' 
  AND COLUMN_NAME = 'HistoryReport_GroupType';

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 为现有数据设置默认值（操作员统计）
UPDATE historyreport 
SET HistoryReport_GroupType = 0 
WHERE HistoryReport_GroupType IS NULL;

-- 添加索引以提高查询性能
SET @sql = '';
SELECT @sql := CASE 
    WHEN COUNT(*) = 0 THEN 
        'CREATE INDEX idx_historyreport_grouptype ON historyreport(HistoryReport_BeginTime, HistoryReport_EndTime, HistoryReport_Type, HistoryReport_GroupType);'
    ELSE 
        'SELECT ''索引 idx_historyreport_grouptype 已存在'' AS message;'
END
FROM information_schema.STATISTICS 
WHERE TABLE_SCHEMA = DATABASE() 
  AND TABLE_NAME = 'historyreport' 
  AND INDEX_NAME = 'idx_historyreport_grouptype';

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;
