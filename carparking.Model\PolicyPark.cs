﻿using System;
using System.Runtime.InteropServices;
using Newtonsoft.Json;

namespace carparking.Model
{
    /// <summary>
    ///车牌\卡类设置
    /// </summary>
    [Serializable]
    public class PolicyPark
    {
        public int? PolicyPark_ID { get; set; }
        public string PolicyPark_No { get; set; }
        /// <summary>
        /// 停车场唯一编码
        /// </summary>
        public string PolicyPark_ParkNo { get; set; }
        /// <summary>
        /// 设备搭配类型，新增车道时，默认新增的相机类型和显示屏类型，简化设置步骤
        /// </summary>
        public string PolicyPark_DefaultDevice { get; set; }
        /// <summary>
        /// 一位多车，1-车牌类型场内智能升降级,2-以入场时系统判别的车牌类型为准,
        /// 举例，场内同时存在同一车主的两辆车，一辆为月租车|另外一辆为月临车,智能升级指的是月租车出场后，场内月临车自动升级为月租车，出场时仅收同时在场的费用。“以入场时车牌计费类型为准”指的是场内月临车不能自动升级，本次整个停车时长全是是按月临车收费
        /// </summary>
        public int? PolicyPark_MoreCar { get; set; }
        /// <summary>
        /// 系统自动释放车位,
        /// 单位“天”，用于优化因车牌识别错误引起的车位数长期被占用问题，0或者负数表示永不释放车位，正整数时，表示停车时长超过指定天数后，系统自动释放车位，但记录不能删除场内记录
        /// </summary>
        public int? PolicyPark_OccupyDay { get; set; }
        /// <summary>
        /// 车牌登记时是否收取临停费用，0-否，1-是,
        /// 场内临时车登记成月租车时，是否需要把临停费用补齐后再进行登记
        /// </summary>
        public int? PolicyPark_IsFeeTempCar { get; set; }
        /// <summary>
        /// 车场所在的省份或直辖市的简称,
        /// 车牌登记时，减少输入内容，根据车场所在区域进行设置，例如深圳设置为“粤B”
        /// </summary>
        public string PolicyPark_CarPrefix { get; set; }
        /// <summary>
        /// 场内缴费限时出场,
        /// 中央收费处缴费或扫场内码缴费后，车主必须在限定的时间内出场，否则按超时收费处理
        /// </summary>
        public int? PolicyPark_MaxStayTime { get; set; }
        /// <summary>
        /// 计费0元是否自动放行，0-否，1-是(默认自动放行),
        /// 车主缴费后，在出口处是否需要确认放行。
        /// </summary>
        public int? PolicyPark_PayedConfirm { get; set; }
        /// <summary>
        /// 无感支付是否需确认开闸，0-否，1-是,
        /// 无感支付车辆，在出口处是否需要确认放行。
        /// </summary>
        public int? PolicyPark_NoPwdPayedConfirm { get; set; }
        /// <summary>
        /// 黄牌车默认车型(大车 小车)
        /// </summary>
        public string PolicyPark_YellowCarType { get; set; }
        /// <summary>
        /// 优惠是否支持叠加次/张,
        /// 指的是同一车牌一次停车最大叠加优惠次/张，1表示仅优惠一次/张，以此类推，优惠叠加
        /// </summary>
        public int? PolicyPark_MaxDiscount { get; set; }
        /// <summary>
        /// 优惠券使用限额，停车金额超过XX金额后，不可进行优惠券减免,0-无限制
        /// </summary>
        public decimal? PolicyPark_MaxUseAmount { get; set; }
        /// <summary>
        /// 商家允许为同一车牌打折/优惠，1-同一个商户可优惠多次，2-同一个商户仅可优惠1次,
        /// 一次停车过程中同一商户对同一辆车可打折/优惠几次，与上面的最多打折次数配合使用,当“优惠是否支持叠加次/张”不等于1时，本设置才有意义
        /// </summary>
        public int? PolicyPark_Discount { get; set; }
        /// <summary>
        /// 证件抓拍策略，1-证件抓拍后免费，2-只做凭证不免费
        /// </summary>
        public int? PolicyPark_Capture { get; set; }
        /// <summary>
        /// 服务器历史记录保存天数
        /// </summary>
        public int? PolicyPark_SerRecordDay { get; set; }
        /// <summary>
        /// 服务器历史图片保存天数
        /// </summary>
        public int? PolicyPark_SerImgDay { get; set; }
        /// <summary>
        /// 岗亭历史记录保存天数
        /// </summary>
        public int? PolicyPark_ClientRecordDay { get; set; }
        /// <summary>
        /// 岗亭历史图片保存天数
        /// </summary>
        public int? PolicyPark_ClientImgDay { get; set; }
        /// <summary>
        /// 图片储存路径
        /// </summary>
        public string PolicyPark_ImgPath { get; set; }
        /// <summary>
        /// 收费框可取消，1-允许取消操作，0-禁止取消操作,
        /// 出口弹出框后是否允许取消操作
        /// </summary>
        public int? PolicyPark_CollectAllowCancel { get; set; }
        /// <summary>
        /// 不同车道识别间隔时长(秒),在间隔时间内，同一个车牌在不同车道被识别时不处理。
        /// </summary>
        public int? PolicyPark_RememberTime { get; set; }
        /// <summary>
        /// 相同车道识别间隔时长(秒),在间隔时间内，同一个车牌在同一个车道被识别时不处理。
        /// </summary>
        public int? PolicyPark_RememberTime0 { get; set; }
        /// <summary>
        /// 黑白名单同步到相机功能，0-关闭，1-开启
        /// </summary>
        public string PolicyPark_IsSyncCamera { get; set; }
        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime? PolicyPark_Addtime { get; set; }
        /// <summary>
        /// 最后一次修改时间
        /// </summary>
        public DateTime? PolicyPark_Updatetime { get; set; }
        /// <summary>
        /// 车场收费金额小数位数,0,1,2
        /// </summary>
        public int? PolicyPark_Floatlen { get; set; }
        /// <summary>
        /// 无入场记录查找最近出场记录,0-禁用，1-启用
        /// </summary>
        public int? PolicyPark_FeeNoRecord { get; set; }
        /// <summary>
        /// 无入场记录查找最近出场记录的时长范围，单位小时
        /// </summary>
        public int? PolicyPark_FeeNoRecordTime { get; set; }
        /// <summary>
        /// 无入场记录显示停车时长，单位分钟
        /// </summary>
        public int? PolicyPark_NoRecordRangTime { get; set; }

        /// <summary>
        /// 无入场记录扫码获取订单方式，0-优先有无牌车、1-优先有牌车。默认优先有无牌车
        /// </summary>
        public int? PolicyPark_NoRecordGetMoney { set; get; }
        /// <summary>
        /// 登记场内车辆时清缴费用，0-禁用，1-启用
        /// </summary>
        public int? PolicyPark_PayedCharge { get; set; }
        /// <summary>
        /// 无牌车入场指定车牌颜色
        /// </summary>
        public string PolicyPark_DefaultNoneCarType { get; set; }
        /// <summary>
        /// 消防开闸，0-禁用，1-启用，（当有一个相机IN2接收到信息输入，所有的车道启用道闸常开）
        /// </summary>
        public int? PolicyPark_FireOpen { get; set; }

        /// <summary>
        /// 手机号码是否保密，0-不保密，1-保密
        /// </summary>
        public int? PolicyPark_PhoneSecret { get; set; }
        /// <summary>
        /// 试运营，0-禁用，1-启用（当启用试运营后，优先保存车辆通行记录，会自动开闸（启用该功能，出口无法使用电子支付））
        /// </summary>
        public int? PolicyPark_AutoTakerecord { get; set; }
        /// <summary>
        /// 多区域嵌套记录严格匹配:0-禁用，1-启用
        /// </summary>
        public int? PolicyPark_NestedRecords { get; set; }

        /// <summary>
        /// 是否创建0元支付订单
        /// </summary>
        public int? PolicyPark_CreateZero { set; get; }

        /// <summary>
        /// 临时车播报汉字：0-否，1-是
        /// </summary>
        public int? PolicyPark_TempChineseBroadcast { get; set; }

        /// <summary>
        /// 白牌车识别自动开闸，0-禁用 1-启用
        /// </summary>
        public int? PolicyPark_WhiteCar { get; set; }
        /// <summary>
        /// 军警车识别自动开闸，0-禁用 1-启用
        /// </summary>
        public int? PolicyPark_MilitaryPoliceCar { get; set; }

        /// <summary>
        /// 离线多长时间认定为脱机（单位：秒）
        /// </summary>
        public int? PolicyPark_OfflineTime { get; set; }

        /// <summary>
        /// 临时车脱机开闸，0-禁用 1-启用
        /// </summary>
        public int? PolicyPark_OfflineOpening { set; get; }

        /// <summary>
        /// 车场业务数据缓存：0-否，1-是【弃用，强制启用缓存】
        /// </summary>
        public int? PolicyPark_BusinessCache { set; get; }
        /// <summary>
        /// 离线支付，0-禁用，1-启用
        /// </summary>
        public int? PolicyPark_OfflinePay { set; get; }
        /// <summary>
        /// 以进外场时创建订单的车牌类型为准，0-禁用，1-启用
        /// </summary>
        public int? PolicyPark_EnterCarCardType { set; get; }
        /// <summary>
        /// 识别未支付记录，0-禁用，1-启用
        /// </summary>
        public int? PolicyPark_UnpaidRecord { set; get; }
        /// <summary>
        /// 出口识别放行，0-禁用，1-启用
        /// </summary>
        public int? PolicyPark_OutRecordFindEnble { set; get; }
        /// <summary>
        /// 出口识别放行时间
        /// </summary>
        public int? PolicyPark_OutRecordFindMin { set; get; }
        /// <summary>
        /// 自动追缴设置，0-禁用，1-启用
        /// </summary>
        public int? PolicyPark_AutoChase { set; get; }
        /// <summary>
        /// 教练车入场，0-禁用，1-启用
        /// </summary>
        public int? PolicyPark_CoachCar { set; get; }
        /// <summary>
        /// 教练车入场车牌类型
        /// </summary>
        public string PolicyPark_CoachCarType { set; get; }
        /// <summary>
        /// 教练车入场车牌颜色
        /// </summary>
        public string PolicyPark_CoachCarColor { set; get; }
        /// <summary>
        /// 无入场记录出场查找预入场记录，0-禁用，1-启用
        /// </summary>
        public int? PolicyPark_NoEnterRecordSearch { set; get; }
        /// <summary>
        /// 播报内容
        /// </summary>
        public string PolicyPark_BroadContent { set; get; }
        /// <summary>
        /// 超时计费以实际支付金额为准：0-禁用，1-启用
        /// </summary>
        public int? PolicyPark_OverTimePay { set; get; }
        /// <summary>
        /// 访客车辆预约时间内可进出次数
        /// </summary>
        public int? PolicyPark_VisitorTimes { set; get; }
        /// <summary>
        /// 商家车辆可往前登记开始时间
        /// </summary>
        public int? PolicyPark_BusinessStartTime { set; get; }

        /// <summary>
        /// 支付成功播报功能：0-禁用，1-启用
        /// </summary>
        public int? PolicyPark_PayedSuccessBroad { set; get; }
        
        /// <summary>
        /// 扫码登记入场自动放行：0-禁用，1-启用
        /// </summary>
        public int? PolicyPark_ScanEnter { set; get; }

        /// <summary>
        /// 同区域合并停车时长计费：0-禁用，1-启用
        /// </summary>
        public int? PolicyPark_MergeyHours { set; get; }

        /// <summary>
        /// 第三方账单追缴：0-禁用，1-启用
        /// </summary>
        public int? PolicyPark_EnbleOverdueBill { set; get; }

        /// <summary>
        /// 非法开闸忽略时间：软件下发开闸后，在指定秒数内收到非法开闸信号不处理，默认10秒
        /// </summary>
        public int? PolicyPark_IllegalGateIgnoreTime { set; get; }

        /// <summary>
        /// 场内月租转临停，出场需缴费：0-禁用，1-启用
        /// </summary>
        public int? PolicyPark_MonthlyToTempFee { set; get; }

        /// <summary>
        /// 月租转临停计费时间起点：1-按入场时间收费，2-按身份变更时间收费
        /// </summary>
        public int? PolicyPark_MonthlyToTempFeeTimeType { set; get; }
    }

    public class PolicyParkExt : PolicyPark
    {
    }

}
