var isLoadVideo = false;//是否加载视频流监控，当为false时加载抓拍图
var subsVideo = "0"; //是否启用辅相机视频：0-禁用
var isLoadData = false;
var modeConfig = {
    value: {
        isLoadVideo: true,
        subsVideo: "0",
        showModule: {
            right: true,
            monitor: true,
            record: true
        }
    },
    init: function () {
        //localStorage.setItem("modeConfig", JSON.stringify(modeConfig.value));
    },
    getConfig: function () {
        var con = localStorage.getItem("modeConfig");
        if (con != null) {
            try {
                return JSON.parse(con)
            } catch (e) {
                console.log("加载本地缓存[modeConfig]错误：" + e.message);
                con = '{"isLoadVideo":true,"subsVideo":"0","showModule":{"right":true,"monitor":true,"record":true}}';
                localStorage.setItem("modeConfig", con);
                return modeConfig.value;
            }
        } else {
            con = '{"isLoadVideo":true,"subsVideo":"0","showModule":{"right":true,"monitor":true,"record":true}}';
            localStorage.setItem("modeConfig", con);
            console.log("本地缓存[modeConfig]未设置,初始化");
            return JSON.parse(con);
        }

        console.log("本地缓存[modeConfig]" + modeConfig.value);
        return modeConfig.value;
    },
    setConfig: function (data) {
        var con = modeConfig.getConfig();
        if (data.isLoadVideo != null)
            con.isLoadVideo = data.isLoadVideo == 1;
        if (data.subsVideo != null)
            con.subsVideo = data.subsVideo;

        if (con.hasOwnProperty("showModule") && data.hasOwnProperty("showModule")) {
            if (data.showModule.right != null)
                con.showModule.right = data.showModule.right;
            if (data.showModule.monitor != null)
                con.showModule.monitor = data.showModule.monitor;
            if (data.showModule.record != null)
                con.showModule.record = data.showModule.record;
        } else {
            if (data.hasOwnProperty("showModule")) con["showModule"] = data.showModule;
        }

        localStorage.setItem("modeConfig", JSON.stringify(con));
    },
    reLoad: function () {
        if (isLoadVideo != modeConfig.getConfig().isLoadVideo || subsVideo != modeConfig.getConfig().subsVideo) {
            isLoadVideo = modeConfig.getConfig().isLoadVideo;
            subsVideo = modeConfig.getConfig().subsVideo;
            return false;
        }
        modeConfig.setConfig({});
        return true;
    },
    loadModule: function () {
        var con = modeConfig.getConfig();
        if (!con.hasOwnProperty("showModule")) {
            con.showModule = modeConfig.value.showModule;
        } else {
            modeConfig.value.showModule = con.showModule;
        }

        if (con.showModule.right) {
            $(".content .right").removeClass("layui-hide");
            $('.content .left').removeClass("rigthDidplay");
        } else {
            $(".content .right").addClass("layui-hide");
            $('.content .left').addClass("rigthDidplay");
        }

        if (con.showModule.monitor) {
            $(".content .monitor").removeClass("layui-hide");
            $('.content .record').css('top', '50%');
            try {
                if (pager && pager.bindVideo) pager.bindVideo();
            } catch (e) {
            }
        } else {
            $(".content .monitor").addClass("layui-hide").html("");
            $('.content .record').css('top', '0%');
            clearTimeout(time1);
            hidePlayers = [];
            videoobj.data = [
                { id: "video1", isload: false, model: {}, data: [] },
                { id: "video2", isload: false, model: {}, data: [] }
            ];

            for (var obj in players) {
                try {
                    if (obj["timer"]) {
                        clearTimeout(obj["timer"]);
                    }
                } catch (e) {
                }
            }
        }

        if (con.showModule.record) {
            $(".content .record").removeClass("layui-hide");
            $('.content .monitor').css('bottom', '50%');
        } else {
            $(".content .record").addClass("layui-hide");
            $('.content .monitor').css('bottom', '0%');
        }
    }
}

const isLoading = false;//是否加载中.
let players = [];//播放中的视频流
let initPromise = null;  // 初始化的 Promise，确保只会触发一次初始化
let deviceStatusCache = {};//保存当前的设备状态
//缓存当班收费数据
let lastWorkShiftData = null;
//缓存车位信息数据
let lastCarSpaceData = null;
// 云平台状态常量定义
const CloudStatus = { ONLINE: 1, OFFLINE: 0, DISABLED: -1 };
//缓存云端状态
let lastParkingState = null;
//缓存主机状态
let lastHostState = null;
//出口弹窗优惠券数据源
let CouponOutDataList = [];
//出口弹窗优惠券下拉列表
let selOutCoupon = null;
//出口弹窗车道编码
let selOutPasswayNo = null;
//出口弹窗订单编码
let selOutOrderNo = null;
//入口弹窗车道编码
let selInPasswayNo = null;
//入口弹窗订单编码
let selInOrderNo = null;

//更新云平台状态UI
function updateCloudStatusUI(statusClass, text) {
    const $cloud = $("#cloudOnline");
    $cloud.removeClass("close connet loading").addClass(statusClass);
    $cloud.find("dd").last().text(text);
}

// 防抖函数
function debounce(fn, delay) {
    let timer = null;
    return function () {
        const context = this;
        const args = arguments;
        clearTimeout(timer); // 清除前一个定时器
        timer = setTimeout(() => {
            fn.apply(context, args); // 执行目标函数
        }, delay);
    };
}

//绑定全局下拉框切换事件
var bindSelectChange = function () {
    layui.form.on("select", function (data) {

        if ($(data.elem).hasClass("video")) {

            var videoid = $(data.elem).attr("data-key");
            var modelno = $(data.elem).attr("data-modelno");
            if (modelno == data.value) return;  // 未改变

            $(data.elem).attr("data-modelno", data.value);

            var passway = null;
            pager.passwaydata.forEach(function (item, index) {
                if (item.Passway_No == data.value) {
                    passway = item;
                }
            });

            var has = false;
            videoobj.data.forEach(function (item, index) {
                if (item.isload && item.model.Passway_No == passway.Passway_No) {
                    has = true;
                }
            });

            if (videoobj.data[0].id == videoid) {
                if (has) videoobj.data[1].model = videoobj.data[0].model;
                videoobj.data[0].model = passway;
            }
            if (videoobj.data[1].id == videoid) {
                if (has) videoobj.data[0].model = videoobj.data[1].model;
                videoobj.data[1].model = passway;
            }

            if (has) {
                videoobj.reload();
                localCache.setCamera(videoobj.data);
            } else {
                $("#" + videoid).find(".item-btns button").attr("data-passwayno", data.value);
                $("#" + videoid).attr("data-passwayno", data.value);
                var message = "常开";
                if (passway.IsLongOpen == "1") {
                    message = "取消常开";
                }
                $("#" + videoid).find(".item-btns button[data-key='6']").text(message);

                var passwayList = [];
                $(".video").each(function () {
                    var selectedValue = $(this).val();
                    pager.passwaydata.forEach(function (item, index) {
                        if (item.Passway_No == selectedValue) {
                            passwayList.push(item);
                            return;
                        }
                    });
                });

                localCache.videodata = [];
                for (let i = 0; i < 2; i++) {
                    localCache.videodata.push({
                        id: `video${i + 1}`,
                        passwayno: passwayList[i]?.Passway_No || "" // 如果 passwayList[i] 不存在，使用空字符串
                    });
                }

                Promise.resolve(videoobj.init(pager.passwaydata)).then(() => {
                    videoobj.event();
                    localCache.save();
                });

                //设置设备状态
                if (LanInterval != null) {
                    clearInterval(LanInterval);
                    LanInterval = null;
                }
                LanInterval = setInterval(() => {
                    try {
                        pager.GetDeviceStatus();
                    } catch (e) { console.log("设置设备状态异常：" + e.message); }
                }, 1000);
            }
        }

        if ($(data.elem).hasClass("outcar_cartype") || $(data.elem).hasClass("out_carcardtype")) {
            var passway = pager.passwaydata.find((item) => { return item.Passway_No == selOutPasswayNo; });
            var param = HtmlParameter.getConfirmPassOut(selOutPasswayNo)
            if (param == null) return;

            if ($(data.elem).hasClass("outcar_cartype") && !formPower.ModifyTypeColor) {
                var targetObj = opsObj.data.find(item => item.id === selOutPasswayNo);
                if (targetObj) {
                    var carTypeObj = pager.cartypes.find(item => item.CarType_Name === targetObj.cartype);
                    if (carTypeObj) {
                        $(".outcar_cartype").val(carTypeObj.CarType_No).parent().find('input').val(targetObj.cartype);
                    }
                }
                layui.form.render('select');
                alerts.error("无权限修改车牌颜色");
                return;
            }

            if ($(data.elem).hasClass("out_carcardtype") && !formPower.ModifyType) {
                var targetObj = opsObj.data.find(item => item.id === selOutPasswayNo);
                if (targetObj) {
                    var carTypeObj = pager.cards.find(item => item.CarCardType_Name === targetObj.out_cardname);
                    if (carTypeObj) {
                        $(".out_carcardtype").val(carTypeObj.CarCardType_No).parent().find('input').val(targetObj.out_cardname);
                    }
                }
                layui.form.render('select');
                alerts.error("无权限修改车牌类型");
                return;
            }

            param.IsSound = 0;

            if ($(data.elem).hasClass("outcar_cartype")) {
                param.ParkOrder_CarType = data.value;
            } else if ($(data.elem).hasClass("outcar_carcardtype")) {
                param.ParkOrder_CarCardType = data.value;
            } else if ($(data.elem).hasClass("outcar_coupon")) {
                //param.CouponList = data.value;
                //暂时单选   
                var sltCoupon = pager.parkdiscountset.find((item, index) => { return item.CouponRecord_No == data.value; });
                if (sltCoupon == null) {
                    var op = opsObj.data.find((ops, i) => { return ops.id == selOutPasswayNo; });
                    if (op.out_coupon != null) { sltCoupon = op.out_coupon.find((item, index) => { return item.CouponRecord_No == data.value; }); }
                }

                var CouponList = [];
                if (sltCoupon != null) CouponList[CouponList.length] = sltCoupon;
                param.CouponList = JSON.stringify(CouponList);
            }

            var idx = layer.msg("正在重新计算费用...", { icon: 16, time: 0 });
            //改变车牌颜色,优惠券 重新计算价格
            $.post("GetParkOrder", { jsonModel: JSON.stringify(param) }, function (json) {
                layer.close(idx);
                if (json.success) {
                    if (pager && json.data && json.data.data) {
                        pager.calcDetail[selOutPasswayNo] = json.data.data.calcdetail;
                        if (data.payres && data.payres.payedmsg) { pager.payedmsg = data.payres.payedmsg; } else { pager.payedmsg = ""; }
                    } else { pager.calcDetail[selOutPasswayNo] = null; }
                    LPR.LPR_REFASH_FRAME(json.data, passway);
                } else {
                    alerts.error(json.msg);
                }
            }, "json").fail(function () {
                layer.close(idx);
                alerts.error("重新计算费用异常");
            });
        }
    });
}

//弹窗界面
var opsObj = {
    data: [],//数据队列
    wayUL: $(".way ul"),//弹窗菜单栏
    opsUL: $(".ops ul"),//弹窗内容栏
    update: function (id, isAutoWin) {

    },
    //绑定事件
    bindEvent: function () {
    },
    //根据停车订单创建队列数据包, passway=车道信息，obj=识别处理结果(为null时表示当前为手动输入)
    create: function (passway, obj, isAutoWin, cartype, carcardtype) {

        //console.log(obj)
        var res = opsObj.tmplitem();

        res.id = passway.Passway_No;
        res.type = passway.Passway_GateType;
        res.passwayname = passway.Passway_Name;
        res.passwaydutymode = passway.Passway_DutyMode;
        res.enter_unpaidresultamount = null;
        res.isAutoWin = isAutoWin;
        res.enter_card = carcardtype ?? "";
        res.out_card = carcardtype ?? "";
        res.enableCare = (obj?.enableCare) ?? false;

        var time = new Date().Format("yyyy-MM-dd hh:mm:ss");
        if (obj == null) {
            res.cartype = cartype;
            res.mode = 6;
            res.carno = pager.policypark.PolicyPark_CarPrefix || "";
            if (res.type != 0 && res.type != 3) {
                res.enter_passway = passway.Passway_Name;
                res.enter_area = passway.Passway_AreaCallName;
                res.enter_time = time;
            }
            else {
                res.out_passway = passway.Passway_Name;
                res.out_area = passway.Passway_AreaCallName;
                res.out_time = time;
            }
        }
        else {

            if (obj.data && obj.data != null && obj.data.unpaidresult != null && obj.data.passres && obj.data.passres.errmsg && obj.data.passres.errmsg != "") {
                if (!obj.enterramrk || obj.enterramrk == "") obj.enterramrk = obj.data.passres.errmsg;
                else obj.enterramrk = obj.data.passres.errmsg + "," + obj.enterramrk;
            }

            res.mode = obj.data.isVideoRecord ? 0 : (obj.data.recog && obj.data.recog != null && obj.data.recog.CarRecog_Mode == 2 ? 2 : 6);
            res.imgsmall = PathCheck(obj.data.recog.CarRecog_SmallImg);
            res.dataimgsmall = obj.data.recog.CarRecog_SmallImg;

            if (obj.data && obj.data.time && obj.data.passres && obj.data.passres.useDeviceTime && obj.data.time != null) time = obj.data.time;

            if (res.type != 0 && res.type != 3) {
                res.carno = obj.carno;
                res.orderno = obj.orderno;
                res.cartype = obj.cartype;

                res.enter_img = PathCheck(obj.enterimg);
                res.enter_dataimg = obj.enterimg;
                res.enter_card = obj.carcardtype;
                res.enter_passway = passway.Passway_Name;
                res.enter_area = passway.Passway_AreaCallName;
                res.enter_time = time;
                res.enter_remark = obj.enterramrk;

                if (obj.data && obj.data.passres.reminderinfo != null) {
                    res.enter_reminderinfo = obj.data.passres.reminderinfo;
                }

                if (res.enter_reminderinfo == null || res.enter_reminderinfo == "") {
                    if (obj.data.errmsg && obj.data.errmsg != null) res.enter_reminderinfo = obj.data.errmsg;
                }

                if (obj.data.unpaidresult != null) {
                    var unpaidMoeny = 0;
                    obj.data.unpaidresult.forEach(x => {
                        if (x.payres != null) unpaidMoeny += Number(x.payres.payedamount);
                    });
                    res.enter_unpaidresultamount = unpaidMoeny;
                    if (pager) { pager.calcDetail[passway.Passway_No] = obj.data.calcdetail; }
                }
            }
            else {

                if (passway.Passway_GateType == 0 || passway.Passway_GateType == 3) {
                    time = obj.outtime;
                }

                res.carno = obj.carno;
                res.orderno = obj.orderno;
                res.cartype = obj.cartype;

                var carOutOrder = {};
                var noenterrecod = false;
                if (obj.data != null && obj.data.resorder != null && obj.data.resorder.resOut != null) {
                    if (obj.data.resorder.resOut.parkorder != null) {
                        carOutOrder = obj.data.resorder.resOut.parkorder;
                        res.orderno = obj.data.resorder.resOut.parkorder.ParkOrder_No;
                    }
                    else {

                        if (res.type == 3 && obj.data.resorder.resOut.code == 4) { carOutOrder.ParkOrder_OutImgPath = obj.enterimg; }
                        else {
                            carOutOrder.ParkOrder_OutImgPath = obj.outimg;
                            if (obj.outimg == undefined || obj.outimg == "") {
                                carOutOrder.ParkOrder_OutImgPath = obj.enterimg;
                            }
                        }
                    }
                    if (obj.data.resorder.resOut.onenter == 0) noenterrecod = true;
                }

                res.enter_img = PathCheck(carOutOrder.ParkOrder_EnterImgPath);
                res.enter_dataimg = carOutOrder.ParkOrder_EnterImgPath;
                res.enter_card = carOutOrder.ParkOrder_CarCardTypeName;
                res.enter_passway = carOutOrder.ParkOrder_EnterPasswayName;
                res.enter_area = carOutOrder.ParkOrder_ParkAreaName;
                res.enter_time = carOutOrder.ParkOrder_EnterTime;
                res.enter_remark = carOutOrder.ParkOrder_EnterRemark;



                res.out_img = PathCheck(carOutOrder.ParkOrder_OutImgPath);
                res.out_dataimg = carOutOrder.ParkOrder_OutImgPath;
                res.out_passway = passway.Passway_Name;
                res.out_area = passway.Passway_AreaCallName;
                res.out_time = time;
                res.out_noincar = noenterrecod ? 1 : 0;

                res.out_cardname = obj.carcardtype;
                if (obj && obj.carcardtypeno && obj.carcardtypeno != "") res.out_card = obj.carcardtypeno;
                res.out_unpaidamount = 0;

                if (obj.data != null && obj.data.payres != null && obj.data.payres.payed != 2) {
                    res.out_orderamount = obj.data.payres.orderamount;
                    res.out_freeremark = null;
                    res.out_couponamount = obj.data.payres.couponamount;
                    res.out_coupon = obj.data.payres.recordlist;


                    if (obj.data.payres.uselist != null) {
                        res.out_couponno = obj.data.payres.uselist.length > 0 ? obj.data.payres.uselist[0].CouponRecord_No : "";
                        res.out_usecoupon = obj.data.payres.uselist;
                    }

                    res.out_chuzhiamount = obj.data.payres.chuzhiamount;
                    res.out_payedamount = obj.data.payres.payedamount;

                    if (isValidDate(obj.data.payres.inTime) && isValidDate(obj.data.payres.calctime)) {
                        res.out_parktimemin = getParkMin(obj.data.payres.inTime, obj.data.payres.calctime);
                    } else {
                        if (obj.data.payres.list != null && obj.data.payres.list.length > 0) {
                            var starttime = obj.data.payres.list[0].starttime;
                            var endtime = obj.data.payres.list[0].endtime;
                            if (isValidDate(starttime) && isValidDate(endtime)) {
                                res.out_parktimemin = getParkMin(starttime, endtime);
                            }
                        }
                    }

                    if (obj.data.unpaidresult != null) {
                        var unpaidMoeny = 0;
                        var unpaidOrderMoeny = 0;
                        var unpaidCZAmount = 0;
                        obj.data.unpaidresult.forEach(x => {
                            if (x.payres != null) { unpaidOrderMoeny += Number(x.payres.orderamount); unpaidMoeny += Number(x.payres.payedamount); unpaidCZAmount += Number(x.payres.chuzhiamount); }
                        });
                        res.out_unpaidamount = Number(unpaidOrderMoeny);
                        res.out_chuzhiamount = Number(res.out_chuzhiamount) + Number(unpaidCZAmount);
                        res.out_payedamount = Number(res.out_payedamount) + Number(unpaidMoeny);
                        res.out_orderamount = Number(res.out_orderamount) + Number(unpaidOrderMoeny);
                    }
                }


                if (obj.data && obj.data.passres.reminderinfo != null) {
                    res.out_reminderinfo = obj.data.passres.reminderinfo;
                }
                if (res.out_reminderinfo == null || res.out_reminderinfo == "") {
                    if (obj.data.errmsg && obj.data.errmsg != null) res.out_reminderinfo = obj.data.errmsg;
                }
            }
        }
        return res;
    },
    //追加到队列，obj=创建队列数据包, snap=是否需要抓拍图片：true,false
    append: function (obj, snap, isAutoWin) {
        var carnotoolid = null;

        var that = this;
        if (that.data.length == 0)
            that.data[that.data.length] = obj;
        else {
            opsObj.remove(obj.id);
            that.data[that.data.length] = obj;
        }

        var hasActive = false;
        var li = that.wayUL.find('li:not(.hide)').last();
        var newLi = null;
        if (li.length > 0) {
            hasActive = true;
            var newLi = li.next('li.hide');
            if (newLi.length > 0) {
                setLiAttributes(newLi, obj.id, obj.orderno); // 设置 data 属性并更新内容
                newLi.removeClass('hide').text(obj.passwayname);
            } else {
                newLi = $('<li></li>').text(obj.passwayname);
                setLiAttributes(newLi, obj.id, obj.orderno); // 设置 data 属性
                li.after(newLi);  // 在 li 后面插入新的 li 元素
            }
        } else {
            newLi = that.wayUL.find('li.hide').first();
            if (newLi.length > 0) {
                setLiAttributes(newLi, obj.id, obj.orderno); // 设置 data 属性并更新内容
                newLi.removeClass('hide').addClass('active').text(obj.passwayname);
            } else {
                newLi = $('<li class="active"></li>').text(obj.passwayname); // 创建新的 li 元素
                setLiAttributes(newLi, obj.id, obj.orderno); // 设置 data 属性
                that.wayUL.append(newLi);  // 添加新的 li 到 ul
            }
        }

        if (!isAutoWin) {
            $("li.active").removeClass("active");
            $(".ttubiao").remove();
            newLi.addClass("active");
            newLi.append('  <t class="ttubiao fa fa-taxi"></t>');
            hasActive = false;
        }

        $(".floatBottom").removeClass("layui-hide");
        if (obj.type == 1 || obj.type == 2) {
            //var opsLI = $("#opsliIn").tmpl([obj])[0].innerHTML;
            //$(that.opsUL).append(opsLI);

            //if (obj.enter_reminderinfo != '') {
            //    $("li[data-key=" + obj.id + "] div.reminderinfo").html('<t class="fa fa-exclamation-triangle"></t>' + obj.enter_reminderinfo + '')
            //}

            //carnotoolid = "enter_carno" + obj.id;
            //s_carno_picker.init(carnotoolid, function (text, carno) { }, "web").bindkeyup();

            //var imgTool = new imgtool();
            //imgTool.imageLoad("opsin" + obj.id, obj.enter_img, 0, $("li[data-key=" + obj.id + "] img.enter_img"), function () { $("li[data-key=" + obj.id + "] img.enter_img").attr("title", "入场图片").attr("src", obj.enter_img || '').attr("data-src", obj.enter_img || ''); imgTool.clear(); })

            if (!hasActive) {
                $(".ops .outwin_ul .out").removeClass("layui-hide").addClass('layui-hide');
                $(".ops .outwin_ul .in").removeClass("layui-hide");
                opsObj.setInWinValue(obj)
            } else {
                // 即使有其他活动弹窗，也要检查是否需要显示车辆到达特效
                if (isAtBottom) {
                    showVehicleArrivalEffect(obj, 'enter');
                }
            }
        }
        else if (obj.type == 0 || obj.type == 3) {
            //var opsLI = $("#opsliOut").tmpl([obj])[0].innerHTML;
            //$(that.opsUL).append(opsLI);

            //if (obj.out_reminderinfo != '') {
            //    $("li[data-key=" + obj.id + "] div.reminderinfo").html('<t class="fa fa-exclamation-triangle"></t>' + obj.out_reminderinfo + '')
            //}

            //carnotoolid = "outcar_carno" + obj.id;
            //$(".outcar_free").val($("#selFree").find("option").first().val());
            //var carobj = $('#' + carnotoolid);
            //s_carno_picker.init(carnotoolid, function (text, carno) { GetInCarList(carobj, carno); }, "web").bindkeyup();

            //var objId = obj.id;
            //var enterImg = obj.enter_img || '';
            //var outImg = obj.out_img || '';

            //var imgTool1 = new imgtool();
            //var imgTool2 = new imgtool();
            //imgTool1.imageLoad("opsoutin" + obj.id, obj.enter_img, 0, $("li[data-key=" + obj.id + "] img.entercar_img"), function () { $("li[data-key=" + objId + "] img.entercar_img").attr("title", "入场图片").attr("src", enterImg).attr("data-src", enterImg); imgTool1.clear(); })
            //imgTool2.imageLoad("opsout" + obj.id, obj.out_img, 0, $("li[data-key=" + obj.id + "] img.outcar_img"), function () { $("li[data-key=" + objId.id + "] img.outcar_img").attr("title", "入场图片").attr("src", outImg).attr("data-src", outImg); imgTool2.clear(); })
            if (!hasActive) {
                $(".ops .outwin_ul .in").removeClass("layui-hide").addClass('layui-hide');
                $(".ops .outwin_ul .out").removeClass("layui-hide");
                opsObj.setOutWinValue(obj)
            } else {
                // 即使有其他活动弹窗，也要检查是否需要显示车辆到达特效
                if (isAtBottom) {
                    showVehicleArrivalEffect(obj, 'exit');
                }
            }
        }

        opsObj.showCalcMsg(obj.id);

        obj = null;//清空

    },
    //从队列移除
    remove: function (id) {
        var that = this;

        try {
            // 1. 检查 id 是否有效
            if (!id) {  // 如果 id 是 null、undefined、空字符串等无效值
                console.log("Invalid ID:", id);
                return;
            }

            var item = null;
            var index = -1;

            // 2. 遍历数组，查找 id 对应的项
            for (var i = 0; i < that.data.length; i++) {
                if (that.data[i].id == id) {
                    item = that.data[i];
                    index = i;
                    break;  // 找到后跳出循环
                }
            }

            // 3. 如果找到了匹配的项
            if (item != null && index !== -1) {
                // 4. 在删除前，显式清除对该元素的引用
                that.data[index] = null;

                // 5. 从 data 数组中删除该项
                that.data.splice(index, 1);

                // 6. 从 DOM 中隐藏对应的列表项
                $(that.wayUL).find("li[data-key='" + id + "']").removeClass('active').addClass('hide').removeAttr("data-key").removeAttr("data-orderno");

                // 7. 查找是否还有其他非 hide 且 active 的 li
                var nextActiveLis = $(that.wayUL).find('li:not(.hide)');
                if (nextActiveLis.length > 0) {
                    // 如果存在不带 hide 类的 li 元素，检查其中是否有带 active 类的元素
                    var activeLi = nextActiveLis.filter('.active');
                    if (activeLi.length == 0) {
                        nextActiveLis.first().addClass('active').click();
                    }
                }

                if (that.data != null && that.data.length > 0) {
                    $(".floatBottom").removeClass("layui-hide");
                } else {
                    $(".floatBottom").removeClass("layui-hide").addClass("layui-hide");
                }

                // 8. 更新其他相关操作
                opsObj.update();
            }
        } catch (e) {
            console.log("Remove exception: " + e);
        }
    },
    //弹窗默认数据
    tmplitem: function () {
        var item = {
            id: null,//弹窗ID=车道编号
            type: 1, //0-出口，1-入口，2-内场出 / 入口
            mode: 0, //是否来源相机识别
            carno: '',//车牌号
            orderno: '',//停车订单号
            cartype: '',//车牌颜色编号
            passwayname: '',//当前车道名称
            passwaydutymode: 1,//值守模式，0-无人值守，1-有人值守
            imgsmall: '',//识别车牌小图加载路径
            dataimgsmall: '',//识别车牌小图保存路径

            enter_img: '',//入场图片加载路径
            enter_dataimg: '',//入场图片保存路径
            enter_card: '',//入场车牌类型编号
            enter_passway: '',//入场车道名称
            enter_area: '',//入场区域名称
            enter_time: '',//入场时间
            enter_remark: '',//入场备注
            enter_reminderinfo: '',//重要提示

            out_img: '',//出场图片加载路径
            out_dataimg: '',//出场图片保存路径
            out_passway: '',//出场车道名称
            out_area: '',//出场区域名称
            out_time: '',//出场时间
            out_noincar: 0,//是否无入场记录出场
            out_card: '',//出场车牌类型编号
            out_cardname: '',//出场车牌类型名称
            out_orderamount: 0,//出场订单金额
            out_freeremark: '',//出场免费原因
            out_couponamount: 0,//出场优惠金额
            out_coupon: [],//出场所有优惠券列表
            out_usecoupon: [],//出场已使用优惠券
            out_couponno: null,//出场已使用优惠券编号
            out_seldata: null,//出场弹窗下拉框
            out_chuzhiamount: 0,//出场已支付金额(储值车抵扣)
            out_scan: 0,//出场扫码支付金额
            out_payedamount: 0,//待支付金额
            out_parktimemin: '',//出场停车时长   
            out_unpaidamount: 0,//追缴金额
            isAutoWin: false, //是否WS消息自动弹窗
            out_reminderinfo: '',//重要提示
            enableCare: false,//是否关怀车辆
        };
        return item;
    },
    showCalcMsg: function (passwayno) {
        if (pager.calcDetail && pager.calcDetail[passwayno] && pager.calcDetail[passwayno] != null && pager.calcDetail[passwayno].length > 0) {
            try {
                var out_ishascar = pager.calcDetail[passwayno].find((m) => { return m.CalcDetail_CalcContent != null && m.CalcDetail_CalcContent.indexOf("有车位") > -1 }) != null;
                var out_isnonecar = pager.calcDetail[passwayno].find((m) => { return m.CalcDetail_CalcContent != null && m.CalcDetail_CalcContent.indexOf("多位多车") > -1 }) != null;
                var out_isovertime = pager.calcDetail[passwayno].find((m) => { return m.CalcDetail_IsOverTime != null && m.CalcDetail_IsOverTime == 1 }) != null;
                var out_isoverdue = pager.calcDetail[passwayno].find((m) => { return m.CalcDetail_IsCarExpire != null && m.CalcDetail_IsCarExpire == 1 }) != null;

                if (out_ishascar) { $("li.out[data-key=" + passwayno + "] span.out_ishascar").removeClass("hide"); }
                if (out_isnonecar) { $("li.out[data-key=" + passwayno + "] span.out_isnonecar").removeClass("hide"); }
                if (out_isovertime) { $("li.out[data-key=" + passwayno + "] span.out_isovertime").removeClass("hide"); }
                if (out_isoverdue) { $("li.out[data-key=" + passwayno + "] span.out_isoverdue").removeClass("hide"); }

                if (!out_isoverdue && !out_isovertime && !out_ishascar && !out_isnonecar) {
                    if (pager.payedmsg != null && pager.payedmsg != "") { $("li.out[data-key=" + passwayno + "] span.out_payedmag").removeClass("hide").html(pager.payedmsg); } else { $("li.out[data-key=" + passwayno + "] span.out_payedmag").html(""); }
                } else {
                    $("li.out[data-key=" + passwayno + "] span.out_payedmag").html("");
                }
            } catch (e) {
                console.log(e.message);
            }
        } else {
            $("li.out[data-key=" + passwayno + "] span.out_payedmag").removeClass("hide").html("");
            pager.payedmsg = "";
        }
    },

    setOutWinValue: function (data) {
        CacheSetOutWinValue(data);

        // 检查弹窗是否被缩小，如果是则显示车辆到达特效
        if (isAtBottom) {
            showVehicleArrivalEffect(data, 'exit');
        }

        // 检查是否为关怀车辆，如果是则显示关怀车辆提示
        if (data && data.enableCare === true) {
            showCareVehicleNotification(data, 'exit');
        }
    },
    setInWinValue: function (data) {
        CacheSetInWinValue(data);

        // 检查弹窗是否被缩小，如果是则显示车辆到达特效
        if (isAtBottom) {
            showVehicleArrivalEffect(data, 'enter');
        }

        // 检查是否为关怀车辆，如果是则显示关怀车辆提示
        if (data && data.enableCare === true) {
            showCareVehicleNotification(data, 'enter');
        }
    },

    openCalcDetail: function (id) {
        /*layer.closeAll();  // 先关闭之前的弹窗*/
        layer.open({
            title: "计费详情",
            type: 2,
            area: getIframeArea(['1000px', '620px']),
            fix: false, //不固定
            maxmin: false,
            content: '/Monitoring/PaymentDetail?passwayno=' + id
        });
    },

    clickWayUlLi: function (that) {

        $(".box .way ul li").removeClass("active");
        $(that).addClass("active");
        $(".box .way ul li:not(active) t.ttubiao").remove();
        $(that).append('  <t class="ttubiao fa fa-taxi"></t>');


        var key = $(that).attr("data-key");
        $(".ops .outwin_ul li").removeClass("layui-hide").addClass("layui-hide");
        console.log("key:" + key)
        var opsExist = opsObj.data.find((item, index) => { return (item.id == key); });
        console.log("clickWayUlLi:" + JSON.stringify(opsExist))
        if (opsExist != null) {
            if (opsExist.type == 1 || opsExist.type == 2) {
                $(".ops .outwin_ul li.in").removeClass("layui-hide");
                opsObj.setInWinValue(opsExist);
            } else {
                $(".ops .outwin_ul li.out").removeClass("layui-hide");
                opsObj.setOutWinValue(opsExist);
            }
            triggerLiFlashWithIcon($(".box").find("li.active"));
        }
    }
}

// 封装设置 li 元素的 data 属性和文本内容
function setLiAttributes(li, id, orderno) {
    $(li).attr('data-key', String(id));       // 设置 data-key 属性
    $(li).attr('data-orderno', String(orderno));  // 设置 data-orderno 属性
}

//出口弹窗赋值
const CacheSetOutWinValue = (function () {
    let cachedElements = null;

    // 维护事件处理器映射，避免重复绑定导致事件叠加
    const out_eventHandlerMap = new Map();
    // 封装一个 createOpsHandler(el) 返回稳定 handler 的方式
    const handlerCache = new Map();

    // 按钮事件工厂方法
    const opspasswaynohandlerFactory = (el) => createStableHandler(el, getSelOutPasswayNo, btns.onOpsClick);
    const opsordernohandlerFactory = (el) => createStableHandler(el, getselOutOrderNo, btns.onOpsClick);
    const voicehandlerFactory = (el) => createStableHandler(el, getSelOutPasswayNo, btns.showVoiceList);
    const calchandlerFactory = (el) => createStableHandler(el, getSelOutPasswayNo, opsObj.openCalcDetail);

    function getSelOutPasswayNo() {

        return selOutPasswayNo;
    }
    function getselOutOrderNo() {

        return selOutOrderNo;
    }

    // 创建稳定的事件处理函数，确保每个按钮只创建一次 handler
    function createStableHandler(el, getDynamicValue, callback) {
        if (!handlerCache.has(el)) {
            const handler = () => callback(getDynamicValue(), el);
            handlerCache.set(el, handler);
        }
        return handlerCache.get(el);
    }

    function updateButtonHandler(buttonEl, createHandlerFn, show = true) {
        if (!buttonEl) return;

        if (show) {
            buttonEl.classList.remove('layui-hide');
        } else {
            buttonEl.classList.add('layui-hide');
            return;
        }

        let handler;
        if (!handlerCache.has(buttonEl)) {
            handler = createHandlerFn(buttonEl);
            handlerCache.set(buttonEl, handler);
        } else {
            handler = handlerCache.get(buttonEl);
        }

        bindButtonHandler(buttonEl, 'click', handler);
    }

    // 安全绑定事件，避免重复绑定
    function bindButtonHandler(buttonEl, eventType, handler) {
        if (!buttonEl) return;

        const oldHandler = out_eventHandlerMap.get(buttonEl);
        if (oldHandler !== handler) {// 解绑之前的 handler
            if (oldHandler) {
                $(buttonEl).off(eventType, oldHandler);
            }
            out_eventHandlerMap.set(buttonEl, handler);
            $(buttonEl).on(eventType, handler);
        }
    }

    /**
     * 初始化并缓存所有需要操作的子元素
     * @returns {Object|null} 返回缓存的子元素对象或 null
     */
    function initializeCachedElements() {
        const container = document.querySelector(`.ops .outwin_ul .out`);
        if (!container) return null; // 如果容器不存在，则返回 null

        // 缓存所有需要操作的子元素
        return {
            container,// 包含 data-key 和 data-orderno 的元素
            enterImg: container.querySelector(".entercar_img"), // 入口图片元素
            outImg: container.querySelector(".outcar_img"), // 出口图片元素
            carnoInput: container.querySelector(".outcar_carno"), // 车牌号输入框
            searchcarnoInput: container.querySelector(".btnsearch"), // 查询车牌号按钮
            enterPassway: container.querySelector(".enter_passway"), // 入口通道
            enterArea: container.querySelector(".enter_area"), // 入口区域
            enterTime: container.querySelector(".enter_time"), // 入口时间
            outPassway: container.querySelector(".out_passway"), // 出口通道
            outArea: container.querySelector(".out_area"), // 出口区域
            outTime: container.querySelector(".out_time"), // 出口时间
            orderAmount: container.querySelector('.orderamount'), // 应收金额
            couponAmount: container.querySelector('.out_couponamount'), // 优惠金额
            outcoupon: container.querySelector('.div_out_coupon'), // 优惠金额相关的 div
            out_chuzhiamount: container.querySelector('.out_chuzhiamount'), // 已收金额
            out_carcardtype: container.querySelector('.out_carcardtype'), // 车型
            out_parktimemin: container.querySelector('.out_parktimemin'), // 计费时长
            outcar_money: container.querySelector('.outcar_money'), // 实际支付金额元素
            out_enterpass: container.querySelector('.out_enterpass'), // 确认放行按钮
            out_enterfreepass: container.querySelector('.out_enterfreepass'), // 免费放行按钮
            out_print: container.querySelector('.out_print'), // 打印按钮
            out_cancel: container.querySelector('.out_cancel'), // 取消按钮
            out_video: container.querySelector('.out_video'), // 点击播报按钮
            out_detail: container.querySelector('.out_detail'), // 订单详情
            out_awaysvoice: container.querySelector('.out_awaysvoice'), // 常用语音
            out_morespace: container.querySelector('.out_morespace'), // 多车多位
            payedAmount: container.querySelector('[data-id="payedamount"]'), // 实际支付金额输入框
            out_isnonecar: container.querySelector(".out_isnonecar"), // 非车牌图标
            out_isovertime: container.querySelector(".out_isovertime"), // 超时图标
            out_isoverdue: container.querySelector(".out_isoverdue"), // 逾期图标
            reminderInfo: container.querySelector(".reminderinfo"), // 提醒信息
            question: container.querySelector(".question"), // 提问按钮
            outcar_free: container.querySelector(".outcar_free"), // 免费原因
        };
    }

    // 根据属性类型更新元素内容或属性
    function updateElement(el, prop, val) {
        if (!el) return;
        if (prop === 'value') el.value = val || '';
        else if (prop === 'src') el.src = val || '../Static/img/img-nothing.png';
        else if (prop === 'html') el.innerHTML = val || '';
        else if (prop === 'textContent' || prop === 'text') el.textContent = val || '';
        else el[prop] = val || '';
    }

    // 图片加载函数，使用 imgtool 工具加载图片
    function loadImage(id, url, imgEl, callback) {
        if (!url) {
            updateElement(imgEl, 'src', '../Static/img/img-nothing.png');
            if (callback) callback();
            return;
        }
        const imgToolInstance = new imgtool();
        imgToolInstance.imageLoad(id, url, 0, imgEl, () => {
            updateElement(imgEl, 'src', url);
            if (imgEl) imgEl.dataset.src = url;
            imgToolInstance.clear();
            if (callback) callback();
        });
    }

    return function (data) {
        if (!cachedElements) {
            cachedElements = initializeCachedElements(); // 获取并缓存元素
            if (!cachedElements) return; // 如果容器不存在，则退出函数
        }

        const {
            container, enterImg, outImg, carnoInput, searchcarnoInput, enterPassway, enterArea,
            enterTime, outPassway, outArea, outTime, orderAmount, couponAmount, outcoupon, out_print,
            out_chuzhiamount, out_carcardtype, out_parktimemin, outcar_money, out_enterpass,
            out_enterfreepass, out_cancel, out_video, payedAmount, out_isnonecar, out_isovertime,
            out_isoverdue, reminderInfo, question, out_detail, out_awaysvoice, out_morespace, outcar_free
        } = cachedElements;

        // 设置 li 的数据属性
        if (container) {
            container.dataset.key = data.id || '';
            container.dataset.orderno = data.orderno || '';
        }


        // 加载入口车辆图片
        loadImage("opsout_1_" + data.id, data.enter_img, enterImg, null);
        // 加载出口车辆图片
        loadImage("opsout_2_" + data.id, data.out_img, outImg, null);

        // 更新车牌号输入框
        updateElement(carnoInput, 'value', data.carno);
        if (searchcarnoInput) searchcarnoInput.dataset.passwayno = String(data.id);

        // 更新入口通道文字
        updateElement(enterPassway, 'textContent', data.enter_passway);
        // 更新入口区域文字
        updateElement(enterArea, 'textContent', data.enter_area);
        // 更新入口时间
        updateElement(enterTime, 'textContent', data.enter_time);
        // 更新出口通道
        updateElement(outPassway, 'textContent', data.out_passway);
        // 更新出口区域
        updateElement(outArea, 'textContent', data.out_area);
        // 更新出口时间
        updateElement(outTime, 'textContent', data.out_time);


        // 显示订单金额，并显示是否有追缴金额
        if (orderAmount) {
            let text = data.out_orderamount?.toFixed(2) || '';
            if (data.out_unpaidamount && data.out_unpaidamount > 0) {
                text += ` (包含追缴金额 ${data.out_unpaidamount.toFixed(2)} 元)`;
            }
            updateElement(orderAmount, 'textContent', text);
        }

        selOutPasswayNo = data.id;
        selOutOrderNo = data.orderno;

        // 更新优惠券金额、储值金额、车型、停车时长等显示
        updateElement(couponAmount, 'textContent', data.out_couponamount?.toFixed(2));
        updateElement(out_chuzhiamount, 'textContent', data.out_chuzhiamount?.toFixed(2));
        //updateElement(out_carcardtype, 'textContent', data.out_cardname);
        updateElement(out_parktimemin, 'textContent', data.out_parktimemin);

        // 更新实际支付金额
        if (outcar_money) {
            updateElement(outcar_money, 'textContent', data.out_payedamount?.toFixed(2));
            outcar_money.setAttribute("onkeypress", `getkey('${data.id}', this)`);
            outcar_money.dataset.no = String(data.id);
        }

        // 绑定出口操作按钮事件：确定放行、免费放行、取消、播报、订单详情、多车多位
        [
            out_enterpass,
            out_enterfreepass,
            out_cancel,
            out_video,
        ].forEach(el => {
            if (el) {
                updateButtonHandler(el, opspasswaynohandlerFactory, true);
            }
        });
        // 绑定出口操作按钮事件：订单详情、多车多位
        [
            out_detail,
            out_morespace
        ].forEach(el => {
            if (el) {
                updateButtonHandler(el, opsordernohandlerFactory, true);
            }
        });

        // 更新打印按钮的事件
        updateButtonHandler(out_print, opspasswaynohandlerFactory, formPower && formPower.PrintReceipt);

        // 更新常用语音按钮的事件
        updateButtonHandler(out_awaysvoice, voicehandlerFactory, true);

        // 更新问题按钮的事件
        updateButtonHandler(question, calchandlerFactory, true);

        // 更新实际支付金额输入框的值
        updateElement(payedAmount, 'value', Number(data.out_payedamount || 0).toFixed(2));

        // 控制各种状态图标显示或隐藏：车牌图标、超时图标、逾期图标
        if (out_isnonecar) out_isnonecar.classList.toggle("hide", !data.out_isnonecar);
        if (out_isovertime) out_isovertime.classList.toggle("hide", !data.out_isovertime);
        if (out_isoverdue) out_isoverdue.classList.toggle("hide", !data.out_isoverdue);

        // 添加关怀车辆标识
        addCareVehicleBadge(container, data.enableCare, 'out');

        // 更新提醒信息
        if (reminderInfo) updateElement(reminderInfo, 'html', data.out_reminderinfo && data.out_reminderinfo != "" ? ('<t class="fa fa-exclamation-triangle"></t>' + data.out_reminderinfo + '') : "");

        // 样式和控件状态初始化
        $(".pwd-edit").removeClass("fa-rotate-left").removeClass("fa-pencil").addClass("fa-pencil");
        $(".action-popup").removeClass("layui-hide").addClass("layui-hide");
        $(".more-actions >.moreicon").removeClass("layui-hide");
        $("input.outcar_money").attr("readonly", true);
        $(".outcar_free").val("");

        // 车类型匹配并更新对应控件
        var carTypeObj = pager.cartypes.find(item => item.CarType_Name === data.cartype);
        if (carTypeObj) {
            $(".outcar_cartype").val(carTypeObj.CarType_No).parent().find('input').val(data.cartype);

        }

        //设置车牌类型
        if (data.out_card && (data.out_card != "" || data.out_cardname != "")) {
            var carCardTypeObj = pager.cards.find(item => item.CarCardType_No === data.out_card || item.CarCardType_Name === data.out_cardname);
            if (carCardTypeObj) {
                $(".out_carcardtype").val(carCardTypeObj.CarCardType_No).parent().find('input').val(carCardTypeObj.CarCardType_Name);
            } else {
                alerts.error("车牌类型信息异常");
            }
        }

        $('.outcar_noincar').prop("checked", !!data.out_noincar).removeAttr("disabled");
        layui.form.render('checkbox');

        // 更新免费原因
        updateElement(outcar_free, 'value', pager.outremarks?.[0] ?? "");

        //优惠券下拉列表赋值
        if (1 == 1) {

            var CouponInfoList = data.out_coupon;
            if (CouponInfoList == null || CouponInfoList.length == 0) { CouponInfoList = pager.parkdiscountset; }
            else if (pager.parkdiscountset && pager.parkdiscountset != null || pager.parkdiscountset.length > 0) {
                for (var i = 0; i < pager.parkdiscountset.length; i++) {
                    CouponInfoList.push(pager.parkdiscountset[i]);
                }
            }
            //过滤CouponRecord_No重复
            if (CouponInfoList != null && CouponInfoList.length > 0) {
                const seen = new Set();
                const filteredList = CouponInfoList.filter(coupon => {
                    // 如果 CouponRecord_No 还没有被记录，就添加到 Set 并保留该元素
                    if (!seen.has(coupon.CouponRecord_No)) {
                        seen.add(coupon.CouponRecord_No);
                        return true;
                    }
                    // 如果 CouponRecord_No 已存在于 Set 中，过滤掉这个元素
                    return false;
                });
                CouponInfoList = filteredList;
            }

            // 判断 CouponOutDataList 与 CouponInfoList 是否相同
            var isSameData = CouponOutDataList.length === CouponInfoList.length &&
                CouponOutDataList.every((outCoupon, index) => {
                    return outCoupon.value === CouponInfoList[index].CouponRecord_No;
                });

            // 如果数据源不一样，重新渲染；如果一样，则只重新设置值
            if (!isSameData) {

                // 数据源不一样，重新渲染
                var newlist = [];
                for (var i = 0; i < CouponInfoList.length; i++) {
                    newlist[i] = {
                        "name": CouponInfoList[i].CouponRecord_Name,
                        "value": CouponInfoList[i].CouponRecord_No
                    };
                }

                CouponOutDataList = newlist;

                // 闭包，传递当前的 passway.Passway_No
                (function (CouponInfoList, newlist) {
                    selOutCoupon = xmSelect.render({
                        el: '#div_out_coupon',
                        name: 'div_out_coupon',
                        layVerify: 'required',
                        layVerType: 'msg',
                        filterable: false,
                        height: '130px',
                        max: policyCount,
                        data: newlist,
                        on: function (d) {
                            var arr = d.arr;
                            var couponIDes = "";
                            var index = 0;
                            $.each(arr, function (n, v) {
                                couponIDes += v.value + ",";
                                index++;
                            })

                            if (index > policyCount) {
                                layer.msg("优惠券一次允许使用" + policyCount + "张，勿超过策略使用限制!", { icon: 0, btn: ['确定'], time: 0 });
                                return;
                            }
                            couponIDes = couponIDes.substring(0, couponIDes.length - 1);



                            var passway = pager.passwaydata.find((item) => { return item.Passway_No == selOutPasswayNo; });
                            var param = HtmlParameter.getConfirmPassOut(selOutPasswayNo);
                            if (param == null) return;

                            var CouponList = [];
                            if (couponIDes == "") {
                                CouponList = '[]';
                            } else {
                                if (CouponInfoList != null) {
                                    CouponInfoList.forEach((item, index) => {
                                        if (couponIDes.indexOf(item.CouponRecord_No) >= 0) {
                                            CouponList[CouponList.length] = item;
                                        }
                                    });
                                }
                            }
                            param.IsSound = 0;
                            var targetObj = opsObj.data.find(item => item.id === selOutPasswayNo);
                            targetObj.out_seldata = CouponList;

                            param.CouponList = JSON.stringify(CouponList);

                            var idx = layer.msg("正在重新计算费用...", { icon: 16, time: 0 });
                            $.post("GetParkOrder", { jsonModel: JSON.stringify(param) }, function (json) {
                                layer.close(idx);
                                if (json.success) {
                                    if (pager && json.data && json.data.data) {
                                        pager.calcDetail[selOutPasswayNo] = json.data.data.calcdetail;
                                        if (d.payres && data.payres.payedmsg) { pager.payedmsg = d.payres.payedmsg; } else { pager.payedmsg = ""; }
                                    } else { pager.calcDetail[selOutPasswayNo] = null; }

                                    LPR.LPR_REFASH_FRAME(json.data, passway);
                                } else {
                                    alerts.error(json.msg);
                                }
                            }, "json").fail(function () {
                                layer.close(idx);
                                alerts.error("重新计算费用异常");
                            });
                        }
                    });

                    $('.xm-option').off('click').on('click', function () {
                        // 获取当前已选中的数量
                        let selectedValues = selOutCoupon.getValue();
                        let selectedCount = selectedValues.length;  // 已选中数量
                        if (selectedCount >= policyCount) {
                            setTimeout(function () {
                                $("#div_out_coupon").css({
                                    'box-shadow': '0 0 20px red'
                                });
                                setTimeout(function () {
                                    $("#div_out_coupon").css({
                                        'box-shadow': 'none'
                                    });
                                }, 1000);
                            }, 100)
                        }
                    });


                    if (data.out_usecoupon && data.out_usecoupon != null && data.out_usecoupon.length > 0) {
                        var couponNolist = [];
                        for (var i = 0; i < data.out_usecoupon.length; i++) {
                            couponNolist.push(data.out_usecoupon[i].CouponRecord_No)

                            var coupon = CouponInfoList.find((item, index) => { return (item.CouponRecord_No == data.out_usecoupon[i].CouponRecord_No); });
                            if (coupon != null) {
                                if (data.out_seldata == null) data.out_seldata = [];
                                data.out_seldata.push(coupon);
                            }
                        }
                        selOutCoupon.setValue(couponNolist);
                    }
                })(CouponInfoList, newlist);
            } else {
                // 数据源相同，直接设置值
                var couponNolist = [];
                var targetObj = opsObj.data.find(item => item.id === selOutPasswayNo);
                if (targetObj.out_usecoupon && targetObj.out_usecoupon.length > 0) {
                    for (var i = 0; i < targetObj.out_usecoupon.length; i++) {
                        couponNolist.push(targetObj.out_usecoupon[i].CouponRecord_No);
                    }
                }
                if (selOutCoupon) selOutCoupon.setValue(couponNolist);
            }
        }
    };
})();
//入口弹窗赋值
const CacheSetInWinValue = (function () {
    // 缓存DOM元素，避免重复查询
    let cachedElements = null;

    // 维护事件处理器映射，避免重复绑定导致事件叠加
    const in_eventHandlerMap = new Map();
    // 封装一个 createOpsHandler(el) 返回稳定 handler 的方式
    const handlerCache = new Map();

    // 按钮事件工厂方法
    const opspasswaynohandlerFactory = (el) => createStableHandler(el, getSelInPasswayNo, btns.onOpsClick);
    const opsordernohandlerFactory = (el) => createStableHandler(el, getselInOrderNo, btns.onOpsClick);
    const voicehandlerFactory = (el) => createStableHandler(el, getSelInPasswayNo, btns.showVoiceList);
    const calchandlerFactory = (el) => createStableHandler(el, getSelInPasswayNo, opsObj.openCalcDetail);

    function getSelInPasswayNo() {

        return selInPasswayNo;
    }
    function getselInOrderNo() {

        return selInOrderNo;
    }

    // 创建稳定的事件处理函数，确保每个按钮只创建一次 handler
    function createStableHandler(el, getDynamicValue, callback) {
        if (!handlerCache.has(el)) {
            const handler = () => callback(getDynamicValue(), el);
            handlerCache.set(el, handler);
        }
        return handlerCache.get(el);
    }

    function updateButtonHandler(buttonEl, createHandlerFn, show = true) {
        if (!buttonEl) return;

        if (show) {
            buttonEl.classList.remove('layui-hide');
        } else {
            buttonEl.classList.add('layui-hide');
            return;
        }

        let handler;
        if (!handlerCache.has(buttonEl)) {
            handler = createHandlerFn(buttonEl);
            handlerCache.set(buttonEl, handler);
        } else {
            handler = handlerCache.get(buttonEl);
        }

        bindButtonHandler(buttonEl, 'click', handler);
    }

    // 安全绑定事件，避免重复绑定
    function bindButtonHandler(buttonEl, eventType, handler) {
        if (!buttonEl) return;

        const oldHandler = in_eventHandlerMap.get(buttonEl);
        if (oldHandler !== handler) {// 解绑之前的 handler
            if (oldHandler) {
                $(buttonEl).off(eventType, oldHandler);
            }
            in_eventHandlerMap.set(buttonEl, handler);
            $(buttonEl).on(eventType, handler);
        }
    }

    /**
     * 初始化并缓存所有需要操作的子元素
     * @returns {Object|null} 返回缓存的子元素对象或 null
     */
    function initializeCachedElements() {
        const item = document.querySelector(`.ops .outwin_ul .in`);
        if (!item) return null; // 如果容器不存在，则返回 null

        // 缓存所有需要操作的子元素
        return {
            inli: item,// 包含 data-key 和 data-orderno 的元素
            enterImg: item.querySelector(".enter_img"), // 入口图片元素
            imgsmall: item.querySelector(".imgsmall"), // 出口图片元素
            enter_carno: item.querySelector(".enter_carno"), // 车牌号输入框
            enter_area: item.querySelector(".enter_area"), // 区域
            passwayname: item.querySelector(".passwayname"), // 放行车道
            enter_remark: item.querySelector(".enter_remark"), // 入场备注
            enter_time: item.querySelector(".enter_time"), // 通行时间
            vUnpaid: item.querySelector(".vUnpaid"), // 补缴金额
            in_enterpass: item.querySelector('.in_enterpass'), // 确认放行按钮
            in_print: item.querySelector('.in_print'), // 打印按钮
            in_cancel: item.querySelector('.in_cancel'), // 取消按钮
            question: item.querySelector(".question"), // 提问按钮
            reminderInfo: item.querySelector(".reminderinfo"), // 提醒信息
            enter_awaysvoice: item.querySelector('.enter_awaysvoice'), // 常用语音
            enter_morespace: item.querySelector('.enter_morespace'), // 多车多位

        };
    }

    /**
     * 更新指定元素的内容或属性
     * @param {HTMLElement} el - 目标元素
     * @param {string} prop - 要更新的属性类型，例如 'value', 'src', 'html', 'text'
     * @param {string} value - 要设置的值
     */
    function updateElement(el, prop, value) {
        if (!el) return;
        switch (prop) {
            case 'value':
                el.value = value || '';
                break;
            case 'src':
                el.src = value || "../Static/img/img-nothing.png"; // 无图时使用默认占位图
                break;
            case 'html':
                el.innerHTML = value || '';
                break;
            case 'text':
                el.textContent = value || '';
                break;
            default:
                el[prop] = value || '';
        }
    }

    /**
     * 给 li 元素设置自定义属性
     * @param {HTMLElement} el - 目标 li 元素
     * @param {string|number} id - 传入数据的ID
     * @param {string|number} orderno - 传入数据的订单号
     */
    function setLiAttributes(el, id, orderno) {
        if (el) {
            el.setAttribute("data-key", id || "");
            el.setAttribute("data-orderno", orderno || "");
        }
    }

    return function (data) {
        if (!cachedElements) {
            cachedElements = initializeCachedElements(); // 获取并缓存元素
            if (!cachedElements) return; // 如果容器不存在，则退出函数
        }

        const {
            inli, enterImg, enter_carno, passwayname, enter_area, enter_remark, enter_time, in_print,
            vUnpaid, in_enterpass, in_cancel, question, reminderInfo, enter_awaysvoice, enter_morespace
        } = cachedElements;

        // 设置 li 的数据属性
        if (inli) {
            setLiAttributes(inli, data.id, data.orderno)
        }

        selInPasswayNo = data.id;
        selInOrderNo = data.orderno;

        if (data.enter_img && data.enter_img != '') {
            // 更新入口图片
            var imgTool = new imgtool();
            imgTool.imageLoad("opsin" + data.id, data.enter_img, 0, enterImg,
                function () {
                    updateElement(enterImg, 'src', data.enter_img); // 设置图片路径
                    if (enterImg) enterImg.dataset.src = data.enter_dataimg || ""; // 设置数据属性
                    imgTool.clear();
                })
        } else {
            // 没有图片显示默认占位
            updateElement(enterImg, 'src', null);
        }

        // 更新车牌号
        if (enter_carno) updateElement(enter_carno, 'value', data.carno); // 设置车牌号

        // 更新入口通道文字
        if (passwayname) updateElement(passwayname, 'textContent', data.enter_passway);

        // 更新入口时间
        if (enter_time) updateElement(enter_time, 'textContent', data.enter_time);

        // 更新入口备注
        if (enter_remark) updateElement(enter_remark, 'value', data.enter_remark);

        //更新入口区域
        if (enter_area) updateElement(enter_area, 'textContent', data.enter_area);

        // 更新提醒信息
        if (reminderInfo) updateElement(reminderInfo, 'html', data.enter_reminderinfo && data.enter_reminderinfo != '' ? ('<t class="fa fa-exclamation-triangle"></t>' + data.enter_reminderinfo + '') : "");

        // 添加关怀车辆标识
        addCareVehicleBadge(inli, data.enableCare, 'in');

        //更新车牌颜色
        //updateElement(out_carcardtype, 'textContent', data.out_cardname);

        // 绑定入口操作按钮事件：确定放行、取消
        [
            in_enterpass,
            in_cancel,
        ].forEach(el => {
            if (el) {
                updateButtonHandler(el, opspasswaynohandlerFactory, true);
            }
        });

        // 更新打印按钮的事件
        updateButtonHandler(in_print, opspasswaynohandlerFactory, formPower && formPower.PrintReceipt);

        // 更新常用语音按钮的事件
        updateButtonHandler(enter_awaysvoice, voicehandlerFactory, true);

        // 更新多车多位按钮的事件
        updateButtonHandler(enter_morespace, opsordernohandlerFactory, true);

        // 更新问题按钮的事件
        updateButtonHandler(question, calchandlerFactory, true);

        //设置车牌颜色
        if (data.cartype && data.cartype != "") {
            var carTypeObj = pager.cartypes.find(item => item.CarType_Name === data.cartype);
            if (carTypeObj) {
                $(".enter_cartype").val(carTypeObj.CarType_No).parent().find('input').val(data.cartype);

            }
        }

        //设置车牌类型
        if (data.enter_card && data.enter_card != "") {
            var carCardTypeObj = pager.cards.find(item => item.CarCardType_Name === data.enter_card);
            if (carCardTypeObj) {
                $(".enter_carcardtype").val(carCardTypeObj.CarCardType_No).parent().find('input').val(carCardTypeObj.CarCardType_Name);
            }
        }

        // 修改入场车牌颜色的权限控制
        if (!formPower.ModifyTypeColor) {
            // 禁用车牌颜色选择控件
            $(".enter_cartype").attr("disabled", "disabled");

            // 阻止下拉框点击事件
            $(".enter_cartype").parent().find('input, .layui-edge, .layui-anim').on('click', function (e) {
                e.preventDefault();
                e.stopPropagation();
                alerts.error("无权限修改车牌颜色");
                layui.form.render('select');
                return false;
            });

            // 监听 form 的 select 事件，阻止值变化
            layui.form.on('select(enter_cartype)', function (data) {
                if (!formPower.ModifyTypeColor) {
                    var targetObj = opsObj.data.find(item => item.id === selInPasswayNo);
                    if (targetObj) {
                        var carTypeObj = pager.cartypes.find(item => item.CarType_Name === targetObj.cartype);
                        if (carTypeObj) {
                            $(".enter_cartype").val(carTypeObj.CarType_No);
                            layui.form.render('select');
                        }
                    }
                    alerts.error("无权限修改车牌颜色");
                    return false;
                }
            });
        } else {
            $(".enter_cartype").removeAttr("disabled");
        }

        // 修改入场车牌类型的权限控制
        if (!formPower.ModifyType) {
            // 禁用车牌颜色选择控件
            $(".enter_carcardtype").attr("disabled", "disabled");

            // 阻止下拉框点击事件
            $(".enter_carcardtype").parent().find('input, .layui-edge, .layui-anim').on('click', function (e) {
                e.preventDefault();
                e.stopPropagation();
                alerts.error("无权限修改车牌类型");
                layui.form.render('select');
                return false;
            });

            // 监听 form 的 select 事件，阻止值变化
            layui.form.on('select(enter_carcardtype)', function (data) {
                if (!formPower.ModifyType) {
                    var targetObj = opsObj.data.find(item => item.id === selInPasswayNo);
                    if (targetObj) {
                        var carTypeObj = pager.cards.find(item => item.CarCardType_No === targetObj.enter_card);
                        if (carTypeObj) {
                            $(".enter_carcardtype").val(carTypeObj.CarCardType_No);
                            layui.form.render('select');
                        }
                    }
                    alerts.error("无权限修改车牌类型");
                    return false;
                }
            });
        } else {
            $(".enter_carcardtype").removeAttr("disabled");
        }

        $(".pwd-edit").removeClass("fa-rotate-left").removeClass("fa-pencil").addClass("fa-pencil");
        $(".action-popup").removeClass("layui-hide").addClass("layui-hide");
    };
})();

//无入场记录启用/取消
var noInRecord = false;
//无入场记录
var chkNothingRecord = function (data) {
    var passway = pager.passwaydata.find((item) => { return item.Passway_No == selOutPasswayNo; });
    var param = HtmlParameter.getConfirmPassOut(selOutPasswayNo)
    if (param == null) {
        $(data.elem).prop("checked", false);
        layui.form.render();
        return;
    }
    var idx = layer.msg("无入场记录处理中...", { icon: 16, time: 0 });
    $(data.elem).attr("disabled", true);
    if (data.value == 'on') {
        $.post("EnableNoEnterRecord", { jsonModel: JSON.stringify(param) }, function (json) {
            layer.close(idx);
            if (json.success) {
                alerts.success("无入场记录校验完成");
                LPR.LPR_OPEN_FRAME(json.data, passway);
                LPR.RECOGRESULT(json.data, passway);
            } else {
                alerts.error(json.msg);
                $(data.elem).prop("checked", false);
                layui.form.render();
            }
            $(data.elem).removeAttr("checked");
            layui.form.render();
        }, "json").fail(function () {
            alerts.error("无入场记录处理异常");
            $(data.elem).removeAttr("checked");
            layui.form.render();
        });
    } else {
        $.post("CancelNoEnterRecord", { jsonModel: JSON.stringify(param) }, function (json) {
            layer.close(idx);
            if (json.success) {
                alerts.success(json.msg);
            } else {
                alerts.error(json.msg);
            }
            $(data.elem).removeAttr("checked");
            layui.form.render();
        }, "json").fail(function () {
            alerts.error("无入场记录处理异常");
            $(data.elem).removeAttr("checked");
            layui.form.render();
        });
    }
}
//无入场记录取消放行
var CancelNothingRecord = function (passwayNo, e) {
    var data = HtmlParameter.getConfirmPassOut(passwayNo);
    if (data == null) return;
    var idx = layer.msg("无入场记录取消放行...", { icon: 16, time: 0 });
    $(e).attr("disabled", true);
    $.post("CancelNoEnterRecord", { jsonModel: JSON.stringify(data) }, function (json) {
        layer.close(idx);
        $(e).removeAttr("disabled");
        if (json.success) {
            alerts.success(json.msg);
            opsObj.remove(passwayNo);
        } else {
            alerts.error(json.msg);
        }
    }, "json").fail(function () {
        alerts.error("无入场记录取消放行异常");
        $(e).removeAttr("disabled");
    });
}

// 关怀车辆通知管理
const CareVehicleNotificationManager = {
    currentNotification: null,
    notificationId: 'care-notification-main',
    autoCloseTimer: null,
    notifications: [],
    recentNotifications: [], // 存储最近180秒内的通知记录

    // 添加新的关怀车辆通知
    addNotification: function (data, direction) {
        try {
            debugger
            var passwayObj = pager.passwaydata.find((item) => { return item.Passway_No == data.passwayno; });
            const directionText = direction === 'enter' ? '入场' : '出场';
            const carno = data.carno || '未知车牌';
            const passway = passwayObj != null ? passwayObj.Passway_Name : (data.out_passway || data.passwayname || '未知');
            const time = direction === 'enter' ? (data.entertime || data.enter_time) : (data.out_time || data.outimg);

            // 创建通知唯一标识（车牌_车道_时间）
            const notificationKey = `${carno}_${passway}_${time}`;

            // 检查是否在180秒内已有相同的通知
            if (this.isDuplicateNotification(notificationKey)) {
                console.log(`关怀车辆通知重复，已忽略：${carno} ${directionText} - ${passway} - ${time}`);
                return;
            }

            // 检查是否已存在相同车牌的通知
            const existingIndex = this.notifications.findIndex(notification => notification.carno === carno);

            // 创建通知数据
            const notification = {
                id: Date.now(),
                carno: carno,
                direction: direction,
                directionText: directionText,
                passway: passway || '未知',
                time: time || '未知',
                timestamp: new Date(),
                notificationKey: notificationKey
            };

            // 记录到最近通知列表
            this.addToRecentNotifications(notificationKey);

            if (existingIndex !== -1) {
                // 如果存在相同车牌，更新现有通知
                this.notifications[existingIndex] = notification;
                console.log(`关怀车辆通知已更新：${carno} ${directionText} - ${time}`);
            } else {
                // 如果不存在，添加新通知
                this.notifications.push(notification);
                console.log(`关怀车辆通知已添加：${carno} ${directionText} - ${time}`);
            }

            // 显示或更新通知
            this.showOrUpdateNotification();

        } catch (error) {
            console.error('添加关怀车辆通知失败:', error);
        }
    },

    // 检查是否为重复通知
    isDuplicateNotification: function(notificationKey) {
        try {
            const now = new Date();
            // 清理超过180秒的记录
            this.cleanExpiredNotifications();

            // 检查是否存在相同的通知
            return this.recentNotifications.some(record => record.key === notificationKey);
        } catch (error) {
            console.error('检查重复通知失败:', error);
            return false;
        }
    },

    // 添加到最近通知列表
    addToRecentNotifications: function(notificationKey) {
        try {
            const now = new Date();
            this.recentNotifications.push({
                key: notificationKey,
                timestamp: now
            });

            // 清理过期记录
            this.cleanExpiredNotifications();
        } catch (error) {
            console.error('添加最近通知记录失败:', error);
        }
    },

    // 清理过期的通知记录
    cleanExpiredNotifications: function() {
        try {
            const now = new Date();
            const expireTime = 180 * 1000; // 180秒

            this.recentNotifications = this.recentNotifications.filter(record => {
                return (now - record.timestamp) < expireTime;
            });
        } catch (error) {
            console.error('清理过期通知记录失败:', error);
        }
    },

    // 显示或更新通知弹窗
    showOrUpdateNotification: function () {
        try {
            if (this.notifications.length === 0) return;

            // 如果已有通知弹窗，更新内容
            if (this.currentNotification && this.currentNotification.length > 0) {
                this.updateNotificationContent();
            } else {
                this.createNotification();
            }

            // 重置自动关闭计时器
            this.resetAutoCloseTimer();

        } catch (error) {
            console.error('显示或更新关怀车辆通知失败:', error);
        }
    },

    // 创建新的通知弹窗
    createNotification: function () {
        const notificationHtml = this.generateNotificationHtml();

        this.currentNotification = $(notificationHtml);
        $('body').append(this.currentNotification);

        // 根据通知数量决定是否启用滚动
        const bodyElement = this.currentNotification.find('.care-notification-body');
        if (this.notifications.length > 2) {
            bodyElement.addClass('scrollable');
        }

        // 初始化拖拽功能
        this.initDragFunctionality();

        // 显示动画
        setTimeout(() => {
            this.currentNotification.addClass('show');
        }, 100);
    },

    // 更新通知内容
    updateNotificationContent: function () {
        if (!this.currentNotification) return;

        const bodyContent = this.generateBodyContent();
        const headerContent = this.generateHeaderContent();

        const bodyElement = this.currentNotification.find('.care-notification-body');
        bodyElement.html(bodyContent);

        // 根据通知数量决定是否启用滚动
        if (this.notifications.length > 2) {
            bodyElement.addClass('scrollable');
        } else {
            bodyElement.removeClass('scrollable');
        }

        this.currentNotification.find('.care-title').html(headerContent);
    },

    // 生成通知HTML
    generateNotificationHtml: function () {
        const headerContent = this.generateHeaderContent();
        const bodyContent = this.generateBodyContent();

        return `
            <div id="${this.notificationId}" class="care-vehicle-notification">
                <div class="care-notification-header">
                    <div class="care-icon">
                        <i class="layui-icon layui-icon-heart-fill"></i>
                    </div>
                    <div class="care-title">${headerContent}</div>
                    <div class="care-close" onclick="CareVehicleNotificationManager.closeNotification()">
                        <i class="layui-icon layui-icon-close"></i>
                    </div>
                </div>
                <div class="care-notification-body">
                    ${bodyContent}
                </div>
                <div class="care-notification-footer">
                    <button class="layui-btn layui-btn-sm care-confirm-btn" onclick="CareVehicleNotificationManager.closeNotification()">
                        <i class="layui-icon layui-icon-ok"></i> 已知悉
                    </button>
                </div>
            </div>
        `;
    },

    // 生成标题内容
    generateHeaderContent: function () {
        const count = this.notifications.length;
        if (count === 1) {
            return `关怀车辆${this.notifications[0].directionText}提醒`;
        } else {
            return `关怀车辆提醒 (${count}条)`;
        }
    },

    // 生成主体内容
    generateBodyContent: function () {
        let content = '';

        this.notifications.forEach((notification, index) => {
            content += `
                <div class="care-vehicle-info ${index > 0 ? 'care-vehicle-info-additional' : ''}" data-notification-id="${notification.id}">
                    <div class="care-vehicle-close" onclick="CareVehicleNotificationManager.removeSingleNotification(${notification.id})">
                        <i class="layui-icon layui-icon-close"></i>
                    </div>
                    <div class="care-info-header">
                        <span class="care-info-index">${index + 1}.</span>
                        <span class="care-info-direction ${notification.direction === 'enter' ? 'care-direction-enter' : 'care-direction-exit'}">
                            ${notification.directionText}
                        </span>
                    </div>
                    <div class="care-info-row">
                        <span class="care-label">车牌号码：</span>
                        <span class="care-value care-carno">${notification.carno}</span>
                    </div>
                    <div class="care-info-row">
                        <span class="care-label">通行通道：</span>
                        <span class="care-value">${notification.passway}</span>
                    </div>
                    <div class="care-info-row">
                        <span class="care-label">${notification.directionText}时间：</span>
                        <span class="care-value">${notification.time}</span>
                    </div>
                </div>
            `;
        });

        content += `
            <div class="care-message">
                <i class="layui-icon layui-icon-tips"></i>
                <span>请值班人员注意，关怀车辆通行提醒！</span>
            </div>
        `;

        return content;
    },

    // 重置自动关闭计时器
    resetAutoCloseTimer: function () {
        if (this.autoCloseTimer) {
            clearTimeout(this.autoCloseTimer);
        }

        // 180秒后自动关闭
        this.autoCloseTimer = setTimeout(() => {
            this.closeNotification();
        }, 180000);
    },

    // 移除单个通知
    removeSingleNotification: function (notificationId) {
        try {
            // 从通知列表中移除指定的通知
            this.notifications = this.notifications.filter(notification => notification.id !== notificationId);

            // 如果还有通知，更新显示
            if (this.notifications.length > 0) {
                this.updateNotificationContent();
                // 重置计时器
                this.resetAutoCloseTimer();
            } else {
                // 如果没有通知了，关闭整个弹窗
                this.closeNotification();
            }

            console.log(`已移除单个关怀车辆通知：${notificationId}`);

        } catch (error) {
            console.error('移除单个关怀车辆通知失败:', error);
        }
    },

    // 初始化拖拽功能
    initDragFunctionality: function () {
        if (!this.currentNotification) return;

        const notification = this.currentNotification[0];
        const header = this.currentNotification.find('.care-notification-header')[0];

        let isDragging = false;
        let startX = 0;
        let startY = 0;
        let initialX = 0;
        let initialY = 0;

        // 获取当前位置
        const getCurrentPosition = () => {
            const rect = notification.getBoundingClientRect();
            return {
                x: rect.left,
                y: rect.top
            };
        };

        // 设置位置
        const setPosition = (x, y) => {
            // 确保弹窗不会拖出屏幕边界
            const maxX = window.innerWidth - notification.offsetWidth;
            const maxY = window.innerHeight - notification.offsetHeight;

            x = Math.max(0, Math.min(x, maxX));
            y = Math.max(0, Math.min(y, maxY));

            notification.style.position = 'fixed';
            notification.style.left = x + 'px';
            notification.style.top = y + 'px';
            notification.style.right = 'auto';
            notification.style.transform = 'none';
        };

        // 鼠标按下事件
        const handleMouseDown = (e) => {
            isDragging = true;
            const currentPos = getCurrentPosition();
            initialX = currentPos.x;
            initialY = currentPos.y;
            startX = e.clientX;
            startY = e.clientY;

            header.style.cursor = 'grabbing';
            document.addEventListener('mousemove', handleMouseMove);
            document.addEventListener('mouseup', handleMouseUp);
            e.preventDefault();
        };

        // 鼠标移动事件
        const handleMouseMove = (e) => {
            if (!isDragging) return;

            const deltaX = e.clientX - startX;
            const deltaY = e.clientY - startY;

            const newX = initialX + deltaX;
            const newY = initialY + deltaY;

            setPosition(newX, newY);
        };

        // 鼠标释放事件
        const handleMouseUp = () => {
            isDragging = false;
            header.style.cursor = 'move';
            document.removeEventListener('mousemove', handleMouseMove);
            document.removeEventListener('mouseup', handleMouseUp);
        };

        // 绑定事件
        header.addEventListener('mousedown', handleMouseDown);

        // 存储清理函数
        this.dragCleanup = () => {
            header.removeEventListener('mousedown', handleMouseDown);
            document.removeEventListener('mousemove', handleMouseMove);
            document.removeEventListener('mouseup', handleMouseUp);
        };
    },

    // 关闭通知
    closeNotification: function () {
        try {
            // 清理拖拽事件
            if (this.dragCleanup) {
                this.dragCleanup();
                this.dragCleanup = null;
            }

            if (this.currentNotification && this.currentNotification.length > 0) {
                this.currentNotification.removeClass('show').addClass('hide');
                setTimeout(() => {
                    this.currentNotification.remove();
                    this.currentNotification = null;
                }, 300);
            }

            // 清除计时器
            if (this.autoCloseTimer) {
                clearTimeout(this.autoCloseTimer);
                this.autoCloseTimer = null;
            }

            // 清空通知列表
            this.notifications = [];

            // 注意：不清空recentNotifications，保持180秒内的重复检查

        } catch (error) {
            console.error('关闭关怀车辆通知失败:', error);
        }
    }
};

// 关怀车辆通知功能
function showCareVehicleNotification(data, direction) {
    CareVehicleNotificationManager.addNotification(data, direction);
}

// 关闭关怀车辆通知
function closeCareNotification(notificationId) {
    CareVehicleNotificationManager.closeNotification();
}

// 添加关怀车辆标识到弹窗
function addCareVehicleBadge(container, enableCare, type) {
    try {
        if (!container) return;

        // 移除已存在的关怀车辆标识
        $(container).find('.care-vehicle-badge').remove();

        // 如果是关怀车辆，添加标识
        if (enableCare === true) {
            const badgeHtml = `
                <div class="care-vehicle-badge ${type === 'in' ? 'care-badge-in' : 'care-badge-out'}">
                    <div class="care-badge-icon">
                        <i class="layui-icon layui-icon-heart-fill"></i>
                    </div>
                    <div class="care-badge-text">关怀车辆</div>
                    <div class="care-badge-glow"></div>
                </div>
            `;

            $(container).prepend(badgeHtml);

            // 添加闪烁动画
            setTimeout(() => {
                $(container).find('.care-vehicle-badge').addClass('care-badge-show');
            }, 100);
        }
    } catch (error) {
        console.error('添加关怀车辆标识失败:', error);
    }
}

//websocket消息处理
var LPR = {
    msgdata: [],//提示信息
    errimg: [],
    Excute: function (data) {
        try {
            var act = data["act"];
            var obj = {};
            if (["updatedata", "UpdatePowerGroup"].includes(act)) { obj = { passwayno: "" }; } else { obj = JSON.parse(data["smsg"]); };
            var passway = pager.passwaydata.find((item) => { return item.Passway_No == obj.passwayno; });
            if (act != "UpdateSpace") console.log("ws消息：" + act + "," + (obj.carno ?? ""));
            console.log(act);
            console.log(obj);
            if (act == "UpdateLPR") {
                if (obj.code == 3) {
                    LPR.LPR_REFASH_FRAME(obj, passway, true);
                } else if (obj.code == 1) {
                    var orderno = '';
                    if (obj.orderno) {
                        orderno = obj.orderno;
                    }
                    var cLi = $("li[data-key='" + passway.Passway_No + "'][data-orderno='" + orderno + "']");
                    if (cLi != null && cLi.length > 0) {
                        //关闭弹窗
                        opsObj.remove(passway.Passway_No);
                        if (obj.evt == 2) {//确认放行
                            //if ($(cLi[0]).find("button[data-key='4']"))
                            //    $(cLi[0]).find("button[data-key='4']").click();
                            LPR.MESSAGE(obj, passway);
                        } else {//取消放行
                            //if ($(cLi[0]).find("button[data-key='0']"))
                            //    $(cLi[0]).find("button[data-key='0']").click();
                        }
                    }
                } else if (obj.code == 2) {
                    var carno = (obj.carno || obj.data.passres.carno) ?? "";
                    if (obj.data.passres.code == 2) {
                        alerts.success(carno + " 缴费完成 请确认通行");
                        LPR.LPR_REFASH_FRAME(obj, passway, true);
                    } else {
                        opsObj.remove(obj.passwayno);
                        LPR.MESSAGE(obj, passway);
                        alerts.success(carno + " 缴费完成 请通行");
                    }
                }
            }
            else if (act == "Remind") {
                LPR.MESSAGEERROR(obj.carno, obj.img, obj.passwayno, obj.passwayname, obj.msg, obj.time);
                this.REFRASHIMG();
            }
            else if (act == "UpdateSpace") {
                if (obj != null && obj != undefined && obj != "") {
                    var enventObj = obj.envent;
                    var spaceObj = obj.space;
                    //车位数
                    if (spaceObj != null && spaceObj != undefined && spaceObj != "") {
                        $("#space_0").text(spaceObj.Item1 || "0");
                        $("#space_1").text(spaceObj.Item2 || "0");

                        $("#space_area").html("");
                        spaceObj.Item4.forEach(function (item, index) {
                            $("#space_area").append(' <li class="color_0"><ndiv>[' + item.Item4 + '] 车位</ndiv><vdiv class="' + getColor(item.Item2) + '">' + item.Item2 + '</vdiv></li>');
                            $("#space_area").append(' <li class="color_0"><ndiv>[' + item.Item4 + '] 余位</ndiv><vdiv class="' + getColor(item.Item3) + '">' + item.Item3 + '</vdiv></li>');
                        });
                        $("#space_area").append(' <li><ndiv></ndiv><vdiv ></vdiv></li>');
                        spaceObj.Item3.forEach(function (item, index) {
                            if (!isNaN(item.Item3) && parseInt(item.Item3) > 0) {
                                $("#space_area").append(' <li><ndiv>' + item.Item2 + '</ndiv><vdiv class="' + getColor(item.Item3) + '">' + parseInt(item.Item3) + '</vdiv></li>');
                            }
                        });
                    }
                    //事件数
                    if (enventObj != null && enventObj != undefined && enventObj != "") {
                        if (enventObj > 0) {
                            $("span.total").removeClass("layui-hide");
                            $(".eventpng").addClass("eventpng-large");
                            $("span.total").html(enventObj);
                        } else {
                            $("span.total").removeClass("layui-hide").addClass("layui-hide");
                            $(".eventpng").removeClass("eventpng-large");
                        }
                    } else {
                        $("span.total").removeClass("layui-hide").addClass("layui-hide");
                        $(".eventpng").removeClass("eventpng-large");
                    }
                }
            }
            else if (act == "updatedata") {
                if (data["smsg"] == "powergroup") {
                    $.post("UpdatePowerGroup", {}, function (json) {
                        if (json.success) {
                            if (json.data == "1") {
                                alerts.success("岗亭权限变更，将于5秒后刷新岗亭权限");
                                setTimeout(function () { location.href = window.location.href; }, 5000); // 刷新当前页面
                            }
                        } else {
                            alerts.error(json.msg);
                            setTimeout(function () {
                                alerts.success("将于5秒后刷新岗亭权限");
                                setTimeout(function () { location.href = window.location.href; }, 5000)
                            }, 2000);
                        }
                    }, "json").fail(function () {
                        alerts.error("刷新岗亭权限异常");
                    });
                } else if (data["smsg"] == "initsentryhost") {
                    alerts.success("岗亭正在初始化，将于5秒后自动刷新");
                    setTimeout(function () { location.href = window.location.href; }, 5000); // 刷新当前页面
                } else if (["passway", "device", "sentryhost", "parkarea", "parkdiscountset"].includes(data["smsg"])) {
                    alerts.success("岗亭参数变更，将于5秒后自动刷新");
                    setTimeout(function () { location.href = window.location.href; }, 5000); // 刷新当前页面
                }
            }
            else if (act == "gate") {
                if (obj.action == "open") {
                    $("#video" + obj.passwayno).find(".item-btns button[data-key='6']").text("取消常开");
                    $("div[data-passwayno=" + obj.passwayno + "]").find(".item-btns button[data-key='6']").text("取消常开");
                } else if (obj.action == "close") {
                    $("#video" + obj.passwayno).find(".item-btns button[data-key='6']").text("常开");
                    $("div[data-passwayno=" + obj.passwayno + "]").find(".item-btns button[data-key='6']").text("常开");
                } else if (obj.action == "allopen") {
                    $("div.item-box").find(".item-btns button[data-key='6']").text("取消常开");
                }
            }
            else if (act == "sysstatus" && obj) {

                const devices = obj.devices || [];
                const currentParkingState = obj.parking || {};

                const receivedNos = new Set();

                //更新设备状态
                devices.forEach(item => {
                    receivedNos.add(item.no);
                    try {
                        videoobj.onstate(
                            $(`div[data-passwayno=${item.no}]`),
                            item.opengate,
                            item.online,
                            item.hascar
                        );
                    } catch (e) {
                        console.warn(`更新设备状态失败: 通道 ${item.no}`, e);
                    }
                });

                //处理未返回的本地缓存通道
                if (localCache?.videodata?.length > 0) {
                    localCache.videodata.forEach(video => {
                        if (!receivedNos.has(video.passwayno)) {
                            try {
                                videoobj.onstate(
                                    $(`div[data-passwayno=${video.passwayno}]`),
                                    -1, // 未知开闸状态
                                    0,  // 离线
                                    0   // 无车
                                );
                            } catch (e) {
                                console.warn(`更新本地设备状态失败: 通道 ${video.passwayno}`, e);
                            }
                        }
                    });
                }

                //云平台状态更新
                if (currentParkingState && currentParkingState.status !== undefined) {
                    if (currentParkingState.status === lastParkingState) {
                        return; // 状态无变化，不更新UI
                    }

                    lastParkingState = currentParkingState.status;

                    switch (currentParkingState.status) {
                        case CloudStatus.ONLINE:
                            updateCloudStatusUI("connet", "云平台连接正常");
                            break;
                        case CloudStatus.OFFLINE:
                            updateCloudStatusUI("close", "云平台连接离线");
                            break;
                        case CloudStatus.DISABLED:
                        default:
                            updateCloudStatusUI("loading", "未启用云平台");
                            break;
                    }
                } else {
                    console.warn("平台状态异常或数据缺失");
                    updateCloudStatusUI("close", "云平台连接异常");
                }
            }
            else {
                LPR.SHOW_IMAGE(obj, passway);
                LPR.RECOGRESULT(obj, passway);
                if (obj.passcode != 2 && obj.passcode != 4) {
                    LPR.MESSAGE(obj, passway);

                    // 检查是否为关怀车辆，如果是则显示关怀车辆提示
                    if (obj && obj.enableCare === true) {
                        if (passway.Passway_GateType == 1 || passway.Passway_GateType == 2) {
                            showCareVehicleNotification(obj, 'enter');
                        } else {
                            showCareVehicleNotification(obj, 'exit');
                        }
                    }

                    opsObj.remove(obj.passwayno);
                    this.REFRASHIMG();
                } else {
                    LPR.LPR_OPEN_FRAME(obj, passway, true);
                    this.REFRASHIMG();
                }
            }

            obj = null;
            data = null;
        } catch (e) {
            obj = null;
            data = null;
            console.error("websocket处理异常：" + e.message);
        }
    },
    //弹窗
    LPR_OPEN_FRAME: function (obj, passway, isAutoWin) {
        if (obj != null) {
            //判断车牌号是否在其他车道已经弹窗,若弹窗则先关闭之前车道的弹窗
            var opsExist = opsObj.data.find((item, index) => { return (item.carno == obj.carno && item.carno != ''); });
            if (opsExist != null) {
                opsObj.remove(opsExist.id);
            }
        }
        var ops = opsObj.create(passway, obj, isAutoWin);
        opsObj.append(ops, false, isAutoWin);

    },
    //刷新弹窗，obj=弹窗数据包
    LPR_REFASH_FRAME: function (obj, passway, isAutoWin) {
        var ops = opsObj.create(passway, obj, isAutoWin);
        var targetIndex = opsObj.data.findIndex(item => item.id === ops.id);
        if (targetIndex !== -1) {
            opsObj.data[targetIndex] = ops;
        }
        var frm = $("li.out[data-key=" + passway.Passway_No + "]");
        if (frm && frm.length > 0) {
            $(frm[0]).find("[data-id='orderamount']").text(ops.out_orderamount.toFixed(2));
            $(frm[0]).find("[data-id='couponamount']").text(ops.out_couponamount.toFixed(2));
            $(frm[0]).find("[data-id='chuzhiamount']").text(ops.out_chuzhiamount.toFixed(2));
            $(frm[0]).find("[data-id='carcardtype']").text(ops.enter_card);
            $(frm[0]).find("[data-id='payedamount']").val(ops.out_payedamount.toFixed(2));
            $(frm[0]).find("[data-id='parktimemin']").text(ops.out_parktimemin);
            var msg = ops.out_reminderinfo && ops.out_reminderinfo != "" ? ('<t class="fa fa-exclamation-triangle"></t>' + ops.out_reminderinfo + '') : "";
            $(frm[0]).find("[class='reminderinfo']").html(msg);
        }
        ops = null;//清空
    },
    //监控显示图片
    SHOW_IMAGE: function (obj, passway) {
        if ($(".monitor .item-box[data-passwayno='" + passway.Passway_No + "'] .videobox > img").length < 1) return false;
        var src = null;
        if (passway.Passway_GateType == 0 || passway.Passway_GateType == 3) src = obj.outimg;
        else src = obj.enterimg;
        src = PathCheck(src);
        if (isFrpUrl) {
            src = replaceFirstPathSegment(src);
        }

        //$(".monitor .item-box[data-passwayno='" + passway.Passway_No + "'] .videobox");
        var videoBox = $(".monitor .item-box[data-passwayno='" + passway.Passway_No + "'] .videobox > img").closest(".videobox");
        if (videoBox != null && videoBox.length > 0) {
            if ($(videoBox).find("img[data-camera=11]").length == 0) {
                var img = document.createElement('img');
                img.src = src;
                img.setAttribute("data-src", src);
                img.onerror = function () { img.src = "../Static/img/img-nothing.png"; };
                img.style.width = "100%";
                img.style.height = "100%";
                $(videoBox).html(img);

                var imgTool1 = new imgtool();
                imgTool1.imageLoad($(videoBox).attr("id"), src, 0, img, function () { $(videoBox).find("img").attr("src", src || ''); imgTool1.clear(); })
            }
        }
    },
    //加载识别结果
    RECOGRESULT: function (obj, passway) {

        //console.log("加载识别结果obj:" + JSON.stringify(obj))
        //console.log("加载识别结果passway:" + JSON.stringify(passway))
        if (pager) pager.calcDetail[passway.Passway_No] = null;
        if (passway != null) {
            var img = null;
            var time = null;
            var remark = null;

            if (passway.Passway_GateType == 0) {
                img = obj.enterimg;
                time = obj.outtime;
                remark = obj.outremark;
            } else if (passway.Passway_GateType == 3) {
                img = obj.enterimg;
                time = obj.outtime;//obj.entertime;
                remark = obj.outremark;
            } else {
                img = obj.outimg;
                time = obj.entertime;
                remark = obj.enterramrk;
            }
            if (remark == null || remark == "") { remark = obj.remark; }
            var money = 0;
            if (obj && obj.data && obj.data.payres) {
                money = Number(obj.data.payres.payedamount);
                if (pager) {
                    pager.calcDetail[passway.Passway_No] = obj.data.calcdetail;
                    if (obj.data.payres) {
                        if (obj.data.payres.payed == 1) {
                            obj.data.payres.payedmsg = "计费成功";
                        } else if (obj.data.payres.payed == 0) {
                            obj.data.payres.payedmsg = "";
                        } else {
                            obj.data.payres.payedmsg = "计费失败";
                        }
                        pager.payedmsg = obj.data.payres.payedmsg;
                    }
                    else {
                        pager.payedmsg = "";
                    }
                } else {
                    pager.payedmsg = "";
                }

                opsObj.showCalcMsg(passway.Passway_No);

                if (obj.data.unpaidresult != null) {
                    var unpaidMoeny = 0;
                    obj.data.unpaidresult.forEach(x => {
                        if (x.payres != null) unpaidMoeny += Number(x.payres.payedamount);
                    });
                    money += Number(unpaidMoeny);
                }

            } else money = 0;
            //入场图片
            var enterSrc = PathCheck(obj.enterimg);
            if (isFrpUrl) {
                enterSrc = replaceFirstPathSegment(enterSrc);
            }
            $("#rlt_enterimg a").attr("href", enterSrc || '');

            var imgTool1 = new imgtool();
            var imgTool2 = new imgtool();
            imgTool1.imageLoad("in" + passway.Passway_No, enterSrc, 0, $("#rlt_enterimg a img"), function () {
                $("#rlt_enterimg a img").attr("title", "入场图片").attr("src", enterSrc || '').attr("data-src", enterSrc || ''); $("#rlt_enterimg a img").get(0).src = enterSrc; $("#rlt_enterimg a img").eq(0).css("display", ""); imgTool1.clear();
            })
            //出场图片
            var outSrc = PathCheck(obj.outimg);
            if (isFrpUrl) {
                outSrc = replaceFirstPathSegment(outSrc);
            }
            $("#rlt_outimg a").attr("href", outSrc || '');
            imgTool2.imageLoad("out" + passway.Passway_No, outSrc, 0, $("#rlt_outimg a img"), function () { $("#rlt_outimg a img").attr("title", "出场图片").attr("src", outSrc || '').attr("data-src", outSrc || ''); $("#rlt_outimg a img").eq(0).css("display", ""); imgTool2.clear(); })

            $("#rlt_carno").attr("title", obj.carno).html(obj.carno);
            //$("#rlt_img").html('<a href="' + img + '" target="_blank">预览</a>');
            $("#rlt_passway").attr("title", passway.Passway_Name).html(passway.Passway_Name);
            $("#rlt_area").attr("title", passway.Passway_AreaCallName).html(passway.Passway_AreaCallName);
            $("#rlt_time").attr("title", time).html(time);

            if (obj.entertime == undefined || obj.entertime == "") { obj.entertime = obj.data?.resorder?.resOut?.parkorder?.ParkOrder_EnterTime ?? ""; }
            if (obj.outtime == undefined || obj.outtime == "") { obj.outtime = obj.data?.resorder?.resOut?.parkorder?.ParkOrder_OutTime ?? ""; }

            if (obj.outtime != undefined && obj.outtime != "" && obj.entertime != undefined && obj.entertime != "") {
                var calctime = getParkMin(obj.entertime, obj.outtime);
                if (calctime != "") {
                    $("#rlt_calctime").attr("title", calctime).html(calctime);
                }
            }
            else if (obj.parktime != null && obj.parktime != "") $("#rlt_calctime").attr("title", obj.parktime).html(obj.parktime);
            else $("#rlt_calctime").attr("title", "").html("");

            $("#rlt_calcmoney").attr("title", money).html(money == 0 ? "" : money);
            //var cartypeName = obj.data.passres.cartype.CarType_Name;
            console.log(obj.carno + '  ' + obj.carcardtype + " / " + obj.cartype)
            $("#rlt_card").attr("title", obj.carcardtype + " / " + obj.cartype).html(obj.carcardtype + " / " + obj.cartype);
            //$("#rlt_cartype").attr("title", obj.cartype).html(obj.cartype);
            $("#rlt_owner").attr("title", obj.carno).html(obj.name || '未登记');
            $("#rlt_remark").attr("title", remark).html(remark || '');
            //禁止通行提示
            $("#rlt_errmsg").html("");
            if (obj.data.passres.code == 0) {
                var errmsg = obj.data != null ? (obj.data.errmsg || obj.data.passres.errmsg) : "";
                $("#rlt_errmsg").attr("title", errmsg).css({ "color": "red" }).html(errmsg || '');
            } else {
                var errmsg = "";
                if (obj.deduction != "" && obj.deduction != "0") errmsg = "扣除" + obj.deduction + "元";
                if (obj.balance != "") errmsg += " 余额" + obj.balance + "元";
                $("#rlt_errmsg").attr("title", errmsg).css({ "color": "red" }).html(errmsg);
            }

            var smimg = PathCheck(obj.data.recog.CarRecog_SmallImg);
            if (isFrpUrl) {
                smimg = replaceFirstPathSegment(smimg);
            }

            //识别小图
            var imgsmall = (obj.data != null && obj.data.recog != null) ? smimg : "";
            $("#rlt_enterimgsmall").html("");
            $("#rlt_outimgsmall").html("");

            var imgTool3 = new imgtool();
            if (obj.data.passres.type == 200) {
                imgTool3.imageLoad("insmall" + passway.Passway_No, imgsmall, 0, $("#rlt_enterimgsmall"), function () { $("#rlt_enterimgsmall").attr("title", "入场车牌").html('<img src="' + imgsmall + '" data-src="' + imgsmall + '" onerror="this.style=\'display: none\'">'); imgTool3.clear(); })
            } else {
                imgTool3.imageLoad("outsmall" + passway.Passway_No, imgsmall, 0, $("#rlt_outimgsmall"), function () { $("#rlt_outimgsmall").attr("title", "入场车牌").html('<img src="' + imgsmall + '" data-src="' + imgsmall + '" onerror="this.style=\'display: none\'">'); imgTool3.clear(); })
            }
        }
    },
    //提示消息
    MESSAGEERROR: function (carno, img, passwayno, passwayname, msg, time) {
        if (carno && carno.length > 0) {
            msg = "[" + carno + "][" + passwayname + "]" + msg;
        }
        else {
            msg = "[" + passwayname + "]" + msg;
        }

        //opsObj.remove(passwayno);//清除弹窗
        alerts.error(msg);
        if ($(".monitor .item-box[data-passwayno='" + passwayno + "'] .videobox > img").length > 0) {
            var src = PathCheck(img);
            if (isFrpUrl) {
                src = replaceFirstPathSegment(src);
            }

            var imgTool1 = new imgtool();
            imgTool1.imageLoad("videobox" + passwayno, src, 0, null, function () {
                var videoBox = $(".monitor .item-box[data-passwayno='" + passwayno + "'] .videobox");
                if (videoBox != null && videoBox.length > 0) {
                    var img = document.createElement('img');
                    img.setAttribute("data-src", src);
                    img.onerror = function () { img.src = "../Static/img/img-nothing.png"; };
                    img.style.width = "100%";
                    img.style.height = "100%";
                    img.onload = function () {
                        img.style.width = "100%";
                        img.style.height = "100%";
                    };
                    img.src = src;
                    $(videoBox).html(img);
                }
                imgTool1.clear();
            })
        }

        $("#notice .labelim").html(" <font style='color: red;'>" + msg + "</font>");
        LPR.msgdata[LPR.msgdata.length] = "<font style='color: red;'>" + "<span class='tiptime'>" + time + "</span>" + " " + msg + "</font>";
        if (LPR.msgdata.length > 20)
            LPR.msgdata.shift();

        for (let i = 0; i < LPR.msgdata.length; i++) {
            let existingLi = $("#msgContent li").eq(i);
            if (existingLi.length > 0) {
                // 更新已有的 <li>
                existingLi.html(LPR.msgdata[i]);
            } else {
                // 如果 <li> 不存在，追加新内容
                $("#msgContent").append('<li>' + LPR.msgdata[i] + '</li>');
            }
        }
    },
    //提示消息
    MESSAGE: function (obj, passway, msg) {
        //console.log(obj)
        var carno = obj.carno || obj.data.passres.carno;
        var time = "";
        if (!msg || msg == "" || msg == undefined) {
            if (obj != null && passway != null) {
                if (passway.Passway_GateType == 0 || passway.Passway_GateType == 3) {
                    time = obj.outtime || obj.data.time;
                    msg = "[" + carno + "][" + passway.Passway_Name + "]";
                    if (obj.passcode != 0)
                        msg += "出场成功";
                    else
                        msg += "出场失败：" + (obj.passmsg || obj.data.passres.errmsg);
                }
                else {
                    time = obj.entertime || obj.data.time;
                    msg = "[" + carno + "][" + passway.Passway_Name + "]";
                    if (obj.passcode != 0)
                        msg += "入场成功";
                    else
                        msg += "入场失败：" + (obj.passmsg || obj.data.passres.errmsg);
                }

            } else {
                return;
            }
        }

        $("#notice .labelim").text(" " + msg);

        LPR.msgdata[LPR.msgdata.length] = "<span class='tiptime'>" + time + "</span>" + " " + msg;
        if (LPR.msgdata.length > 20)
            LPR.msgdata.shift();

        for (let i = 0; i < LPR.msgdata.length; i++) {
            let existingLi = $("#msgContent li").eq(i);
            if (existingLi.length > 0) {
                // 更新已有的 <li>
                existingLi.html(LPR.msgdata[i]);
            } else {
                // 如果 <li> 不存在，追加新内容
                $("#msgContent").append('<li>' + LPR.msgdata[i] + '</li>');
            }
        }

        var money = 0;
        if (obj && obj.data && obj.data.payres) {
            money = Number(obj.data.payres.payedamount);
            if (obj.data.unpaidresult != null) {
                var unpaidMoeny = 0;
                obj.data.unpaidresult.forEach(x => {
                    if (x.payres != null) unpaidMoeny += Number(x.payres.payedamount);
                });
                money += Number(unpaidMoeny);
            }
        } else { money = 0; }

        $("#rlt_calcmoney").attr("title", money).html(money == 0 ? "" : money);
    },
    //出入场后加载界面显示
    LOAD_SHOW: function (obj, passway) {
        localCache.setLastOrder({ obj: obj, passway: passway });
        localCache.setMessage({ obj: obj, passway: passway });

        this.SHOW_IMAGE(obj, passway);
        this.RECOGRESULT(obj, passway);
        this.REFRASHIMG();
    },
    //抓拍图片后，过一秒再刷新一次，防止图片还未保存就加载了
    REFRASHIMG: function () {
        //setTimeout(() => {
        //    $("img").each(function () {
        //        var src = $(this).attr("data-src");
        //        if (src && src != null) {
        //            $(this).attr("src", PathCheck(src));
        //        }
        //    });
        //}, 1500);
    }
}

//按钮[弹窗操作]
var btns = {
    uiIncar: null,
    //监控按钮
    onVideoClick: function (passwayNo, e) {
        var key = $(e).attr("data-key");
        console.log("触发按钮：" + key)
        switch (key) {
            //识别车牌
            case '0':
                $(e).attr("disabled", true).text("识别...");
                $.post("PlateRecognition", { passwayno: passwayNo }, function (json) {
                    if (json.success) {
                        alerts.success(json.msg);
                    } else {
                        alerts.error(json.msg);
                    }
                    $(e).removeAttr("disabled").text("识别");
                }, "json").fail(function () {
                    alerts.error("识别车牌异常");
                    $(e).removeAttr("disabled").text("识别");
                });
                break
            //特殊车牌
            case '1':
                alerts.success("特殊车牌");
                break
            //车牌放行
            case '2':
                var passway = pager.passwaydata.find((item) => { return item.Passway_No == passwayNo; });
                if (passway.Passway_GateType == -1) { layer.msg("车道未关联区域"); return; }
                if (passway.Passway_DutyMode == 0) { alerts.error("车道为无人值守模式"); return; }
                if (pager) pager.calcDetail[passway.Passway_No] = null;
                pager.payedmsg = "";
                (function (cartypes, passwayno, carcardtypes) {
                    var idx = layer.msg("正在打开弹窗...", { icon: 16, time: 0 });
                    $.post("GetPolicyPassway", { passwayno: passwayno }, function (json) {
                        layer.close(idx);
                        var cartype = "";
                        var enter_card = "";
                        if (json && json.data != null) {
                            var cartypeno = json.data.PolicyPassway_DefaultCarType;
                            var carTypeModel = cartypes.find((item) => { return item.CarType_No == cartypeno; });
                            if (carTypeModel != null) {
                                cartype = carTypeModel.CarType_Name;
                            }

                            enter_card = json.data.PolicyPassway_DefaultCarCardType;
                        }

                        var ops = opsObj.create(passway, null, undefined, cartype, enter_card);
                        opsObj.append(ops, true);

                        //var index = layer.msg("正在抓拍图片...", { icon: 16, time: 0 });
                        videoobj.snap(passwayno, function (data) {
                            var img = data.tempImage;
                            var display = data.displayImg;
                            //layer.close(index);
                            //console.log("抓拍到图片：" + img)

                            var imgtool1 = new imgtool();
                            if (passway.Passway_GateType == 1 || passway.Passway_GateType == 2) {
                                imgtool1.imageLoad("enterimgsmall" + passwayno, display, 0, $("li.in .img img"), function () { $("li.in .img img").attr("src", display).attr("data-src", img); imgtool1.clear(); })
                            } else if (passway.Passway_GateType == 0 || passway.Passway_GateType == 3) {
                                imgtool1.imageLoad("enterimg" + passwayno, display, 0, $("li.out .img img.outcar_img"), function () { $("li.out .img img.outcar_img").attr("src", display).attr("data-src", display); imgtool1.clear(); })
                            }
                        }, (errmsg) => {
                            /*layer.close(index);*/
                            alerts.error(errmsg);
                        });
                    }).fail(function (jqXHR, textStatus, errorThrown) {
                        layer.msg("打开弹窗失败，未能获取该车道通行策略", { icon: 2, time: 3000 });
                    });

                })(pager.cartypes, passway.Passway_No)

                break
            //关闸
            case '3':
                $(".item-btns-other2").addClass("layui-hide");
                layer.open({
                    type: 0,
                    title: "",
                    btn: ["确定", "取消"],
                    content: "确定关闸?",
                    area: ["300px"],
                    yes: function (index) {
                        layer.close(index);
                        $(e).text("关闸...").attr("disabled", true);
                        $.post("OpenCloseGate", { passwayno: passwayNo, code: 0 }, function (json) {
                            if (json.success) {
                                alerts.success(json.msg);
                                GetCurrentWorkShift();
                            } else {
                                alerts.error(json.msg);
                            }

                            $(e).text("关闸").removeAttr("disabled");
                        }, "json").fail(function () {
                            alerts.error("关闸异常");
                            $(e).text("关闸").removeAttr("disabled");
                        });
                    }
                });


                break
            //开闸
            case '4':
                //layer.open({
                //    type: 0,
                //    title: "",
                //    btn: ["确定", "取消"],
                //    content: "确定开闸?",
                //    area: ["300px"],
                //    yes: function (index) {
                //        layer.close(index);

                //    }
                //});
                // 检查是否需要选择开闸原因
                checkOpenGateReason(function (reason) {
                    $(e).text("开闸...").attr("disabled", true);
                    $.post("OpenCloseGate", { passwayno: passwayNo, code: 1, reason: reason }, function (json) {
                        if (json.success) {
                            alerts.success(json.msg);
                            GetCurrentWorkShift();
                        } else {
                            alerts.error(json.msg);
                        }

                        $(e).text("开闸").removeAttr("disabled");
                    }, "json").fail(function () {
                        alerts.error("开闸异常");
                        $(e).text("开闸").removeAttr("disabled");
                    });
                }, function () {
                    // 用户取消选择原因
                    $(e).text("开闸").removeAttr("disabled");
                });
                break;
            //抓拍
            case '5':
                $(".item-btns-other2").addClass("layui-hide");
                videoobj.snap(passwayNo, (data) => {
                    var src = data.tempImage;
                    var display = data.displayImg;
                    if (src == "" && display == "") { alerts.error("抓拍失败"); }
                    if (isFrpUrl) {
                        display = replaceFirstPathSegment(display);
                    }
                    var videoBox = $(".monitor .item-box[data-passwayno='" + passwayNo + "'] .videobox");
                    if (videoBox != null && videoBox.length > 0) {
                        var img = document.createElement('img');
                        img.src = display;
                        img.setAttribute("data-src", display);
                        img.onerror = function () { img.src = "../Static/img/img-nothing.png"; };
                        img.style.width = "100%";
                        img.style.height = "100%";
                        $(videoBox).html(img);
                    }
                }, (errmsg) => {
                    alerts.error(errmsg);
                });
                break;
            //道闸常开
            case '6':
                var btnText = $(e).text();
                $(".item-btns-other2").addClass("layui-hide");
                layer.open({
                    type: 0,
                    title: "",
                    btn: ["确定", "取消"],
                    content: "确定" + btnText + "?",
                    area: ["300px"],
                    yes: function (index) {
                        layer.close(index);
                        $(e).text("执行中...").attr("disabled", true);
                        $.post("OpenCloseGate", { passwayno: passwayNo, code: 2 }, function (json) {
                            if (json.success) {
                                alerts.success(json.msg);
                                GetCurrentWorkShift();
                                pager.passwaydata.forEach(function (item, index) { if (item.Passway_No == passwayNo) { pager.passwaydata[index].IsLongOpen = json.code; } });
                                if (json.code == 0) {
                                    $(e).text("常开");
                                } else {
                                    $(e).text("取消常开");
                                }
                            } else {
                                $(e).text(btnText);
                                alerts.error(json.msg);
                            }
                            $(e).removeAttr("disabled");
                        }, "json").fail(function () {
                            alerts.error("道闸常开异常");
                            $(e).removeAttr("disabled");
                        });
                    }
                });
                break;
            //播报
            case '7':
                layer.open({
                    type: 2,
                    title: "车道常用提示语音",
                    content: "/Monitoring/CustomBroadVoice?Passway_No=" + passwayNo,
                    area: ["500px", "350px"],
                    maxmin: false
                });
                break;
            //更多
            case '888':
                //获取当前按钮同级别的item-btns-other2
                $(e).siblings(".item-btns-other2").toggleClass("layui-hide");
                var ot2div = $(e).siblings(".item-btns-other2")[0];
                ot2div.style.bottom = $(e).parent()[0].offsetHeight + "px";
                break;

            default:
                break;
        }
    },
    //弹窗操作按钮
    onOpsClick: function (passwayNo, e, callback) {
        var key = $(e).attr("data-key");
        console.log("弹窗操作按钮：" + key)
        switch (key) {
            //取消放行
            case '0':
                var chk = $("li.out[data-key='" + passwayNo + "'] input[type='checkbox'].outcar_noincar");
                if (chk && chk.length > 0 && chk[0].checked) {
                    //取消无入场记录放行
                    CancelNothingRecord(passwayNo, e);
                }
                else {

                    $(e).prop("disabled", true);
                    var idx = layer.msg("正在处理取消放行...", { icon: 16, time: 0 });
                    $.post("CancelPass", { passwayno: passwayNo }, function (json) {
                        layer.close(idx);
                        if (json.success) {
                            opsObj.remove(passwayNo);
                            alerts.success("取消放行");
                        } else {
                            alerts.error(json.msg);
                        }
                    }, "json").fail(function () {
                        alerts.error("取消放行异常");
                    }).always(function () {
                        $(e).prop("disabled", false)
                    });
                }

                break;
            //语音播报
            case '1':
                //alerts.success("语音播报");
                var passway = pager.passwaydata.find((item) => { return item.Passway_No == passwayNo; });
                var data = HtmlParameter.getConfirmPassOut(passwayNo);
                if (data == null) return;

                $(e).prop("disabled", true);
                $.post("BroadVoice", { jsonModel: JSON.stringify(data) }, function (json) {
                    if (json.success) {
                        alerts.success(json.msg);
                    } else {
                        alerts.error(json.msg);
                    }
                }, "json").fail(function () {
                    alerts.error("语音播报异常");
                }).always(function () {
                    $(e).prop("disabled", false)
                });
                break;
            //入口确认放行
            case '2':
                var passway = pager.passwaydata.find((item) => { return item.Passway_No == passwayNo; });
                var data = HtmlParameter.getConfirmPass(passwayNo);
                if (data == null) return;

                $(e).prop("disabled", true).text("处理中...");
                $.post("ManualEntrance", { jsonModel: JSON.stringify(data) }, function (json) {
                    if (json.success) {
                        alerts.success(json.msg);
                        inParkOrder.onSearch();

                        //if (data.mode == 6)
                        //    LPR.LOAD_SHOW(json.data, passway);
                        //LPR.MESSAGE(json.data, passway);
                        $("#rlt_card").attr("title", data.ParkOrder_CarCardTypeText + " / " + data.ParkOrder_CarTypeText).html(data.ParkOrder_CarCardTypeText + " / " + data.ParkOrder_CarTypeText);
                        opsObj.remove(passwayNo);
                    } else {
                        alerts.error(json.msg);
                    }
                }, "json").fail(function () {
                    alerts.error("确认放行异常");
                    $(e).removeAttr("disabled").html("确认放行<t style='font-size: 1vw;'>(F2)<t>");
                }).always(function () {
                    $(e).prop("disabled", false).html("确认放行<t style='font-size: 1vw;'>(F2)<t>");
                });
                break;
            //免费放行
            case '3':
                //alerts.success("免费放行");
                var passway = pager.passwaydata.find((item) => { return item.Passway_No == passwayNo; });
                var data = HtmlParameter.getConfirmPassOut(passwayNo);
                if (data == null) return;
                if (data.ParkOrder_FreeReason == null || data.ParkOrder_FreeReason == "") { alerts.error("请输入免费原因"); return; }
                $(e).prop("disabled", true).text("处理中...");
                $.post("FreeRelease", { jsonModel: JSON.stringify(data) }, function (json) {
                    if (json.success) {
                        alerts.success(json.msg);
                        inParkOrder.onSearch();

                        //if (data.mode == 6)
                        //    LPR.LOAD_SHOW(json.data, passway);
                        //LPR.MESSAGE(json.data, passway);
                        $("#rlt_card").attr("title", data.ParkOrder_CarCardTypeText + " / " + data.ParkOrder_CarTypeText).html(data.ParkOrder_CarCardTypeText + " / " + data.ParkOrder_CarTypeText);
                        opsObj.remove(passwayNo);
                    } else {
                        alerts.error(json.msg);
                    }
                }, "json").fail(function () {
                    alerts.error("免费放行异常");
                }).always(function () {
                    $(e).prop("disabled", false).text("免费放行");
                });
                break;
            //出口确认放行
            case '4':
                var passway = pager.passwaydata.find((item) => { return item.Passway_No == passwayNo; });
                var data = HtmlParameter.getConfirmPassOut(passwayNo);
                if (data == null) return;
                if (!myVerify.check()) return;

                $(e).prop("disabled", true).text("处理中...");
                var chk = $("li.out[data-key='" + passwayNo + "'] input[type='checkbox'].outcar_noincar");
                if (chk && chk.length > 0 && !chk[0].checked) {
                    //有入场放行
                    $.post("ConfirmRelease", { jsonModel: JSON.stringify(data) }, function (json) {
                        if (json.success) {
                            alerts.success(json.msg);
                            inParkOrder.onSearch();

                            //if (data.mode == 6)
                            //    LPR.LOAD_SHOW(json.data, passway);
                            //LPR.MESSAGE(json.data, passway);
                            $("#rlt_card").attr("title", data.ParkOrder_CarCardTypeText + " / " + data.ParkOrder_CarTypeText).html(data.ParkOrder_CarCardTypeText + " / " + data.ParkOrder_CarTypeText);
                            opsObj.remove(passwayNo);

                            if (json.data.payres != null && json.data.payres.payed != 0) {
                                GetCurrentWorkShift();
                            }

                        } else {
                            alerts.error(json.msg);
                        }
                    }, "json").fail(function () {
                        alerts.error("确认放行异常");
                    }).always(function () {
                        $(e).prop("disabled", false).html("确认放行<t style='font-size: 1vw;'>(F1)<t>");
                    });
                } else {
                    //无入场放行
                    $.post("ConfirmReleaseNothingRecord", { jsonModel: JSON.stringify(data) }, function (json) {
                        if (json.success) {
                            alerts.success(json.msg);
                            inParkOrder.onSearch();
                            if (json.data != null && json.data.payedamount != null && json.data.payedamount != undefined) {
                                $("#rlt_calcmoney").attr("title", json.data.payedamount).html(json.data.payedamount == 0 ? "" : json.data.payedamount);
                            }
                            //if (data.mode == 6)
                            //    LPR.LOAD_SHOW(json.data, passway);
                            //LPR.MESSAGE(json.data, passway);
                            $("#rlt_card").attr("title", data.ParkOrder_CarCardTypeText + " / " + data.ParkOrder_CarTypeText).html(data.ParkOrder_CarCardTypeText + " / " + data.ParkOrder_CarTypeText);
                            opsObj.remove(passwayNo);

                            if (json.data.payres != null && json.data.payres.payed != 0) {
                                GetCurrentWorkShift();
                            }

                        } else {
                            alerts.error(json.msg);
                        }
                    }, "json").fail(function () {
                        alerts.error("确认放行异常");
                    }).always(function () {
                        $(e).prop("disabled", false).html("确认放行<t style='font-size: 1vw;'>(F1)<t>");
                    });
                }
                break;
            //改价
            case '5':
                var passway = pager.passwaydata.find((item) => { return item.Passway_No == passwayNo; });
                var data = HtmlParameter.getConfirmPassOut(passwayNo);
                if (data == null) return false;
                //$(e).attr("disabled", true);

                var modifyMoney = data.PayedMoney;
                $.post("ModifyMoney", { jsonModel: JSON.stringify(data) }, function (json) {
                    if (json.success) {
                        alerts.success(json.msg);
                        callback();
                        var opsExist = opsObj.data.find((item, index) => { return (item.id == passwayNo); });
                        if (opsExist != null) {
                            opsExist.out_payedamount = modifyMoney;
                        }
                        return true;
                    } else {
                        if (data.payedamount != undefined && data.payedamount != null) {
                            var li = $("li[data-key='" + passwayNo + "']");
                            $(li).find("input.outcar_money").val(data.payedamount);
                        }
                        alerts.error(json.msg);
                        $(".pwd-edit").removeClass("fa-rotate-left").removeClass("fa-pencil").addClass("fa-pencil");
                        $("input.outcar_money").attr("readonly", true);
                        return false;
                    }
                }, "json").fail(function () {
                    alerts.error("改价异常");
                    //$(e).removeAttr("disabled");
                    return false;
                });
                break;

            case '99'://打印收费小票
                var data = HtmlParameter.getConfirmPass(passwayNo);
                frmPrintIndex = layer.open({
                    type: 2,
                    title: "打印小票",
                    content: "PrintReceipt?CarNo=" + data.ParkOrder_CarNo + "&enterTime=" + data.ParkOrder_EnterTime + "&passwayNo=" + passwayNo + "&type=in&carCardType=" + data.ParkOrder_CarCardType,
                    area: getIframeArea(["380px", "600px"]),
                    maxmin: false
                })
                layer.close(idx);
                break;
            case '100'://打印小票
                var data = HtmlParameter.getConfirmPassOut(passwayNo);
                frmPrintIndex = layer.open({
                    type: 2,
                    title: "打印小票",
                    content: "PrintReceipt?CarNo=" + data.ParkOrder_CarNo + "&enterTime=" + data.ParkOrder_EnterTime + "&outTime=" + data.ParkOrder_OutTime + "&payedMoney=" + data.PayedMoney + "&passwayNo=" + passwayNo + "&type=out&carCardType=" + data.ParkOrder_CarCardType,
                    area: getIframeArea(["380px", "600px"]),
                    maxmin: false
                })
                layer.close(idx);
                break;
            case '101':
                console.log("订单详情:" + passwayNo)
                if (passwayNo == null || passwayNo == "") { alerts.error("订单号为空"); return; }
                //订单详情
                layer.open({
                    type: 2,
                    title: "订单详情",
                    content: "/Monitoring/ParkOrderDetail?ParkOrder_No=" + passwayNo,
                    area: ["90%", "90%"],
                    maxmin: false
                });
                break;
            case '102':
                //多车多位
                layer.open({
                    type: 2,
                    title: "多车多位",
                    content: "/Monitoring/MultiCar?ParkOrder_No=" + passwayNo + "&carNo=" + $(e).closest("li").find("input.iptcarno").val(),
                    area: ["750px", "660px"],
                    maxmin: false
                });
                break;
            default:
        }
    },
    //头部菜单按钮
    onBarClick: function (e) {
        var key = $(e).attr("data-key");
        console.log("头部菜单按钮：" + key)
        switch (key) {
            case '0':
                //layer.msg("刷新");
                window.location.reload();
                break;
            case '1':
                //layer.msg("全屏显示");
                if (!$(e).hasClass("open")) {
                    var de = document.documentElement;
                    if (de.requestFullscreen) {
                        de.requestFullscreen();
                    } else if (de.mozRequestFullScreen) {
                        de.mozRequestFullScreen();
                    } else if (de.webkitRequestFullScreen) {
                        de.webkitRequestFullScreen();
                    } else if (de.msRequestFullscreen) {
                        de.msRequestFullscreen();
                    }
                    else {
                        alerts.error("当前浏览器不支持全屏！");
                        return;
                    }
                    $(e).find("span").last().text(" 退出全屏");
                    $(e).addClass("open")
                } else {
                    if (document.exitFullscreen) {
                        document.exitFullscreen();
                    } else if (document.mozCancelFullScreen) {
                        document.mozCancelFullScreen();
                    } else if (document.webkitExitFullscreen) {
                        document.webkitExitFullscreen();
                    }
                    $(e).find("span").last().text(" 全屏显示");
                    $(e).removeClass("open")
                }
                break;
            case '2':
                break;
            case '3':
                layer.open({
                    type: 2,
                    title: false,
                    content: '/Monitoring/Record',
                    area: ["90%", "90%"],
                    maxmin: false
                });

                break;
            case '4':
                layer.open({
                    type: 2,
                    title: false,
                    content: '/Monitoring/Car',
                    area: ["90%", "90%"],
                    maxmin: false
                });
                break;
            case '100':
                if (btns.uiIncar) {
                    layer.close(btns.uiIncar);
                    btns.uiIncar = null;
                }

                btns.uiIncar = layer.open({
                    type: 2,
                    title: "<span class='incartitle'>岗亭后台人工调试操作</span>",
                    content: '/Monitoring/InCar',
                    area: ["1000px", "450px"],
                    maxmin: false,
                    shade: 0
                });
                break;
            case '8':
                layer.open({
                    type: 2,
                    title: false,
                    content: '/Monitoring/InParkRecord?inouttype=1',
                    area: ["90%", "90%"],
                    maxmin: false
                });
                break;
            case '9':
                layer.open({
                    type: 2,
                    title: false,
                    content: '/Monitoring/InParkRecord?inouttype=2',
                    area: ["90%", "90%"],
                    maxmin: false
                });
                break;
            default:
        }
    },
    //出场查询订单
    onGetOrderPay: function (e) {
        var passwayNo = $(e).attr("data-passwayno");
        var passway = pager.passwaydata.find((item) => { return item.Passway_No == passwayNo; });

        var data = HtmlParameter.getConfirmPassOut(passwayNo, true);
        if (data == null) return;
        $.post("GetParkOrder", { jsonModel: JSON.stringify(data) }, function (json) {
            if (json.success) {
                LPR.LPR_OPEN_FRAME(json.data, passway);
                LPR.LOAD_SHOW(json.data, passway);
            } else {
                $(".outcar_noincar").removeAttr("disabled");
                layui.form.render();
                alerts.error(json.msg);
            }
        }, "json").fail(function () {
            alerts.error("查询订单异常");
            $(".outcar_noincar").removeAttr("disabled");
            layui.form.render();
        });
    },
    //更多功能按钮
    onMenuBarClick: function (e) {
        var key = $(e).attr("data-key");
        switch (key) {
            case '0'://管理后台
                $.post("GetLoginAuthCode", {}, (json) => {
                    if (json.success) {
                        win_open(json.data);
                    } else {
                        alerts.error(json.msg);
                    }
                }, "json").fail(function () {
                    alerts.error("跳转异常");
                });
                break
            case '1'://播放设置
                var frmIndex = layer.open({
                    id: 'VideoPlayMode',
                    type: 2,
                    title: "播放设置",
                    content: "VideoPlayMode",
                    area: getIframeArea(["480px", "320px"]),
                    maxmin: false,
                    shade: 0,
                    btn: ["保存", "取消"],
                    yes: function (d, i) {
                        var ifr = $(i).find(".layui-layer-content iframe");
                        var form = ifr[0].contentWindow.document.getElementById('verify-form');
                        /*var param = $(form).formToJSON(true, function (data) { return data; });*/
                        var frameData = {};
                        $(form).find(".btnCombox").each(function () {
                            var idName = $(this).attr("id");
                            frameData[idName] = $(this).find("li.select").attr("data-value");
                        });

                        console.log(frameData)
                        modeConfig.setConfig(frameData);

                        if (sysconfig_playerType == 0) {
                            if (frameData.playerType != "" && frameData.playerType != playerType) {
                                localStorage.setItem("playerType", frameData.playerType);
                                window.location.reload();
                            }
                        }

                        if (!modeConfig.reLoad()) {
                            $(".monitor").html("");
                            localStorage.removeItem("selPasswayNoes");
                            pager.selData = [];
                            videoobj.init(pager.passwaydata);
                        }

                        $(".landi").addClass("layui-hide");
                        $(".barList").find("dd").first().addClass("checkAll").removeClass("checkNotAll").text("全选");
                        layer.close(frmIndex);
                    }
                })
                break
            case '2'://退出登录
                LAYER_OPEN_TYPE_0("退出登录不会生成换班记录并且清空累计金额，确定退出登录?", res => {
                    LAYER_LOADING("正在退出...");
                    $.post("LogOut", {}, (json) => {
                        location.href = "/Gt/Index";
                    }, "json").fail(function () {
                        alerts.error("退出登录异常");
                    });
                }, res => {
                })

                break
            case '3'://黑白名单下载
                var frmIndex = layer.open({
                    id: 'BackLoad',
                    type: 2,
                    title: "同步黑白名单",
                    content: "WhiteRecord",
                    area: getIframeArea(["1000px", "850px"]),
                    shade: 0,
                    maxmin: false
                });
                break
            case '4'://关于岗亭软件
                var frmIndex = layer.open({
                    id: 'MonitoringVersion',
                    type: 2,
                    title: "关于岗亭软件",
                    content: "MonitoringVersion",
                    shade: 0,
                    area: getIframeArea(["400px", "200px"]),
                    maxmin: false,
                })
                break
            case '5'://交班下班
                layer.open({
                    type: 2,
                    title: "换班登录",
                    content: "OffWorkChange",
                    area: getIframeArea(["980px", "680px"]),
                    maxmin: false
                })
                break;
            case '6'://显示设置
                var frmIndex = layer.open({
                    id: 'DisplayPart',
                    type: 2,
                    title: "",
                    content: "DisplayPart",
                    area: getIframeArea(["525px", "400px"]),
                    maxmin: false,
                    shade: 0,
                    closeBtn: 0,
                    btn: ["保存", "取消"],
                    yes: function (d, i) {
                        var frameData = modeConfig.getConfig();
                        if (frameData.hasOwnProperty("showModule")) {
                            if (frameData.showModule.monitor != modeConfig.value.showModule.monitor
                                || frameData.showModule.right != modeConfig.value.showModule.right
                                || frameData.showModule.record != modeConfig.value.showModule.record) {
                                frameData.showModule = modeConfig.value.showModule;
                                modeConfig.setConfig(frameData);
                            }
                        } else {
                            frameData.showModule = modeConfig.value.showModule;
                            modeConfig.setConfig(frameData);
                        }
                        modeConfig.loadModule();
                        layer.close(frmIndex);
                    },
                    btn2: function () {
                        var frameData = modeConfig.getConfig();
                        if (frameData.hasOwnProperty("showModule"))
                            modeConfig.value.showModule = frameData.showModule;
                        else {
                            frameData["showModule"] = modeConfig.value.showModule;
                            modeConfig.setConfig(frameData);
                        }
                        modeConfig.loadModule();
                    },
                })
                break
            case '8'://字体设置
                var frmIndex = layer.open({
                    id: 'FrontSetting',
                    type: 2,
                    title: "界面设置",
                    content: "FrontSetting",
                    area: getIframeArea(["460px", "390px"]),
                    maxmin: false,
                    shade: 0,
                    btn: ["保存", "取消"],
                    yes: function (d, i) {
                        var ifr = $(i).find(".layui-layer-content iframe");
                        var form = ifr[0].contentWindow.document.getElementById('verify-form');
                        /*var param = $(form).formToJSON(true, function (data) { return data; });*/
                        var sysfont = 0;
                        $(form).find(".btnCombox").each(function () {
                            sysfont = $(this).find("li.select").attr("data-value");
                        });

                        var uiVersion = "standard-font";
                        if (sysfont == 1) uiVersion = "large-font"; else uiVersion = "standard-font";
                        localStorage.setItem("b30uiversion", uiVersion);

                        var frameData = modeConfig.getConfig();
                        if (frameData.hasOwnProperty("showModule")) {
                            if (frameData.showModule.monitor != modeConfig.value.showModule.monitor
                                || frameData.showModule.right != modeConfig.value.showModule.right
                                || frameData.showModule.record != modeConfig.value.showModule.record) {
                                frameData.showModule = modeConfig.value.showModule;
                                modeConfig.setConfig(frameData);
                            }
                        } else {
                            frameData.showModule = modeConfig.value.showModule;
                            modeConfig.setConfig(frameData);
                        }
                        modeConfig.loadModule();

                        window.location.reload();
                    }, btn2: function () {
                        var frameData = modeConfig.getConfig();
                        if (frameData.hasOwnProperty("showModule"))
                            modeConfig.value.showModule = frameData.showModule;
                        else {
                            frameData["showModule"] = modeConfig.value.showModule;
                            modeConfig.setConfig(frameData);
                        }
                        modeConfig.loadModule();
                    },
                })
                break
            case '66'://打印小票设置
                frmPrintIndex = layer.open({
                    type: 2,
                    title: "打印小票设置",
                    content: "PrintReceiptSetting",
                    area: getIframeArea(["380px", "600px"]),
                    maxmin: false
                })
                break;
            default:
                break;
        }
    },
    showVoiceList: function (passwayno, e) {
        console.log("showVoiceList:" + passwayno)
        layer.open({
            type: 2,
            title: "车道常用提示语音",
            content: "/Monitoring/CustomBroadVoice?Passway_No=" + passwayno,
            area: ["500px", "350px"],
            maxmin: false
        });
    }
}

//确认放行参数
var HtmlParameter = {
    //入场参数
    getConfirmPass: function (passwayno) {
        var ops = opsObj.data.find((item) => { return item.id == passwayno });
        var li = $("li[data-key='" + passwayno + "']");
        var data = {
            mode: ops.mode,
            ParkOrder_EnterPasswayNo: passwayno,
            ParkOrder_EnterImgPath: $(li).find("img.enter_img").attr("data-src"),
            ParkOrder_CarNo: $(li).find("input.enter_carno").val(),
            ParkOrder_CarCardType: $(li).find("select.enter_carcardtype").val(),
            ParkOrder_CarCardTypeText: $(li).find("select.enter_carcardtype option:selected").text(),
            ParkOrder_CarType: $(li).find("select.enter_cartype").val(),
            ParkOrder_CarTypeText: $(li).find("select.enter_cartype option:selected").text(),
            ParkOrder_EnterReamrk: $(li).find("input.enter_remark").val(),
            ParkOrder_EnterTime: $(li).find("value.enter_time").text(),
        }

        if (data.mode == null || (!data.ParkOrder_EnterPasswayNo)) { alerts.error("异常错误,请重试"); return null; }
        if (!data.ParkOrder_CarNo) { alerts.error("车牌号为空"); return null; }
        if (!data.ParkOrder_CarCardType) { alerts.error("车牌类型为空"); return null; }
        if (!data.ParkOrder_CarType) { alerts.error("车牌颜色为空"); return null; }

        return data;
    },
    //出场参数
    getConfirmPassOut: function (passwayno, isFirstGetData) {

        var ops = opsObj.data.find((item) => { return item.id == passwayno });
        if (ops == undefined) return null;

        var li = $("li[data-key='" + passwayno + "']");
        var outnoenter = 0;
        if ($(".outcar_noincar").attr("checked")) {
            var outnoenter = 1;
        }
        var sltCoupon = null;
        var CouponList = [];

        if (ops.out_seldata != undefined && ops.out_seldata && ops.out_seldata != null && ops.out_seldata.length > 0) {
            CouponList = ops.out_seldata;
        }

        if (ops.out_usecoupon && ops.out_usecoupon != null && ops.out_usecoupon.length > 0) {
            pager.parkdiscountset.forEach((item, index) => {
                if (ops.out_usecoupon.some(coupon => coupon.CouponRecord_No === item.CouponRecord_No)) {
                    if (!CouponList.some(coupon => coupon.CouponRecord_No === item.CouponRecord_No)) {
                        CouponList.push(item);
                    }
                }
            });
        }

        //else {
        //    if (ops.out_coupon != null && ops.out_usecoupon != null) {
        //        ops.out_coupon.forEach((item, index) => {
        //            var n = ops.out_usecoupon.find((v, index) => { return v.CouponRecord_No == item.CouponRecord_No; });
        //            if (n != null) {
        //                CouponList[CouponList.length] = item;
        //            }
        //        });
        //    }
        //}

        var data = {
            mode: ops.mode,
            OutNoEnter: outnoenter,
            ParkOrder_OutPasswayNo: passwayno,
            ParkOrder_OutImgPath: $(li).find("img.outcar_img").attr("data-src"),
            ParkOrder_CarNo: $(li).find("input.outcar_carno").val(),
            ParkOrder_CarCardType: $(li).find("select.out_carcardtype").val(),
            ParkOrder_CarCardTypeText: $(li).find("select.out_carcardtype option:selected").text(),
            ParkOrder_CarType: $(li).find("select.outcar_cartype").val(),
            ParkOrder_CarTypeText: $(li).find("select.outcar_cartype option:selected").text(),
            ParkOrder_FreeReason: $(li).find("input.outcar_free").val(),
            orderamount: ops.out_orderamount,
            payedamount: ops.out_payedamount,
            PayedMoney: $(li).find("input.outcar_money").val(),
            CouponList: JSON.stringify(CouponList),
            ParkOrder_EnterTime: $(li).find(".enter_time").text(),
            ParkOrder_OutTime: $(li).find(".out_time").text()
        }

        if (data.mode == null || (!data.ParkOrder_OutPasswayNo)) { alerts.error("异常错误,请重试"); return null; }
        if (!data.ParkOrder_CarNo) { alerts.error("车牌号为空"); return null; }
        if (!data.ParkOrder_CarType) { alerts.error("车牌颜色为空"); return null; }
        if (!data.ParkOrder_CarCardType) { alerts.error("车牌类型为空"); return null; }

        return data;
    }
}
var timetipsIndex;
//场内记录
var inParkOrder = {
    data: [],
    table: null,
    onLoad: function () {
        var carno = $("#inParkOrder_CarNo").val();
        var conditionParam = { ParkOrder_CarNo: carno };
        var cols = [[
            {
                field: 'ParkOrder_CarNo', title: '车牌号', minWidth: 152, templet: function (d) {
                    return '<span title="点击查看" data-orderno="' + d.ParkOrder_No + '" class="carnoys">' + d.ParkOrder_CarNo + '</span>'
                }
            }
            , { field: 'ParkOrder_CarCardTypeName', title: '车牌类型', minWidth: 100 }
            , {
                field: 'ParkOrder_EnterTime', title: '入场时间', minWidth: 180, templet: function (d) {
                    return '<t  class="orderentertime">' + d.ParkOrder_EnterTime + '</t>'
                }
            }
            //, {
            //    field: 'ParkOrder_EnterImgPath', title: '图片', width: 80, templet: function (d) {
            //        var img = PathCheck(decodeURIComponent(d.ParkOrder_EnterImgPath));
            //        if (isFrpUrl) {
            //            img = replaceFirstPathSegment(img);
            //        }
            //        var bar1 = tempImg(img);
            //        return bar1;
            //    }
            //}
            , { field: 'ParkOrder_ParkAreaName', title: '区域', minWidth: 85 }

        ]];

        var colsOptionWidth = 90;
        if (!(formPower && formPower.ModifyRecord) || !(formPower && formPower.PassCarNo)) colsOptionWidth = 60;

        if (formPower && formPower.PassCarNo || formPower && formPower.ModifyRecord) {
            cols[0].push({
                title: '操作', fixed: "right", minWidth: colsOptionWidth, templet: function (d) {

                    var show = '';

                    if (formPower.PassCarNo) show += ('<i class="fa fa-share-square-o edit-icon" title="出场" onclick="inParkOrder.onClick(\'' + d.ParkOrder_No + '\',this)"></i>');
                    if (formPower.ModifyRecord) show += ('<i class="fa fa-edit edit-icon" title="修改场内记录"  onclick="inParkOrder.onClickUpdateCarNo(\'' + d.ParkOrder_No + '\',this)"></i>');
                    return show;
                }
            })
        }

        //根据浏览器可显示的高度来显示表格行数
        var row = 5;
        //var height = $("div.record").height() - 170;
        //console.log(height)
        //if (height < 100) height = 100;
        //var row = Math.floor(height / 50);

        inParkOrder.page = { layout: ['count', 'prev', 'page', 'next'], groups: 3 };

        inParkOrder.table = layui.table.render({
            elem: '#inparkorder-table'
            , url: '/Monitoring/GetInParkCarList'
            , method: 'post'
            , toolbar: false
            , defaultToolbar: ["filter"]
            , cols: cols
            , page: inParkOrder.page
            , totalRow: false
            , loading: false
            , request: { pageName: 'pageIndex', limitName: 'pageSize' }
            , where: { conditionParam: JSON.stringify(conditionParam) }
            , limits: [row]
            , limit: row
            , done: function (data) {
                inParkOrder.data = data.data;
                $(".carnoys").unbind("click").click(function (e) {
                    var orderno = $(this).attr("data-orderno");
                    if (orderno != undefined && orderno != null) {
                        $.post("/Monitoring/GetCarOrder", { orderno: orderno }, function (res) {
                            if (res.success) {
                                if (res.data != null) {
                                    var enterSrc = res.data.ParkOrder_EnterImgPath;
                                    $("#rlt_enterimg a").attr("href", enterSrc || '');
                                    $("#rlt_enterimg a img").attr("title", "入场图片").attr("src", '').attr("data-src", '');

                                    var imgtool1 = new imgtool();
                                    var imgtool2 = new imgtool();
                                    imgtool1.imageLoad("in" + orderno + Math.random(), enterSrc, 0, $("#rlt_enterimg a img"), function () { $("#rlt_enterimg a img").attr("title", "入场图片").attr("src", enterSrc || '').attr("data-src", enterSrc || ''); $("#rlt_enterimg a img").get(0).src = enterSrc; $("#rlt_enterimg a img").eq(0).css("display", ""); imgtool1.clear(); })

                                    var outSrc = PathCheck(res.data.ParkOrder_OutImgPath);
                                    if (isFrpUrl) {
                                        outSrc = replaceFirstPathSegment(outSrc);
                                    }
                                    $("#rlt_outimg a").attr("href", outSrc || '');
                                    $("#rlt_outimg a img").attr("title", "入场图片").attr("src", '').attr("data-src", '');
                                    imgtool2.imageLoad("out" + orderno + Math.random(), outSrc, 0, $("#rlt_outimg a img"), function () { $("#rlt_outimg a img").attr("title", "出场图片").attr("src", outSrc || '').attr("data-src", outSrc || ''); $("#rlt_outimg a img").eq(0).css("display", ""); imgtool2.clear(); })
                                    $("#rlt_carno").attr("title", res.data.ParkOrder_CarNo).html(res.data.ParkOrder_CarNo);
                                    //$("#rlt_img").html('<a href="' + img + '" target="_blank">预览</a>');
                                    $("#rlt_passway").attr("title", res.data.ParkOrder_EnterPasswayName).html(res.data.ParkOrder_EnterPasswayName);
                                    $("#rlt_area").attr("title", res.data.ParkOrder_ParkAreaName).html(res.data.ParkOrder_ParkAreaName);
                                    $("#rlt_time").attr("title", res.data.ParkOrder_EnterTime).html(res.data.ParkOrder_EnterTime);
                                    $("#rlt_card").attr("title", res.data.ParkOrder_CarCardTypeName + " / " + res.data.ParkOrder_CarTypeName).html(res.data.ParkOrder_CarCardTypeName + " / " + res.data.ParkOrder_CarTypeName);
                                    $("#rlt_owner").attr("title", res.data.ParkOrder_OwnerName).html(res.data.ParkOrder_OwnerName || '');
                                    $("#rlt_remark").attr("title", res.data.ParkOrder_Remark).html(res.data.ParkOrder_Remark || '');
                                    $('#rlt_calctime').attr("title", '').html('');
                                    $('#rlt_calcmoney').attr("title", '').html('');
                                    $('#rlt_errmsg').attr("title", '').html('');
                                    $("#rlt_enterimgsmall").html("");
                                    $("#rlt_outimgsmall").html("");
                                } else {
                                    LAYER_MSG("未找到停车订单信息");
                                }
                            } else {
                                LAYER_MSG(res.msg);
                            }
                        })
                    }
                });

                $(".orderentertime").hover(
                    function (e) {
                        timetipsIndex = layer.tips($(this).text(), $(this), { time: 0, tips: [1, '#5868e0'], skin: 'time-tips' });
                    },
                    function (e) {
                        layer.close(timetipsIndex);
                    }
                );
            }
        });
    },
    onSearch: function () {
        var carno = $("#inParkOrder_CarNo").val();
        var conditionParam = { ParkOrder_CarNo: carno };
        inParkOrder.table.reload({
            url: '/Monitoring/GetInParkCarList'
            , where: { conditionParam: JSON.stringify(conditionParam) }
            , page: inParkOrder.page
        });

        GetCurrentWorkShift();
    },
    onClick: function (orderno, e) {
        //获取停车区域关联的车道，点出场时可选择从哪个车道离开
        var order = inParkOrder.data.find((item, index) => { return item.ParkOrder_No == orderno; });
        var passways = [];
        pager.arealinkpassway.forEach((item, index) => { if (item.PasswayLink_ParkAreaNo == order.ParkOrder_ParkAreaNo && item.PasswayLink_GateType == 0) passways[passways.length] = item; });

        if (passways.length == 0) { alerts.error("区域未设置出口车道<br/>或车道不在当前岗亭管理范围内"); return; }
        else if (passways.length == 1) { inParkOrder.openOrderOut(orderno, passways[0].PasswayLink_PasswayNo, e); return; }
        else {
            //不止一个出口车道，则弹出车道选项
            $("#chosenOutGate").html("");
            $('#chosenOutGate').parent().prev('.chheader').html("【" + order.ParkOrder_CarNo + "】请选择出口");
            passways.forEach((item, index) => {
                $("#chosenOutGate").append('<li data-orderno="' + orderno + '" data-passwayno="' + item.PasswayLink_PasswayNo + '">' + item.Passway_Name + '</li>');
                $("#chosenOutGate li").unbind("click").click(function () {
                    inParkOrder.openOrderOut($(this).attr("data-orderno"), $(this).attr("data-passwayno"), e);
                });
            });
            $(".outPasswayChosen").removeClass("layui-hide");
        }
    },
    onClickUpdateCarNo: function (orderno, e) {
        if (orderno != undefined && orderno != null) {
            console.log(encodeURIComponent(orderno))
            layer.open({
                title: "<i class='fa fa-list-alt' style='margin-top: 17px;'></i> 修改场内记录",
                type: 2, id: orderno,
                area: getIframeArea(['780px', '440px']),
                fix: false, //不固定
                maxmin: false,
                content: 'EditInParkRecord?ParkOrder_No=' + encodeURIComponent(orderno)
            });
        }
    },
    //场内记录出场，弹窗
    openOrderOut: function (orderno, passwayno, e) {
        console.log(encodeURIComponent(orderno))
        orderno = orderno.trim();
        passwayno = passwayno.trim();
        $(".outPasswayChosen").removeClass("layui-hide").addClass("layui-hide");

        var order = inParkOrder.data.find((d) => { return d.ParkOrder_No == orderno; });
        var passway = pager.passwaydata.find((item) => { return item.Passway_No == passwayno; });

        var data = {
            mode: 1,
            ParkOrder_OutPasswayNo: passwayno,
            ParkOrder_OutImgPath: "",
            ParkOrder_CarNo: order.ParkOrder_CarNo,
            ParkOrder_CarCardType: order.ParkOrder_CarCardType,
            ParkOrder_CarType: order.ParkOrder_CarType
        }
        var carno = data.ParkOrder_CarNo;

        $(e).addClass("aDisabled");
        //var layerIndex1 = layer.msg("加载中", { icon: 16, time: 0 });
        //抓拍
        videoobj.snap(passwayno, (d) => {
            $(e).removeClass("aDisabled");
            //layer.close(layerIndex1);
            var display = d.displayImg;
            var img = d.tempImage;
            data.ParkOrder_OutImgPath = img;
            inParkOrder.openOrderOutDetail(passway, passwayno, data, display, carno);
        }, error => {
            $(e).removeClass("aDisabled");
            //layer.close(layerIndex1);
            alerts.error(error);
            inParkOrder.openOrderOutDetail(passway, passwayno, data, "", carno);
        });
    },
    //场内记录出场，弹窗
    openOrderOutDetail: function (passway, passwayno, data, display, carno) {
        $.post("GetParkOrder", { jsonModel: JSON.stringify(data) }, function (json) {
            if (json.success) {
                if (pager && json.data && json.data.data) {
                    pager.calcDetail[selOutPasswayNo] = json.data.data.calcdetail;
                    if (data.payres && data.payres.payedmsg) { pager.payedmsg = data.payres.payedmsg; } else { pager.payedmsg = ""; }
                } else { pager.calcDetail[selOutPasswayNo] = null; }

                LPR.LPR_OPEN_FRAME(json.data, passway);
                LPR.LOAD_SHOW(json.data, passway);
                if (opsObj.data) {
                    var findItem = opsObj.data.find((item) => { return item.id == passwayno && item.carno == carno; });
                    if (findItem && findItem != null) {
                        $("li[data-key='" + passwayno + "']").find("img.outcar_img").attr("data-src", display).attr("src", display);
                        //拍照成功，加载出场图片显示(确认放行后的显示)
                        $("#rlt_outimg a").attr("href", display || '');
                        var imgtool1 = new imgtool();
                        imgtool1.imageLoad("out" + passwayno, display, 0, $("#rlt_outimg a img"), function () { $("#rlt_outimg a img").attr("title", "出场图片").attr("src", display || '').attr("data-src", display || ''); imgtool1.clear(); })
                    }
                }
            } else {
                alerts.error(json.msg);
            }
        }, "json").fail(function () {
            alerts.error("场内记录出场异常");
        });
    }
}


//刷新当班收费
let lastWorkShiftCallTime = 0;
var GetCurrentWorkShift = function () {

    const now = Date.now();

    // 如果15秒内已调用，则跳过
    if (now - lastWorkShiftCallTime < 15000) {
        console.log("GetCurrentWorkShift 调用过于频繁，已忽略。");
        return;
    }

    lastWorkShiftCallTime = now;
    $.post("GetCurrentWorkShift", {}, function (json) {
        if (json.success) {
            // 如果当前数据与缓存数据相同，则不更新
            if (lastWorkShiftData && JSON.stringify(lastWorkShiftData) === JSON.stringify(json.data)) {
                return; // 数据没有变化，不做任何操作
            }

            // 更新缓存数据
            lastWorkShiftData = json.data;

            if (formPower && formPower.ShowMoney) {
                $("#work_0").text(json.data.cashMoneys || '0.00');
                $("#work_1").text(json.data.ysMoneys || '0.00');
                $("#work_2").text(json.data.jmMoneys || '0.00');
                $("#work_3").text(json.data.ssMoneys || '0.00');
                $("#work_8").text(json.data.electronicMoneys || '0.00');
            } else {
                $("#work_0").text('--');
                $("#work_1").text('--');
                $("#work_2").text('--');
                $("#work_3").text('--');
                $("#work_8").text('--');
            }

            $("#work_4").text(json.data.speosonNum || '0');
            $("#work_5").text(json.data.spNums || '0');
            $("#work_6").text(json.data.time || '');
            $("#work_7").text(decodeURIComponent(json.data.name) || '');
        } else {
            console.log("刷新当班收费失败：" + json.msg);
        }
    }, "json").fail(function () {
        alerts.error("刷新当班收费异常");
    });
};
//刷新车位信息
var GetCurrentCarSpace = function () {
    $.post("GetCurrentCarSpace", {}, function (json) {
        if (!json.success) {
            console.log("刷新车位信息失败：" + json.msg);
            return; // 如果请求失败，退出函数
        }

        // 如果数据与缓存数据相同，则不更新UI
        if (lastCarSpaceData && JSON.stringify(lastCarSpaceData) === JSON.stringify(json.data)) {
            return; // 数据没有变化，不做任何操作
        }

        // 更新缓存数据
        lastCarSpaceData = json.data;

        if (json.data != null) {
            // 更新车位信息
            $("#space_0").text(json.data.Item1 || "0");
            $("#space_1").text(json.data.Item2 || "0");

            // 清空原有车位区域内容
            $("#space_area").html("");

            // 渲染车位区域
            json.data.Item4.forEach(function (item, index) {
                $("#space_area").append(' <li class="color_0"><ndiv>[' + item.Item4 + '] 车位</ndiv><vdiv class="' + getColor(item.Item2) + '">' + item.Item2 + '</vdiv></li>');
                $("#space_area").append(' <li class="color_0"><ndiv>[' + item.Item4 + '] 余位</ndiv><vdiv class="' + getColor(item.Item3) + '">' + item.Item3 + '</vdiv></li>');
            });

            // 添加最后一项空白行
            $("#space_area").append(' <li><ndiv></ndiv><vdiv ></vdiv></li>');

            // 渲染余位信息
            json.data.Item3.forEach(function (item, index) {
                if (!isNaN(item.Item3) && parseInt(item.Item3) > 0) {
                    $("#space_area").append(' <li><ndiv>' + item.Item2 + '</ndiv><vdiv class="' + getColor(item.Item3) + '">' + parseInt(item.Item3) + '</vdiv></li>');
                }
            });
        }
    }, "json").fail(function () {
        alerts.error("刷新车位信息异常");
    });
};
//获取云端在线状态
var GetParkingState = function () {
    $.post("GetParkingState", {}, function (json) {
        if (json.success) {
            var currentParkingState = json.data; // 当前云平台状态

            // 如果当前状态和缓存状态相同，则不更新
            if (currentParkingState === lastParkingState) {
                return; // 状态没有变化，不做任何操作
            }

            // 更新缓存状态
            lastParkingState = currentParkingState;

            // 更新UI
            if (currentParkingState == 1) {
                $("#cloudOnline").removeClass("close").removeClass("connet").removeClass("loading").addClass("connet");
                $("#cloudOnline dd").last().text("云平台连接正常");
            } else if (currentParkingState == 0) {
                $("#cloudOnline").removeClass("close").removeClass("connet").removeClass("loading").addClass("close");
                $("#cloudOnline dd").last().text("云平台连接离线");
            } else {
                $("#cloudOnline").removeClass("close").removeClass("connet").removeClass("loading").addClass("loading");
                $("#cloudOnline dd").last().text("未启用云平台");
            }
        } else {
            console.log("中间件离线：" + json.msg);
            $("#cloudOnline").removeClass("close").removeClass("connet").removeClass("loading").addClass("close");
            $("#cloudOnline dd").last().text("云平台连接异常");
        }
    }, "json").fail(function () {
        alerts.error("获取云端在线状态异常");
    });
};

//获取停车场主机状态
var GetHostState = function () {
    $.post("GetHostState", {}, function (json) {
        if (json.success) {
            var currentHostState = json.data; // 当前主机状态

            // 如果当前状态和缓存状态相同，则不更新
            if (currentHostState === lastHostState) {
                return; // 状态没有变化，不做任何操作
            }

            // 更新缓存状态
            lastHostState = currentHostState;

            // 更新UI
            if (currentHostState == 1) {
                $("#hostOnline").removeClass("close").removeClass("connet").removeClass("loading").addClass("connet");
                $("#hostOnline dd").last().text("主机连接正常");
            } else {
                $("#hostOnline").removeClass("close").removeClass("connet").removeClass("loading").addClass("close");
                $("#hostOnline dd").last().text("主机连接离线");
            }
        } else {
            console.log("主机离线：" + json.msg);
            $("#hostOnline").removeClass("close").removeClass("connet").removeClass("loading").addClass("close");
            $("#hostOnline dd").last().text("主机连接离线");
        }
    }, "json").fail(function () {
        alerts.error("获取停车场主机状态异常");
    });
};

function triggerLiFlashWithIcon($li) {
    // 图标旋转重触发
    let $icon = $li.find(".ttubiao");
    $icon.removeClass("spin-once");
    void $icon[0].offsetWidth;
    $icon.addClass("spin-once");
}

//当前浏览器缓存数据
var localCache = {
    //监控画面
    videodata: [
        { id: "video1", passwayno: "" },
        { id: "video2", passwayno: "" }
    ],
    //最后一次识别的车牌信息
    lastOrder: {},
    //消息记录
    message: [],

    //设置监控画面
    setCamera: function (data) {
        if (this.videodata.length < 1) {
            this.videodata = [
                { id: "video1", passwayno: "" },
                { id: "video2", passwayno: "" }
            ]
        }
        if (data[0] && data[0].model) {
            this.videodata[0].passwayno = data[0].model.Passway_No;
        }
        if (data[1] && data[1].model) {
            this.videodata[1].passwayno = data[1].model.Passway_No;
        }
        this.save();
    },

    //设置最后一次识别信息
    setLastOrder: function (data) {
        this.lastOrder = data;
        this.save();
    },

    //设置消息记录
    setMessage: function (data) {
        this.message = data;
        this.save();
    },

    //保存到本地缓存
    save: function () {
        var json = JSON.stringify(localCache);
        localStorage.setItem("bs_localcache", json);
    },

    //打开页面时加载缓存信息
    init: function () {
        var json = localStorage.getItem("bs_localcache");
        if (json != null && json != '') {
            var data = JSON.parse(json);
            if (data.videodata && data.videodata.length > 0) {
                this.videodata = data.videodata;
            }
            this.lastOrder = data.lastOrder;
            this.message = data.message;

            //if (this.videodata != null && this.videodata.length != 0) {
            //    videoobj.data = this.videodata;
            //    videoobj.reload();
            //}

            //if (this.lastOrder.obj != null)
            //    LPR.RECOGRESULT(this.lastOrder.obj, this.lastOrder.passway);

            //if (this.message.obj != null)
            //    LPR.MESSAGE(this.message.obj, this.message.passway);
        }
    }
}

//获取车辆信息列表
var GetInCarList = function (obj, newcarno) {
    try {
        var carno = '';
        if (newcarno) {
            newcarno = '' + newcarno;
            var carno = newcarno
                .replace(/,/g, '')         // 移除逗号
                .replace(/[ ]/g, '')       // 移除空格（如果有）
                .replace(/'/g, '');        // 移除单引号（如果有）
            $(obj).val(carno);
            carno = carno.replace(/[^\x00-\xff]/g, '') // 移除非 ASCII 字符（如果有）
        }
        else {
            carno = $(obj).val()
                .replace(/,/g, '')         // 移除逗号
                .replace(/[ ]/g, '')       // 移除空格（如果有）
                .replace(/[^\x00-\xff]/g, '') // 移除非 ASCII 字符（如果有）
                .replace(/'/g, '');        // 移除单引号（如果有）
        }

        if (carno != "" && carno.length >= 3) {
            setTimeout(function () {
                $.post("GetInCarList", { carno: carno }, function (json) {
                    if (json.success) {
                        if (json.data && json.data.length > 0) {
                            var carLI = $("#tmplCarList").tmpl(json.data);
                            $(".carlist").html("");
                            $.each(carLI, function (m, n) {
                                $(".carlist").append(n.innerHTML);
                            })
                            $(".selcarno").removeClass("hide");

                            var $iptCarno = $(".iptcarno:visible");

                            if ($iptCarno.length) {
                                var offset = $iptCarno.offset(); // 获取绝对位置
                                var height = $iptCarno.outerHeight();
                                var width = $iptCarno.outerWidth();

                                var selcarnoWidth = $(".selcarno").outerWidth();
                                var selcarnoHeight = $(".selcarno").outerHeight();
                                var windowWidth = $(window).width(); // 获取窗口宽度
                                var windowHeight = $(window).height(); // 获取窗口高度
                                var scrollLeft = $(window).scrollLeft(); // 获取水平滚动距离
                                var scrollTop = $(window).scrollTop(); // 获取垂直滚动距离

                                // **左侧优先**，但如果超出屏幕则放右侧
                                var l = offset.left - selcarnoWidth - 5;
                                if (l < scrollLeft) { // 如果超出左边界，则放到右侧
                                    l = offset.left + width + 5;
                                }

                                // **调整 top，避免超出底部**
                                var t = offset.top;
                                if (t + selcarnoHeight > windowHeight + scrollTop) { // 超出底部时，上移
                                    t = windowHeight + scrollTop - selcarnoHeight - 5;
                                }

                                // **确保不会超出右侧**
                                if (l + selcarnoWidth > windowWidth + scrollLeft) {
                                    l = windowWidth + scrollLeft - selcarnoWidth - 5;
                                }

                                console.log(t + "," + l)

                                // **设置最终位置**
                                $(".selcarno").css({
                                    top: t + "px",
                                    left: l + "px",
                                });
                            }


                            $(".laycarno_box").remove();

                            $(".selcarno a").off("click").on("click", function (e) {
                                var car = $(this).text();
                                $(".outcar_carno").val(car);
                                setTimeout(function () {
                                    $(".outcar_carno").parents(".layui-col-md7").find("button").click();
                                }, 10);
                                $(".selcarno").removeClass("hide").addClass("hide");
                            });
                        } else {
                            $(".carlist").html("");
                            $(".selcarno").removeClass("hide").addClass("hide");
                        }

                        if ($(".sui-carw:visible").length > 0) {
                            $(".selcarno").addClass("selcarno2");
                        } else {
                            $(".selcarno").removeClass("selcarno2");
                        }
                    } else {
                        alerts.error("获取车辆信息错误：" + json.msg);
                        $(".selcarno").removeClass("hide").addClass("hide");
                    }
                }, "json").fail(function () {
                    alerts.error("获取车辆信息异常");
                    $(".selcarno").removeClass("hide").addClass("hide");
                });
            }, 1)
        } else {
            $(".selcarno").removeClass("hide").addClass("hide");
        }
    } catch (e) {
        console.error(e)
    }
}

//键盘监听
$(window).keydown(function (e) {
    var keyCode = e.keyCode;
    try {
        switch (keyCode) {
            // user presses the "F1" 出场确定放行
            case 112:
                $("button.out:visible").first().click();
                break;
            // user presses the "F2" key 入场确定放行
            case 113:
                $("button.in:visible").first().click();
                break;
        }
    } catch (e) {
        console.log("快捷键：" + keyCode + "," + e.message)
    }
});

var getParkMin = function (enterTime, outTime) {
    var d1 = new Date(enterTime);
    var d2 = new Date(outTime);
    var min = Math.floor(parseInt(d2 - d1) / 1000 / 60);
    return _DATE.getZhTimesbyMin(Math.ceil(min));
}


var getColor = function (num) {
    if (num < 0) return "color_999";
    else if (num <= 100) return "color_0";
    else if (num <= 200) return "color_1";
    else if (num <= 300) return "color_2";
    else if (num <= 400) return "color_3";
    else if (num <= 500) return "color_4";
    else if (num <= 800) return "color_5";
    else return "color_6";
}

//document.addEventListener('DOMContentLoaded', (event) => {
//    const draggable = document.getElementById('winDraggable');
//    draggable.addEventListener('mousedown', (e) => {
//        let shiftX = e.clientX - draggable.getBoundingClientRect().left;
//        let shiftY = e.clientY - draggable.getBoundingClientRect().top;

//        function moveAt(pageX, pageY) {
//            draggable.style.left = pageX - shiftX + 'px';
//            draggable.style.top = pageY - shiftY + 'px';
//        }

//        function onMouseMove(event) {
//            moveAt(event.pageX, event.pageY);
//        }

//        document.addEventListener('mousemove', onMouseMove);

//        draggable.onmouseup = function () {
//            document.removeEventListener('mousemove', onMouseMove);
//            draggable.onmouseup = null;
//        };
//    });
//    draggable.ondragstart = function () {
//        return false;
//    };

//});

let initialTop = 0;
let initialLeft = 0;
let isAtBottom = false; // 状态标识
$(function () {
    $(".docfb-popup__btn").click(function (e) {
        const draggable = document.getElementById('winDraggable');
        const box = document.getElementById('box');
        if (isAtBottom) {
            // 恢复到原始位置
            draggable.style.top = `${initialTop}px`;
            draggable.style.left = `${initialLeft}px`;
            $(this).find("i").text("").removeClass("layui-icon-up").addClass("layui-icon-down")

            box.style.display = 'block';
            e.currentTarget.style.bottom = 'auto';
            e.currentTarget.style.top = '-40px';
        } else {
            // 记录初始位置
            initialTop = parseInt(window.getComputedStyle(draggable).top, 10);
            initialLeft = parseInt(window.getComputedStyle(draggable).left, 10);
            const rect = draggable.getBoundingClientRect();
            const viewportWidth = window.innerWidth;
            const viewportHeight = window.innerHeight;
            const elementWidth = rect.width;
            const elementHeight = rect.height;
            // 移动到视口底部
            const newLeft = Math.max(0, viewportWidth - elementWidth);
            draggable.style.top = `${viewportHeight - 2}px`;
            draggable.style.left = `${newLeft}px`;
            $(this).find("i").text("打开岗亭弹窗").removeClass("layui-icon-down").addClass("layui-icon-up")

            box.style.display = 'none';
            e.currentTarget.style.top = 'auto';
            e.currentTarget.style.bottom = '0';
        }

        isAtBottom = !isAtBottom; // 切换状态

    })

    $(document).click(function (event) {
        $(".sui-carw").removeClass("sui-carw-hide").addClass("sui-carw-hide");
        var selectBox = document.querySelector('.xm-body');  // 获取 xmSelect 下拉框
        var toggleButton = document.querySelector('.xm-icon'); // 获取倒三角按钮
        if (selectBox && toggleButton && !selectBox.classList.contains('dis')) {
            toggleButton.click(); // 触发点击收起
        }
        $(".outWin .selcarno").removeClass("hide").addClass("hide");

    });
})

// 车辆到达特效
function showVehicleArrivalEffect(data, type = 'exit') {
    // 清理之前的特效，确保同一时间只有一个特效
    $('.vehicle-arrival-effect').remove();

    // 调试信息：记录特效类型和车辆信息
    console.log('显示车辆到达特效:', {
        type: type,
        carno: data.carno,
        dataType: data.type,
        timestamp: new Date().toLocaleTimeString()
    });

    // 创建特效容器
    var effectContainer = $('<div class="vehicle-arrival-effect"></div>');

    // 车辆信息
    var carNo = data.carno || '未识别车牌';
    var passway = type === 'enter' ? (data.enter_passway || '未知车道') : (data.out_passway || '未知车道');
    var time = new Date().toLocaleTimeString();
    var isEnter = type === 'enter';

    // 根据类型设置不同的样式和文字
    var titleText = isEnter ? '车辆入场' : '车辆出场';
    var iconClass = isEnter ? 'layui-icon-right' : 'layui-icon-left';
    var gradientColors = isEnter ?
        'linear-gradient(135deg, #11998e 0%, #38ef7d 100%)' :
        'linear-gradient(135deg, #667eea 0%, #764ba2 100%)';
    var plateColor = isEnter ? '#00FF7F' : '#FFD700';

    // 创建特效HTML
    var effectHTML = `
        <div class="arrival-notification" style="background: ${gradientColors};">
            <div class="notification-header">
                <div class="pulse-ring"></div>
                <div class="notification-icon">
                    <i class="layui-icon ${iconClass}"></i>
                </div>
                <div class="notification-title">${titleText}</div>
            </div>
            <div class="notification-content">
                <div class="car-info">
                    <div class="car-plate" style="color: ${plateColor};">${carNo}</div>
                    <div class="car-details">
                        <span class="passway-info">${passway}</span>
                        <span class="time-info">${time}</span>
                    </div>
                </div>
                <div class="action-hint">
                    <span class="hint-text">点击查看详情</span>
                    <i class="layui-icon layui-icon-right"></i>
                </div>
            </div>
            <div class="progress-bar">
                <div class="progress-fill"></div>
            </div>
        </div>

        <style>
        .vehicle-arrival-effect {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 99999;
            pointer-events: none;
            animation: slideInRight 0.6s cubic-bezier(0.175, 0.885, 0.32, 1.275);
        }

        .arrival-notification {
            border-radius: 16px;
            padding: 0;
            min-width: 320px;
            box-shadow:
                0 20px 40px rgba(102, 126, 234, 0.3),
                0 8px 16px rgba(0, 0, 0, 0.1);
            overflow: hidden;
            position: relative;
            pointer-events: auto;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .arrival-notification:hover {
            transform: translateY(-5px) scale(1.02);
            box-shadow:
                0 25px 50px rgba(102, 126, 234, 0.4),
                0 12px 24px rgba(0, 0, 0, 0.15);
        }

        .arrival-notification::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0.05) 100%);
            pointer-events: none;
        }

        .notification-header {
            display: flex;
            align-items: center;
            padding: 20px 20px 15px;
            position: relative;
        }

        .pulse-ring {
            position: absolute;
            left: 17px;
            top: 17px;
            width: 40px;
            height: 40px;
            border: 3px solid rgba(255, 255, 255, 0.3);
            border-radius: 50%;
            animation: pulse 2s infinite;
        }

        .notification-icon {
            width: 40px;
            height: 40px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 15px;
            backdrop-filter: blur(10px);
            position: relative;
            z-index: 2;
        }

        .notification-icon .layui-icon {
            font-size: 20px;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            width: 100%;
            height: 100%;
            line-height: 1;
        }

        .notification-title {
            color: white;
            font-size: 18px;
            font-weight: 700;
            text-shadow: 0 2px 4px rgba(0,0,0,0.2);
        }

        .notification-content {
            padding: 0 20px 20px;
            color: white;
        }

        .car-info {
            margin-bottom: 15px;
        }

        .car-plate {
            font-size: 24px;
            font-weight: 800;
            text-shadow: 0 2px 4px rgba(0,0,0,0.3);
            margin-bottom: 8px;
            letter-spacing: 2px;
        }

        .car-details {
            display: flex;
            justify-content: space-between;
            font-size: 13px;
            color: rgba(255, 255, 255, 0.9);
        }

        .passway-info {
            background: rgba(255, 255, 255, 0.2);
            padding: 4px 8px;
            border-radius: 12px;
            backdrop-filter: blur(5px);
        }

        .time-info {
            font-family: 'Courier New', monospace;
            font-weight: 600;
        }

        .action-hint {
            display: flex;
            align-items: center;
            justify-content: center;
            background: rgba(255, 255, 255, 0.1);
            padding: 10px;
            border-radius: 8px;
            backdrop-filter: blur(5px);
            transition: all 0.3s ease;
        }

        .action-hint:hover {
            background: rgba(255, 255, 255, 0.2);
            transform: scale(1.02);
        }

        .hint-text {
            font-size: 13px;
            margin-right: 8px;
            font-weight: 500;
        }

        .progress-bar {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: rgba(255, 255, 255, 0.2);
            overflow: hidden;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #FFD700, #FFA500);
            width: 0%;
            animation: progressFill 5s linear forwards;
        }

        @keyframes slideInRight {
            from {
                transform: translateX(100%);
                opacity: 0;
            }
            to {
                transform: translateX(0);
                opacity: 1;
            }
        }

        @keyframes pulse {
            0% {
                transform: scale(1);
                opacity: 1;
            }
            50% {
                transform: scale(1.2);
                opacity: 0.7;
            }
            100% {
                transform: scale(1);
                opacity: 1;
            }
        }

        @keyframes progressFill {
            from {
                width: 0%;
            }
            to {
                width: 100%;
            }
        }

        @keyframes slideOutRight {
            from {
                transform: translateX(0);
                opacity: 1;
            }
            to {
                transform: translateX(100%);
                opacity: 0;
            }
        }

        .vehicle-arrival-effect.fade-out {
            animation: slideOutRight 0.4s ease-in forwards;
        }

        /* 车道高亮闪烁效果 */
        .highlight-flash {
            animation: highlightFlash 2s ease-in-out;
            position: relative;
            z-index: 1000;
        }

        @keyframes highlightFlash {
            0% {
                background: linear-gradient(135deg, #667eea, #764ba2);
                color: white;
                transform: scale(1);
                box-shadow: 0 0 0 0 rgba(102, 126, 234, 0.7);
            }
            25% {
                background: linear-gradient(135deg, #667eea, #764ba2);
                color: white;
                transform: scale(1.05);
                box-shadow: 0 0 0 10px rgba(102, 126, 234, 0.3);
            }
            50% {
                background: linear-gradient(135deg, #28a745, #20c997);
                color: white;
                transform: scale(1.05);
                box-shadow: 0 0 0 15px rgba(40, 167, 69, 0.2);
            }
            75% {
                background: linear-gradient(135deg, #667eea, #764ba2);
                color: white;
                transform: scale(1.02);
                box-shadow: 0 0 0 5px rgba(102, 126, 234, 0.1);
            }
            100% {
                background: transparent;
                color: inherit;
                transform: scale(1);
                box-shadow: 0 0 0 0 rgba(102, 126, 234, 0);
            }
        }
        </style>
    `;

    effectContainer.html(effectHTML);
    $('body').append(effectContainer);

    // 自动消失的定时器
    var autoHideTimer = null;
    var isHovered = false;

    // 设置自动消失定时器
    function setAutoHideTimer() {
        clearTimeout(autoHideTimer);
        autoHideTimer = setTimeout(function () {
            if (!isHovered && effectContainer.length) {
                effectContainer.addClass('fade-out');
                setTimeout(function () {
                    effectContainer.remove();
                }, 400);
            }
        }, 5000);
    }

    // 鼠标悬停事件
    effectContainer.find('.arrival-notification').hover(
        function () {
            // 鼠标进入
            isHovered = true;
            clearTimeout(autoHideTimer);
        },
        function () {
            // 鼠标离开
            isHovered = false;
            // 鼠标离开后1秒自动消失
            autoHideTimer = setTimeout(function () {
                if (effectContainer.length) {
                    effectContainer.addClass('fade-out');
                    setTimeout(function () {
                        effectContainer.remove();
                    }, 400);
                }
            }, 1000);
        }
    );

    // 点击事件 - 恢复弹窗并定位到对应车道
    effectContainer.find('.arrival-notification').click(function () {
        // 清除定时器
        clearTimeout(autoHideTimer);

        // 恢复弹窗到原始位置
        if (isAtBottom) {
            $('.docfb-popup__btn').click();
        }

        // 定位到对应的车道
        setTimeout(function () {
            // 查找对应的车道li元素
            var targetLi = $(".box .way ul li[data-key='" + data.id + "']");
            if (targetLi.length > 0) {
                // 切换到对应车道
                opsObj.clickWayUlLi(targetLi[0]);

                // 添加高亮闪烁效果，让用户知道定位到了哪个车道
                targetLi.addClass('highlight-flash');
                setTimeout(function () {
                    targetLi.removeClass('highlight-flash');
                }, 2000);
            }
        }, 500); // 等待弹窗恢复动画完成

        // 关闭特效
        effectContainer.addClass('fade-out');
        setTimeout(function () {
            effectContainer.remove();
        }, 400);
    });

    // 启动自动消失定时器
    setAutoHideTimer();
}

// 开闸原因检查函数
function checkOpenGateReason(successCallback, cancelCallback) {
    // 获取系统配置
    $.get("/Monitoring/GetOpenGateReasons", function (config) {
        if (config.success && config.data.SysConfig_OpenGateReasonsEnable == 1) {
            // 需要选择开闸原因
            var reasons = [];
            if (config.data.SysConfig_OpenGateReasons) {
                reasons = config.data.SysConfig_OpenGateReasons.split(';').filter(function (r) {
                    return r.trim() !== '';
                });
            }

            showOpenGateReasonDialog(reasons, successCallback, cancelCallback);
        } else {
            // 不需要选择原因，直接执行
            successCallback('');
        }
    }).fail(function () {
        // 获取配置失败，直接执行
        successCallback('');
    });
}

// 显示开闸原因选择对话框
function showOpenGateReasonDialog(reasons, successCallback, cancelCallback) {
    var html = '<div class="premium-gate-dialog">';

    // 头部区域
    html += '<div class="dialog-header">';
    html += '<div class="header-icon"><i class="layui-icon layui-icon-release"></i></div>';
    html += '<div class="header-content">';
    html += '<h3 class="dialog-title">开闸确认</h3>';
    html += '<p class="dialog-subtitle">请选择或输入开闸原因</p>';
    html += '</div>';
    html += '</div>';

    // 快捷选择区域
    if (reasons && reasons.length > 0) {
        html += '<div class="quick-select-section">';
        html += '<div class="section-title">';
        html += '<i class="layui-icon layui-icon-lightning"></i>';
        html += '<span>快捷选择开闸原因</span>';
        html += '</div>';
        html += '<div class="reason-grid">';

        for (var i = 0; i < reasons.length; i++) {
            html += '<div class="reason-card" data-reason="' + reasons[i] + '">';
            html += '<div class="card-content">';
            html += '<i class="layui-icon layui-icon-ok-circle card-icon"></i>';
            html += '<span class="card-text">' + reasons[i] + '</span>';
            html += '</div>';
            html += '</div>';
        }

        html += '</div>';
        html += '</div>';
    }

    // 自定义输入区域
    html += '<div class="custom-input-section">';
    html += '<div class="section-title">';
    html += '<i class="layui-icon layui-icon-edit"></i>';
    html += '<span>自定义原因</span>';
    html += '</div>';
    html += '<div class="input-container">';
    html += '<input type="text" class="premium-input" id="customReasonInput" placeholder="请输入开闸原因..." maxlength="50">';
    html += '<div class="input-underline"></div>';
    html += '</div>';
    html += '<div class="input-hint">支持中文、英文、数字，最多50个字符</div>';
    html += '</div>';

    // 操作按钮区域
    html += '<div class="dialog-actions">';
    html += '<button type="button" class="premium-btn btn-cancel">取消</button>';
    html += '<button type="button" class="premium-btn btn-confirm" disabled>确认开闸</button>';
    html += '</div>';

    html += '</div>';

    // 样式
    var style = `
        <style>
        .premium-gate-dialog {
            padding: 0;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 20px 60px rgba(102, 126, 234, 0.3);
            position: relative;
        }

        .premium-gate-dialog::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0.05) 100%);
            pointer-events: none;
        }

        .dialog-header {
            display: flex;
            align-items: center;
            padding: 30px 30px 20px;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-bottom: 1px solid rgba(255, 255, 255, 0.2);
        }

        .header-icon {
            width: 60px;
            height: 60px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 20px;
            backdrop-filter: blur(5px);
        }

        .header-icon .layui-icon {
            font-size: 28px;
            color: white;
        }

        .header-content {
            flex: 1;
        }

        .dialog-title {
            margin: 0 0 5px 0;
            font-size: 24px;
            font-weight: 700;
            color: white;
            text-shadow: 0 2px 4px rgba(0,0,0,0.2);
        }

        .dialog-subtitle {
            margin: 0;
            font-size: 14px;
            color: rgba(255, 255, 255, 0.8);
            font-weight: 400;
        }

        .quick-select-section, .custom-input-section {
            padding: 25px 30px;
            background: white;
            position: relative;
        }

        .section-title {
            display: flex;
            align-items: center;
            margin-bottom: 20px;
            font-size: 16px;
            font-weight: 600;
            color: #333;
        }

        .section-title .layui-icon {
            margin-right: 10px;
            font-size: 18px;
            color: #667eea;
        }

        .reason-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(140px, 1fr));
            gap: 12px;
        }

        .reason-card {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border: 2px solid #e9ecef;
            border-radius: 12px;
            padding: 16px 12px;
            cursor: pointer;
            transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
            position: relative;
            overflow: hidden;
        }

        .reason-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(102, 126, 234, 0.1), transparent);
            transition: left 0.6s ease;
        }

        .reason-card:hover::before {
            left: 100%;
        }

        .reason-card:hover {
            transform: translateY(-3px) scale(1.02);
            border-color: #667eea;
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.2);
        }

        .reason-card.selected {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-color: #667eea;
            color: white;
            transform: translateY(-3px) scale(1.02);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
        }

        .card-content {
            display: flex;
            flex-direction: column;
            align-items: center;
            text-align: center;
            position: relative;
            z-index: 2;
        }

        .card-icon {
            font-size: 24px;
            margin-bottom: 8px;
            transition: all 0.3s ease;
        }

        .reason-card.selected .card-icon {
            color: white;
            transform: scale(1.2);
        }

        .card-text {
            font-size: 13px;
            font-weight: 500;
            line-height: 1.3;
        }

        .input-container {
            position: relative;
            margin-bottom: 10px;
        }

        .premium-input {
            width: 100%;
            padding: 15px 0;
            font-size: 16px;
            border: none;
            border-bottom: 2px solid #e9ecef;
            background: transparent;
            outline: none;
            transition: all 0.3s ease;
            color: #333;
        }

        .premium-input:focus {
            border-bottom-color: #667eea;
        }

        .premium-input:focus + .input-underline {
            transform: scaleX(1);
        }

        .input-underline {
            position: absolute;
            bottom: 0;
            left: 0;
            width: 100%;
            height: 2px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            transform: scaleX(0);
            transition: transform 0.3s ease;
            transform-origin: left;
        }

        .input-hint {
            font-size: 12px;
            color: #999;
            margin-top: 5px;
        }

        .dialog-actions {
            padding: 25px 30px;
            background: #f8f9fa;
            display: flex;
            justify-content: flex-end;
            gap: 15px;
        }

        .premium-btn {
            padding: 12px 30px;
            border: none;
            border-radius: 25px;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
            position: relative;
            overflow: hidden;
        }

        .premium-btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: left 0.6s ease;
        }

        .premium-btn:hover::before {
            left: 100%;
        }

        .btn-cancel {
            background: linear-gradient(135deg, #6c757d, #adb5bd);
            color: white;
            box-shadow: 0 4px 15px rgba(108, 117, 125, 0.3);
        }

        .btn-cancel:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(108, 117, 125, 0.4);
        }

        .btn-confirm {
            background: linear-gradient(135deg, #28a745, #20c997);
            color: white;
            box-shadow: 0 4px 15px rgba(40, 167, 69, 0.3);
        }

        .btn-confirm:hover:not(:disabled) {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(40, 167, 69, 0.4);
        }

        .btn-confirm:disabled {
            background: linear-gradient(135deg, #e9ecef, #dee2e6);
            color: #6c757d;
            cursor: not-allowed;
            box-shadow: none;
        }

        .custom-input-section {
            border-top: 1px solid #f0f0f0;
        }
        </style>
    `;

    var index = layer.open({
        type: 1,
        title: false,
        content: style + html,
        area: ['550px', 'auto'],
        closeBtn: 1,
        shadeClose: false,
        skin: 'premium-dialog',
        success: function (layero, index) {
            var selectedReason = '';
            var customInput = layero.find('#customReasonInput');
            var confirmBtn = layero.find('.btn-confirm');
            var reasonCards = layero.find('.reason-card');

            // 检查确认按钮状态
            function checkConfirmButton() {
                var hasSelection = selectedReason !== '';
                var hasCustomInput = customInput.val().trim() !== '';
                confirmBtn.prop('disabled', !hasSelection && !hasCustomInput);
            }

            // 默认选中第一个开闸原因
            if (reasonCards.length > 0) {
                var firstCard = reasonCards.first();
                firstCard.addClass('selected');
                selectedReason = firstCard.data('reason');
                customInput.val(selectedReason);
                checkConfirmButton();
            }

            // 绑定快捷选择事件
            layero.find('.reason-card').click(function () {
                layero.find('.reason-card').removeClass('selected');
                $(this).addClass('selected');
                selectedReason = $(this).data('reason');

                // 将选中的原因填入输入框
                customInput.val(selectedReason);
                checkConfirmButton();
            });

            // 绑定自定义输入事件
            customInput.on('input', function () {
                var inputValue = $(this).val().trim();

                // 如果输入框有内容，清除快捷选择
                if (inputValue !== '') {
                    layero.find('.reason-card').removeClass('selected');
                    selectedReason = '';
                }

                checkConfirmButton();
            });

            // 绑定回车键
            customInput.on('keypress', function (e) {
                if (e.which === 13 && !confirmBtn.prop('disabled')) {
                    confirmBtn.click();
                }
            });

            // 绑定确认按钮事件
            confirmBtn.click(function () {
                if (!$(this).prop('disabled')) {
                    var finalReason = customInput.val().trim() || selectedReason;
                    if (finalReason) {
                        layer.close(index);
                        successCallback(finalReason);
                    }
                }
            });

            // 绑定取消按钮事件
            layero.find('.btn-cancel').click(function () {
                layer.close(index);
                if (cancelCallback) cancelCallback();
            });

            // 自动聚焦到输入框
            setTimeout(function () {
                customInput.focus();
            }, 300);
        },
        cancel: function () {
            if (cancelCallback) cancelCallback();
        }
    });
}

