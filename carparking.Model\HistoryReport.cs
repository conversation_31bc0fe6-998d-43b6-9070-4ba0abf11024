﻿//------------------------------------------------------------------------------
// <autogenerated>
//     Implementation of the Class Admin
//     Creater: runrui.wang
//     Date:    2016-08-05 19:11
//     Version: 2.0.0.0
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </autogenerated>
//------------------------------------------------------------------------------
using System;
using Newtonsoft.Json;

namespace carparking.Model
{
    /// <summary>
    /// 报表历史记录
    /// </summary>
    [Serializable]
    public class HistoryReport
    {
        public int? HistoryReport_ID { get; set; }
        public string HistoryReport_No { get; set; }
        /// <summary>
        /// 添加时间
        /// </summary>
        [JsonConverter(typeof(JsonConvertDateTimeOverride_DateTime))]
        public DateTime? Syswarn_AddTime { get; set; }
        /// <summary>
        /// 文件类型:1-日报表,2-月报表,3-车流量统计报表,4-临时车收费统计报表,5-月租车充值统计,6-储值车充值统计,7-车位续费统计
        /// </summary>
        public int? HistoryReport_Type { get; set; }
        /// <summary>
        /// 本地文件路径地址
        /// </summary>
        public string HistoryReport_FilePath { get; set; }
        /// <summary>
        /// 开始时间
        /// </summary>
        [JsonConverter(typeof(JsonConvertDateTimeOverride_DateTime))]
        public DateTime? HistoryReport_BeginTime { get; set; }
        /// <summary>
        /// 结束时间
        /// </summary>
        [JsonConverter(typeof(JsonConvertDateTimeOverride_DateTime))]
        public DateTime? HistoryReport_EndTime { get; set; }
        /// <summary>
        /// 操作账号
        /// </summary>
        public string HistoryReport_Account { get; set;}
        /// <summary>
        /// 操作时间
        /// </summary>
        [JsonConverter(typeof(JsonConvertDateTimeOverride_DateTime))]
        public DateTime? HistoryReport_AddTime { get; set; }
        /// <summary>
        /// 统计方式:0-操作员,1-区域,2-操作员+区域
        /// </summary>
        public int? HistoryReport_GroupType { get; set; }

    }

}
