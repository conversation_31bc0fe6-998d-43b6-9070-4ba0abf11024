using System;
using System.Collections.Generic;
using System.Linq;
using Microsoft.AspNetCore.Mvc;
using carparking.Common;
using Newtonsoft.Json.Linq;
using System.Data;
using carparking.BLL.Cache;

namespace carparking.Web.Controllers
{
    public class RptDayController : BaseController
    {
        public IActionResult Index()
        {
            if (!Powermanage.PowerCheck("RptDay", PowerEnum.View.ToString(), false, true, lgAdmins))
                return new EmptyResult();
            return View();
        }

        public IActionResult RptView()
        {
            if (!Powermanage.PowerCheck("RptDay", PowerEnum.Print.ToString(), false, true, lgAdmins))
                return new EmptyResult();

            ViewBag.Start = Request.Query["sdt"];
            ViewBag.End = Request.Query["edt"];
            ViewBag.ParkName = parking.Parking_Name;

            ViewBag.RptPrintList = new List<Model.RptPrintItem>();
            ViewBag.RptPrintListTotal = new Model.RptPrintItem();
            ViewBag.onlineData = new Model.RptPrintOnline();
            ViewBag.IsWindows = AppBasicCache.IsWindows ? 1 : 0;
            return View();
        }

        public IActionResult RptData(string sdt, string edt, bool checkhis, int dataType = 0, int groupType = 0)
        {
            try
            {
                if (!Powermanage.PowerCheck("RptDay", PowerEnum.Search.ToString(), false, true, lgAdmins))
                    return ResOk(false, "无权限");

                if (!DateTime.TryParse(sdt, out var start)) return ResOk(false, "请填写正确有效的开始时间");
                if (!DateTime.TryParse(edt, out var end)) return ResOk(false, "请填写正确有效的截止时间");

                if (checkhis)
                {
                    var isHaveHis = BLL.RptAnalysis.CheckHistroyData(start, end, 1, out var path, out DateTime? histime1, dataType, true, groupType);
                    if (!isHaveHis)
                    {
                        return ResOk(false, "ok", "0");
                    }
                }

                var data = BLL.RptAnalysis.GetRptData<Model.RptPrintData>(start, end, 1, out var old, out DateTime? histime, dataType, groupType);

                ViewBag.RptPrintList = data?.RptPrintList ?? new List<Model.RptPrintItem>();
                ViewBag.RptPrintListTotal = data?.RptPrintListTotal ?? new Model.RptPrintItem();
                ViewBag.onlineData = data?.onlineData ?? new Model.RptPrintOnline();

                var res = new
                {
                    RptPrintList = data?.RptPrintList ?? new List<Model.RptPrintItem>(),
                    RptPrintListTotal = data?.RptPrintListTotal ?? new Model.RptPrintItem(),
                    onlineData = data?.onlineData ?? new Model.RptPrintOnline(),
                    payHtml = BLL.RptAnalysis.CreateOnlinePayTdHtml(data?.onlineData, 16)
                };

                return ResOk(true, $"车场日报表：{sdt} ~ {edt}  {(old ? $"来源于{(histime == null ? "" : histime.Value.ToString("yyyy-MM-dd HH:mm:ss"))}历史统计数据" : "")}", res);
            }
            catch (Exception ex)
            {
                BLL.UserLogs.AddLog(lgAdmins, LogEnum.Backend, SecondOption.Search, $"异常:{ex}", SecondIndex.RptDay);
                return ResOk(false, ex.Message);
            }
        }

        #region 打印
        public ActionResult print()
        {
            if (!Powermanage.PowerCheck("RptDay", PowerEnum.Print.ToString(), false, true, lgAdmins))
                return new EmptyResult();
            return View();
        }
        #endregion

        #region 导出
        public FileContentResult Export(string conditionParam)
        {
            try
            {
                if (!Powermanage.PowerCheck("RptDay", PowerEnum.Export.ToString(), false, true, lgAdmins))
                    return null;

                JObject obj = Utils.ClearModelRiskSQL<JObject>(conditionParam);

                DateTime? date = null;
                if (!BLL.RptAnalysis.ChkDate(obj["start"], ref date))
                {
                    return null;
                }

                var data = BLL.RptAnalysis.GetDayOrMonthOrYearAnalysisAoto(date, null, "day", Utils.ObjectToInt(obj["dataType"], 0));

                byte[] bt = null;
                if (data != null && data.Count > 0)
                {
                    #region 创建DataTable
                    DataTable dataTable = new DataTable();
                    dataTable.Columns.Add("编号");
                    dataTable.Columns.Add("时间");
                    //表头
                    for (int i = 0; i < data.Count; i++)
                    {
                        dataTable.Columns.Add(data[i].typeName);
                    }
                    //数据
                    for (int n = 0; n < data[0].rpts.Count; n++)
                    {
                        DataRow row = dataTable.NewRow();
                        row[0] = n + 1;
                        row[1] = data[0].rpts[n].date;
                        for (int j = 0; j < data.Count; j++)
                        {
                            int index = j + 2;
                            row[index] = data[j].rpts[n].num;
                        }
                        dataTable.Rows.Add(row);
                    }
                    //添加合计行
                    DataRow totalrow = dataTable.NewRow();
                    totalrow[0] = "合计";
                    totalrow[1] = "";
                    for (int i = 0; i < data.Count; i++)
                    {
                        int index = i + 2;
                        totalrow[index] = data[i].total;
                    }
                    dataTable.Rows.Add(totalrow);
                    #endregion
                    #region 填充EXCEL数据

                    if (dataTable.Rows.Count > 5000)
                        bt = NPOIExcelHelper.ExportToExcel2(dataTable);
                    else
                        bt = NPOIExcelHelper.ToExport(dataTable, (index, sheet) => { sheet.SetColumnWidth(index, 20 * 256); });

                    BLL.UserLogs.AddLog(lgAdmins, LogEnum.Backend, SecondOption.Export, $"{dataTable.Rows.Count} 条数据", SecondIndex.RptDay);
                    HttpHelper.HttpContext.Response.Cookies.Append("fileDownload", "true",
                       new Microsoft.AspNetCore.Http.CookieOptions
                       {
                           Expires = DateTimeOffset.Now.AddSeconds(10),
                           IsEssential = true
                       });

                    #endregion
                }

                return File(bt, "application/vnd.ms-excel", $"车场日报表_{DateTimeHelper.GetNowTime().ToString("yyyy-MM-dd")}.xlsx");
            }
            catch (Exception ex)
            {
                BLL.UserLogs.AddLog(lgAdmins, LogEnum.Backend, SecondOption.Export, $"异常:{ex}", SecondIndex.RptDay);
                return null;
            }
        }
        public string GetDateStr(string hour, DateTime? date)
        {
            if (hour.Length == 1) { hour = "0" + hour; }
            return Convert.ToDateTime(date).ToString("yyyy-MM-dd") + " " + hour + ":00";
        }
        #endregion

        public IActionResult DeleteCache()
        {
            try
            {
                if (!Powermanage.PowerCheck("RptDay", PowerEnum.Delete.ToString(), false, true, lgAdmins))
                    return ResOk(false, "无权限");

                var hisData = BLL.BaseBLL._GetAllEntity(new Model.HistoryReport(), "HistoryReport_ID,HistoryReport_FilePath", $"HistoryReport_Type in (1,1001)");

                if (hisData.Count > 0)
                {
                    foreach (var item in hisData)
                    {
                        FileHelper.DeleteFile(item.HistoryReport_FilePath);
                    }

                    var ret = BLL.BaseBLL._Execute($"DELETE FROM historyreport WHERE HistoryReport_ID in ({string.Join(",", hisData.Select(x => x.HistoryReport_ID))})");
                    if (ret < 0)
                    {
                        BLL.UserLogs.AddLog(lgAdmins, LogEnum.Backend, SecondOption.Delete, "清除历史统计失败", SecondIndex.RptDay);
                    }
                }

                BLL.UserLogs.AddLog(lgAdmins, LogEnum.Backend, SecondOption.Delete, "清除历史统计成功", SecondIndex.RptDay);

                return ResOk(true, $"清除成功");
            }
            catch (Exception ex)
            {
                BLL.UserLogs.AddLog(lgAdmins, LogEnum.Backend, SecondOption.Delete, $"异常:{ex}", SecondIndex.RptDay);
                return ResOk(false, $"清除失败，{ex.Message}");
            }
        }

    }
}