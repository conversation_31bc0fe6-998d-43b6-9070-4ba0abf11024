﻿using carparking.BLL.Cache;
using carparking.Common;
using carparking.Config;
using carparking.Model;
using Dapper;
using NPOI.Util;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using static Aliyun.OSS.Model.CreateSelectObjectMetaInputFormatModel;

namespace carparking.BLL
{
    public class ParkOrder : BaseBLL
    {
        static DAL.ParkOrder dal = new DAL.ParkOrder();

        public static string CreateOrderNo(string carNo)
        {
            return $"{Utils.CreateNumberWith()}-{(carNo.Length > 6 ? carNo.Substring(carNo.Length - 6, 6) : "")}";
        }

        /// <summary>
        /// 新增停车订单并且更新冗余表（不需要更新冗余表，不要调用，以免造成数据异常）
        /// </summary>
        /// <param name="po"></param>
        /// <returns></returns>
        public static int AddOrderCar(Model.ParkOrder po)
        {
            Model.InCar inCar = null;
            if (po != null && !string.IsNullOrWhiteSpace(po.ParkOrder_ParkNo) && !string.IsNullOrWhiteSpace(po.ParkOrder_CarNo))
            {
                inCar = new Model.InCar();
                inCar.InCar_ParkOrderNo = po.ParkOrder_No;
                inCar.InCar_CarNo = po.ParkOrder_CarNo;
                inCar.InCar_Status = po.ParkOrder_StatusNo;
                inCar.InCar_CarCardTypeNo = po.ParkOrder_CarCardType;
                inCar.InCar_ParkAreaNo = po.ParkOrder_ParkAreaNo;
                inCar.InCar_EnterTime = po.ParkOrder_EnterTime;
            }
            return dal.AddOrder(po, inCar);
        }

        public static Model.ParkOrder GetEntity(string ParkOrder_No, int dataType = 0, DateTime? time = default)
        {
            if (string.IsNullOrEmpty(ParkOrder_No)) return null;

            if (dataType != 1)
            {
                if (AppBasicCache.ReadWriteCache)
                {
                    var model = DataCache.ParkOrder.Get(ParkOrder_No);
                    if (model == null)
                    {
                        model = _GetEntityByNo(new Model.ParkOrder(), ParkOrder_No);
                    }
                    return model;
                }
                else
                {
                    return _GetEntityByNo(new Model.ParkOrder(), ParkOrder_No);
                }
            }
            else
            {
                if (time == null || time == default) time = DateTime.Now;
                var tablename = $"ParkOrder_{time.Value.ToString("yyyy")}";
                var exist = CommonBLL.ExistDBTable(tablename);
                if (!exist) { return null; }
                var d = dal.GetExtEntity("*", $"ParkOrder_No=@ParkOrder_No", tablename, parameters: new { ParkOrder_No = ParkOrder_No });
                return d;
            }
        }

        public static Model.ParkOrder GetEntity(string showFields, string selectWhere)
        {
            var d = _GetEntityByWhere(new Model.ParkOrder(), showFields, selectWhere);
            //if (d != null)
            //{
            //    d.ParkOrder_EnterImgPath = Utils.RegReplaceIP(d.ParkOrder_EnterImgPath, Config.AppSettingConfig.SiteDomain_IPPort);
            //    d.ParkOrder_OutImgPath = Utils.RegReplaceIP(d.ParkOrder_OutImgPath, Config.AppSettingConfig.SiteDomain_IPPort);
            //}
            return d;
        }

        /// <summary>
        /// 自定义条件分页,获得实体列表
        /// </summary>
        public static Model.ParkOrder GetExtEntity(string showFields, string selectWhere)
        {
            var d = dal.GetExtEntity(showFields, selectWhere);
            //if (d != null)
            //{
            //    d.ParkOrder_EnterImgPath = Utils.RegReplaceIP(d.ParkOrder_EnterImgPath, Config.AppSettingConfig.SiteDomain_IPPort);
            //    d.ParkOrder_OutImgPath = Utils.RegReplaceIP(d.ParkOrder_OutImgPath, Config.AppSettingConfig.SiteDomain_IPPort);
            //}
            return d;
        }

        /// <summary>
        /// 自定义条件分页,获得实体列表
        /// </summary>
        public static List<Model.ParkOrder> GetAllEntity(string showFields, string selectWhere, int? dataType = 0, DateTime? time = default, int commtimeout = 0, object parameters = null)
        {
            var tablename = "ParkOrder";
            if (dataType == 1)
            {
                if (time == default || time == null) time = DateTime.Now;
                tablename = $"ParkOrder_{time.Value.ToString("yyyy")}";
                var exist = CommonBLL.ExistDBTable(tablename);
                if (!exist) { return new List<Model.ParkOrder>(); }
            }

            var d = dal.GetAllEntity(showFields, selectWhere, tableName: tablename, commtimeout: commtimeout, parameters: parameters);
            //d.ForEach(x =>
            //{
            //    x.ParkOrder_EnterImgPath = Utils.RegReplaceIP(x.ParkOrder_EnterImgPath, Config.AppSettingConfig.SiteDomain_IPPort);
            //    x.ParkOrder_OutImgPath = Utils.RegReplaceIP(x.ParkOrder_OutImgPath, Config.AppSettingConfig.SiteDomain_IPPort);
            //});
            return d;

        }

        /// <summary>
        /// 自定义条件分页,获得实体列表
        /// </summary>
        public static List<Model.ParkOrder> GetAllEntityExt(string showFields, string selectWhere)
        {
            var d = dal.GetAllExtEntity(showFields, selectWhere);
            //d.ForEach(x =>
            //{
            //    x.ParkOrder_EnterImgPath = Utils.RegReplaceIP(x.ParkOrder_EnterImgPath, Config.AppSettingConfig.SiteDomain_IPPort);
            //    x.ParkOrder_OutImgPath = Utils.RegReplaceIP(x.ParkOrder_OutImgPath, Config.AppSettingConfig.SiteDomain_IPPort);
            //});
            return d;
        }

        /// <summary>
        /// 自定义条件分页,获得实体列表
        /// </summary>
        public static List<Model.ParkOrder> GetList(int InOutType, string showFields, string selectWhere, int pageIndex, int pageSize, out int pageCount, out int totalRecord, object parameters = null)
        {
            var d = dal.GetList(InOutType, showFields, selectWhere, pageIndex, pageSize, out pageCount, out totalRecord, parameters);
            //d.ForEach(x =>
            //{
            //    x.ParkOrder_EnterImgPath = Utils.RegReplaceIP(x.ParkOrder_EnterImgPath, Config.AppSettingConfig.SiteDomain_IPPort);
            //    x.ParkOrder_OutImgPath = Utils.RegReplaceIP(x.ParkOrder_OutImgPath, Config.AppSettingConfig.SiteDomain_IPPort);
            //});
            return d;
        }

        /// <summary>
        /// 自定义条件分页,获得实体列表
        /// </summary>
        public static List<Model.ParkOrder> GetList(int InOutType, string showFields, string selectWhere, int pageIndex, int pageSize, string sortField, int sortType, out int pageCount, out int totalRecord, int? dataType = 0, DateTime? time = default, int commtimeout = 0, object parameters = null)
        {
            var tablename = "parkorder";
            if (dataType == 1)
            {
                if (time == default || time == null) time = DateTime.Now;
                tablename = $"parkorder_{time.Value.ToString("yyyy")}";
                var exist = CommonBLL.ExistDBTable(tablename);
                if (!exist) { pageCount = 0; totalRecord = 0; return new List<Model.ParkOrder>(); }
            }

            var d = dal.GetList(tablename, InOutType, showFields, selectWhere, pageIndex, pageSize, sortField, sortType, out pageCount, out totalRecord, commtimeout, parameters);
            //d.ForEach(x =>
            //{
            //    x.ParkOrder_EnterImgPath = Utils.RegReplaceIP(x.ParkOrder_EnterImgPath, Config.AppSettingConfig.SiteDomain_IPPort);
            //    x.ParkOrder_OutImgPath = Utils.RegReplaceIP(x.ParkOrder_OutImgPath, Config.AppSettingConfig.SiteDomain_IPPort);
            //});
            return d;
        }

        /// <summary>
        /// 自定义条件分页,获得实体列表
        /// </summary>
        public static List<Model.ParkOrder> GetExtList(string showFields, string selectWhere, int pageIndex, int pageSize, out int pageCount, out int totalRecord)
        {
            var d = dal.GetExtList(showFields, selectWhere, pageIndex, pageSize, out pageCount, out totalRecord);
            //d.ForEach(x =>
            //{
            //    x.ParkOrder_EnterImgPath = Utils.RegReplaceIP(x.ParkOrder_EnterImgPath, Config.AppSettingConfig.SiteDomain_IPPort);
            //    x.ParkOrder_OutImgPath = Utils.RegReplaceIP(x.ParkOrder_OutImgPath, Config.AppSettingConfig.SiteDomain_IPPort);
            //});
            return d;
        }

        /// <summary>
        /// 自定义条件分页,获得实体列表
        /// </summary>
        public static List<Model.ParkOrder> GetExtList(string showFields, string selectWhere, int pageIndex, int pageSize, string sortField, int sortType, out int pageCount, out int totalRecord)
        {
            var d = dal.GetExtList(showFields, selectWhere, pageIndex, pageSize, sortField, sortType, out pageCount, out totalRecord);
            //d.ForEach(x =>
            //{
            //    x.ParkOrder_EnterImgPath = Utils.RegReplaceIP(x.ParkOrder_EnterImgPath, Config.AppSettingConfig.SiteDomain_IPPort);
            //    x.ParkOrder_OutImgPath = Utils.RegReplaceIP(x.ParkOrder_OutImgPath, Config.AppSettingConfig.SiteDomain_IPPort);
            //});
            return d;
        }

        /// <summary>
        /// 自定义条件分页,获得实体列表
        /// </summary>
        public static List<Model.ParkOrder> GetListAndDetail(string showFields, string selectWhere, int pageIndex, int pageSize, out int pageCount, out int totalRecord)
        {
            var d = dal.GetListAndDetail(showFields, selectWhere, pageIndex, pageSize, out pageCount, out totalRecord);
            //d.ForEach(x =>
            //{
            //    x.ParkOrder_EnterImgPath = Utils.RegReplaceIP(x.ParkOrder_EnterImgPath, Config.AppSettingConfig.SiteDomain_IPPort);
            //    x.ParkOrder_OutImgPath = Utils.RegReplaceIP(x.ParkOrder_OutImgPath, Config.AppSettingConfig.SiteDomain_IPPort);
            //});
            return d;
        }
        #region 扩展方法

        /// <summary>
        /// 批量关闭场内记录
        /// </summary>
        public static int CloseList(List<Model.ParkOrder> parkOrders, object parameters = null)
        {
            return dal.CloseList(parkOrders, parameters);
        }

        /// <summary>
        /// 批量关闭场内记录
        /// </summary>
        public static int CloseList2(List<Model.ParkOrder> parkOrders, object parameters = null)
        {
            return dal.CloseList2(parkOrders, parameters);
        }

        /// <summary>
        /// 批量关闭场内记录
        /// </summary>
        public static int CloseList(DateTime? startTime, DateTime? endTime)
        {
            return dal.CloseList(startTime, endTime);
        }

        public static int Delete(string ParkOrder_No)
        {
            return dal.Delete(ParkOrder_No);
        }

        /// <summary>
        /// 获取停车订单
        /// </summary>
        /// <param name="ParkOrder_ParkNo"></param>
        /// <param name="ParkOrder_CarNo"></param>
        public static List<Model.ParkOrder> GetParkOrderList(string showFields, string ParkOrder_ParkNo, string ParkOrder_CarNo, int? ParkOrder_IsSettle)
        {
            List<Model.ParkOrder> d = BLL.ParkOrder._GetAllEntity(new Model.ParkOrder(), showFields, $"ParkOrder_ParkNo='{ParkOrder_ParkNo}' and ParkOrder_CarNo='{ParkOrder_CarNo}' and ParkOrder_IsSettle={ParkOrder_IsSettle}");
            //d.ForEach(x =>
            //{
            //    x.ParkOrder_EnterImgPath = Utils.RegReplaceIP(x.ParkOrder_EnterImgPath, Config.AppSettingConfig.SiteDomain_IPPort);
            //    x.ParkOrder_OutImgPath = Utils.RegReplaceIP(x.ParkOrder_OutImgPath, Config.AppSettingConfig.SiteDomain_IPPort);
            //});
            return d;
        }
        /// <summary>
        /// 获取停车订单
        /// </summary>
        /// <param name="ParkOrder_ParkNo"></param>
        /// <param name="ParkOrder_CarNo"></param>
        public static Model.ParkOrder GetParkOrder(string showFields, string ParkOrder_ParkNo, string ParkOrder_CarNo, int? ParkOrder_IsSettle = 0, int? ParkOrder_StatusNo = 200)
        {
            StringBuilder selectWhere = new StringBuilder();
            selectWhere.Append($" ParkOrder_ParkNo='{ParkOrder_ParkNo}' ");
            selectWhere.Append($" and ParkOrder_CarNo='{ParkOrder_CarNo}' ");
            if (ParkOrder_IsSettle != null)
                selectWhere.Append($" and ParkOrder_IsSettle={ParkOrder_IsSettle} ");
            if (ParkOrder_StatusNo != null)
                selectWhere.Append($" and ParkOrder_StatusNo={ParkOrder_StatusNo} ");

            Model.ParkOrder d = BLL.ParkOrder._GetEntityByWhere(new Model.ParkOrder(), showFields, selectWhere.ToString());
            //if (d != null)
            //{
            //    d.ParkOrder_EnterImgPath = Utils.RegReplaceIP(d.ParkOrder_EnterImgPath, Config.AppSettingConfig.SiteDomain_IPPort);
            //    d.ParkOrder_OutImgPath = Utils.RegReplaceIP(d.ParkOrder_OutImgPath, Config.AppSettingConfig.SiteDomain_IPPort);
            //}
            return d;
        }

        /// <summary>
        /// 获取停车订单
        /// </summary>
        /// <param name="ParkOrder_ParkNo"></param>
        /// <param name="ParkOrder_CarNo"></param>
        public static List<Model.ParkOrder> GetParkOrderByOwner(string showFields, string ParkOrder_ParkNo, string ParkOrder_OwnerNo, int? ParkOrder_IsSettle = 0, int? ParkOrder_StatusNo = 200, string ParkOrder_CarNo = "")
        {
            StringBuilder selectWhere = new StringBuilder();
            if (!string.IsNullOrWhiteSpace(ParkOrder_ParkNo))
                selectWhere.Append($" ParkOrder_ParkNo='{ParkOrder_ParkNo}' ");
            if (!string.IsNullOrWhiteSpace(ParkOrder_OwnerNo))
                selectWhere.Append($" and ParkOrder_OwnerNo='{ParkOrder_OwnerNo}' ");
            if (!string.IsNullOrWhiteSpace(ParkOrder_CarNo))
                selectWhere.Append($" and ParkOrder_CarNo='{ParkOrder_CarNo}' ");
            if (ParkOrder_IsSettle != null)
                selectWhere.Append($" and ParkOrder_IsSettle={ParkOrder_IsSettle} ");
            if (ParkOrder_StatusNo != null)
                selectWhere.Append($" and ParkOrder_StatusNo={ParkOrder_StatusNo} ");

            List<Model.ParkOrder> d = BLL.ParkOrder.GetAllEntity(showFields, selectWhere.ToString());
            //d.ForEach(x =>
            //{
            //    x.ParkOrder_EnterImgPath = Utils.RegReplaceIP(x.ParkOrder_EnterImgPath, Config.AppSettingConfig.SiteDomain_IPPort);
            //    x.ParkOrder_OutImgPath = Utils.RegReplaceIP(x.ParkOrder_OutImgPath, Config.AppSettingConfig.SiteDomain_IPPort);
            //});
            return d;
        }

        /// <summary>
        /// 获取停车订单
        /// </summary>
        /// <param name="ParkOrder_ParkNo"></param>
        /// <param name="ParkOrder_CarNo"></param>
        public static List<Model.ParkOrder> GetParkOrderByOwnerCarNo(string showFields, string ParkOrder_ParkNo, string ParkOrder_OwnerNo, List<string> CarNoList, int? ParkOrder_IsSettle = 0, int? ParkOrder_StatusNo = 200)
        {
            StringBuilder selectWhere = new StringBuilder();
            //if (!string.IsNullOrWhiteSpace(ParkOrder_ParkNo))
            //    selectWhere.Append($" ParkOrder_ParkNo='{ParkOrder_ParkNo}' ");

            selectWhere.Append($" 1=1 ");
            if (!string.IsNullOrWhiteSpace(ParkOrder_OwnerNo))
                selectWhere.Append($" and ParkOrder_OwnerNo='{ParkOrder_OwnerNo}' ");
            if (CarNoList != null && CarNoList.Count > 0)
                selectWhere.Append($" and ParkOrder_CarNo in ('{string.Join("','", CarNoList)}') ");
            if (ParkOrder_IsSettle != null)
                selectWhere.Append($" and ParkOrder_IsSettle={ParkOrder_IsSettle} ");
            if (ParkOrder_StatusNo != null)
                selectWhere.Append($" and ParkOrder_StatusNo={ParkOrder_StatusNo} ");

            List<Model.ParkOrder> d = BLL.ParkOrder.GetAllEntity(showFields, selectWhere.ToString());
            //d.ForEach(x =>
            //{
            //    x.ParkOrder_EnterImgPath = Utils.RegReplaceIP(x.ParkOrder_EnterImgPath, Config.AppSettingConfig.SiteDomain_IPPort);
            //    x.ParkOrder_OutImgPath = Utils.RegReplaceIP(x.ParkOrder_OutImgPath, Config.AppSettingConfig.SiteDomain_IPPort);
            //});
            return d;
        }

        /// <summary>
        /// 获取停车订单
        /// </summary>
        /// <param name="ParkOrder_ParkNo"></param>
        /// <param name="ParkOrder_CarNo"></param>
        public static List<Model.ParkOrder> GetSameParkOrder(string showFields, string ParkOrder_ParkNo, string ParkOrder_CarNo, int? ParkOrder_IsSettle = 0, int? ParkOrder_StatusNo = 200, int limit = 5)
        {
            StringBuilder selectWhere = new StringBuilder();
            selectWhere.Append($" ParkOrder_ParkNo='{ParkOrder_ParkNo}' ");
            selectWhere.Append($" and ParkOrder_CarNo like '%{ParkOrder_CarNo}%' ");
            if (ParkOrder_IsSettle != null)
                selectWhere.Append($" and ParkOrder_IsSettle={ParkOrder_IsSettle} ");
            if (ParkOrder_StatusNo != null)
                selectWhere.Append($" and ParkOrder_StatusNo={ParkOrder_StatusNo} ");

            selectWhere.Append($" LIMIT {limit} ");

            List<Model.ParkOrder> d = BLL.ParkOrder.GetAllEntity(showFields, selectWhere.ToString());
            //d.ForEach(x =>
            //{
            //    x.ParkOrder_EnterImgPath = Utils.RegReplaceIP(x.ParkOrder_EnterImgPath, Config.AppSettingConfig.SiteDomain_IPPort);
            //    x.ParkOrder_OutImgPath = Utils.RegReplaceIP(x.ParkOrder_OutImgPath, Config.AppSettingConfig.SiteDomain_IPPort);
            //});
            return d;
        }

        public static List<Model.InCar> GetSameParkOrder(string showFields, string ParkOrder_CarNo, int limit = 5)
        {
            StringBuilder selectWhere = new StringBuilder();
            selectWhere.Append($" InCar_CarNo like @ParkOrder_CarNo and InCar_Status=200");
            selectWhere.Append($" LIMIT {limit} ");

            return BLL.BaseBLL._GetAllEntity(new Model.InCar(), showFields, selectWhere.ToString(), parameters: new { ParkOrder_CarNo = "%" + ParkOrder_CarNo + "%" });
        }

        /// <summary>
        /// 获取停车订单
        /// </summary>
        /// <param name="ParkOrder_ParkNo"></param>
        /// <param name="ParkOrder_CarNo"></param>
        public static List<Model.ParkOrder> GetParkOrderList(string showFields, string ParkOrder_ParkNo, string Park_AreaNo, string ParkOrder_CarNo, int? ParkOrder_IsSettle)
        {
            List<Model.ParkOrder> d = BLL.ParkOrder._GetAllEntity(new Model.ParkOrder(), showFields, $"ParkOrder_ParkNo=@ParkOrder_ParkNo and ParkOrder_ParkAreaNo=@Park_AreaNo and ParkOrder_CarNo=@ParkOrder_CarNo and ParkOrder_IsSettle=@ParkOrder_IsSettle", parameters: new { ParkOrder_ParkNo = ParkOrder_ParkNo, Park_AreaNo = Park_AreaNo, ParkOrder_CarNo = ParkOrder_CarNo, ParkOrder_IsSettle = ParkOrder_IsSettle });
            //d.ForEach(x =>
            //{
            //    x.ParkOrder_EnterImgPath = Utils.RegReplaceIP(x.ParkOrder_EnterImgPath, Config.AppSettingConfig.SiteDomain_IPPort);
            //    x.ParkOrder_OutImgPath = Utils.RegReplaceIP(x.ParkOrder_OutImgPath, Config.AppSettingConfig.SiteDomain_IPPort);
            //});
            return d;
        }

        /// <summary>
        /// 创建停车订单
        /// </summary>
        /// <param name="po"></param>
        /// <returns></returns>
        public static int AddParkOrder(Model.ParkOrder po)
        {
            return dal.AddOrder(po, null);
        }

        /// <summary>
        /// 创建入场订单
        /// </summary>
        public static Model.ParkOrder CreateParkOrder(string Parking_No, string ParkArea_No, string ParkArea_Name, string Car_No, string CarCardType_No, string CarCardType_Name, string CarType_No, string CarType_Name, DateTime EnterTime, string Passway_No, string Passway_Name, int ParkOrder_IsFree = 0, int ParkOrder_IsNoInRecord = 0, string ParkOrder_OwnerNo = "", string ParkOrder_OwnerName = "", string parkOrderNo = null, bool isSupplement = true, bool isNoEnter = false)
        {
            string ParkOrder_No = string.IsNullOrWhiteSpace(parkOrderNo) ? $"{Utils.CreateNumberWith()}-{(Car_No.Length > 6 ? Car_No.Substring(Car_No.Length - 6, 6) : "")}" : parkOrderNo;
            Model.ParkOrder parkOrder = new Model.ParkOrder()
            {
                ParkOrder_No = ParkOrder_No,
                ParkOrder_ParkNo = Parking_No,
                ParkOrder_ParkAreaNo = ParkArea_No,
                ParkOrder_ParkAreaName = ParkArea_Name,
                ParkOrder_CarNo = Car_No,
                ParkOrder_CarCardType = CarCardType_No,
                ParkOrder_CarCardTypeName = CarCardType_Name,
                ParkOrder_CarType = CarType_No,
                ParkOrder_CarTypeName = CarType_Name,
                ParkOrder_StatusNo = 199,
                ParkOrder_EnterTime = EnterTime,
                ParkOrder_EnterPasswayNo = Passway_No,
                ParkOrder_EnterPasswayName = Passway_Name,
                ParkOrder_EnterAdminAccount = "",
                ParkOrder_EnterAdminName = "",
                ParkOrder_EnterImgPath = "",
                ParkOrder_OutType = 0,
                ParkOrder_TotalAmount = 0,
                ParkOrder_Lock = 0,
                ParkOrder_UserNo = "",
                ParkOrder_QrCodeType = 1,
                ParkOrder_Remark = "",
                ParkOrder_IsSettle = 0,
                ParkOrder_IsLift = 0,
                ParkOrder_IsFree = ParkOrder_IsFree,
                ParkOrder_IsNoInRecord = ParkOrder_IsNoInRecord,
                ParkOrder_OwnerNo = ParkOrder_OwnerNo ?? "",
                ParkOrder_OwnerName = ParkOrder_OwnerName ?? "",
            };
            if (AppSettingConfig.SentryMode == VersionEnum.CloudServer && isSupplement)
            {
                if (isNoEnter)
                    parkOrder.ParkOrder_UserNo = "4";
                else
                    parkOrder.ParkOrder_UserNo = "1";
            }

            return parkOrder;
        }

        /// <summary>
        /// 车辆出/入场 开闸成功后回调 更新订单
        /// </summary>
        /// <param name="order"></param>
        /// <param name="detail"></param>
        /// <returns></returns>
        public static int CarInComplete(Model.ParkOrder order, Model.OrderDetail detail)
        {
            if (order == null && detail == null) return 0;

            //order.ParkOrder_EnterImgPath = Utils.RegReplaceIP(order.ParkOrder_EnterImgPath, Config.AppSettingConfig.SiteDomain_IPPort);
            //order.ParkOrder_OutImgPath = Utils.RegReplaceIP(order.ParkOrder_OutImgPath, Config.AppSettingConfig.SiteDomain_IPPort);

            List<Model.ParkOrder> orders = new List<Model.ParkOrder>() { order };
            List<Model.OrderDetail> details = new List<Model.OrderDetail>() { detail };
            return CarInComplete(orders, details);
        }

        /// <summary>
        /// 车辆出/入场 开闸成功后回调 更新订单
        /// </summary>
        /// <param name="order"></param>
        /// <param name="detail"></param>
        /// <returns></returns>
        public static int CarInComplete(Model.ParkOrder order, List<Model.OrderDetail> details)
        {
            //if (order != null)
            //{
            //    order.ParkOrder_EnterImgPath = Utils.RegReplaceIP(order.ParkOrder_EnterImgPath, Config.AppSettingConfig.SiteDomain_IPPort);
            //    order.ParkOrder_OutImgPath = Utils.RegReplaceIP(order.ParkOrder_OutImgPath, Config.AppSettingConfig.SiteDomain_IPPort);
            //}
            List<Model.ParkOrder> orders = new List<Model.ParkOrder>() { order };
            return CarInComplete(orders, details);
        }

        /// <summary>
        /// 车辆出/入场 开闸成功后回调 更新订单
        /// </summary>
        /// <param name="orders"></param>
        /// <param name="details"></param>
        /// <param name="reserves"></param>
        /// <param name="incar"></param>
        /// <param name="incarUpdate">是否更新Incar状态</param>
        /// <param name="isForward">是否其它岗亭转发信息</param>
        /// <returns></returns>
        public static int CarInComplete(List<Model.ParkOrder> orders, List<Model.OrderDetail> details, List<Model.Reserve> reserves = null,
        Model.InCar incar = null, bool incarUpdate = true, bool isForward = false, List<Model.CouponRecord> coupons = null, List<Model.CouponPlan> couponPlanList = null,
        List<Model.Owner> owners = null, List<Model.Car> cars = null, List<Model.OverdueBill> overdueBills = null)
        {
            List<Model.ParkOrder> estOrderList = new List<Model.ParkOrder>();
            List<Model.OrderDetail> estDetailList = new List<Model.OrderDetail>();

            if (isForward && orders?.Count > 0)
            {
                var po = orders.Where(x => x != null).OrderBy(x => x.ParkOrder_EnterTime).ToList().LastOrDefault();
                Model.InCar inModel = BLL.BaseBLL._GetEntityByWhere(new Model.InCar(), "InCar_ParkOrderNo,InCar_Status,InCar_EnterTime", $"InCar_CarNo='{po.ParkOrder_CarNo}'");
                if (inModel != null)
                {
                    if (po.ParkOrder_No == inModel.InCar_ParkOrderNo)
                    {
                        if (inModel.InCar_Status > EnumParkOrderStatus.In)//同一订单，入场状态大于当前订单状态，不更新Incar状态
                        {
                            incarUpdate = false;
                            po.ParkOrder_StatusNo = inModel.InCar_Status;
                        }
                    }
                    else if (po.ParkOrder_EnterTime <= inModel.InCar_EnterTime)//入场时间小于当前入场记录时间，不更新Incar状态
                    {
                        incarUpdate = false;
                        if (po.ParkOrder_StatusNo < EnumParkOrderStatus.Out) po.ParkOrder_StatusNo = EnumParkOrderStatus.Out;
                    }
                }
            }

            if (incarUpdate)
            {
                #region 如果停车订单状态为预入场，则查询当前车牌号码是否已存在预入场订单，如存在则关闭旧的预入场订单

                List<Model.ParkOrder> nowOrders = orders?.Where(x => x != null).ToList();
                nowOrders = nowOrders?.FindAll(x => x.ParkOrder_StatusNo == Model.EnumParkOrderStatus.Est);
                List<Model.OrderDetail> nowDetails = details?.Where(x => x != null).ToList();
                nowDetails = nowDetails?.FindAll(x => x.OrderDetail_StatusNo == Model.EnumParkOrderStatus.Est);
                var nowOrders_cars = nowOrders?.Select(x => x.ParkOrder_CarNo)?.Distinct()?.ToList();
                var nowDetails_cars = nowDetails?.Select(x => x.OrderDetail_CarNo)?.Distinct()?.ToList();

                bool selIncar = true;
                List<Model.InCar> incarList = null;
                if (nowOrders != null && nowOrders.Count > 0)
                {
                    incarList = BLL.BaseBLL._GetAllEntity(new Model.InCar(), "InCar_ParkOrderNo", $"InCar_CarNo in ('{string.Join("','", nowOrders_cars)}') and  InCar_Status='{Model.EnumParkOrderStatus.Est}'");
                    if (incarList.Count > 0) { selIncar = false; }
                    incarList = incarList.Where(x => !nowOrders.Any(y => y.ParkOrder_No == x.InCar_ParkOrderNo)).ToList();
                    if (incarList != null && incarList.Count > 0)
                    {

                        var d = BLL.ParkOrder.GetAllEntity("ParkOrder_No,ParkOrder_ParkNo,ParkOrder_CarNo,ParkOrder_StatusNo,ParkOrder_IsLift,ParkOrder_IsSettle,ParkOrder_EnterTime", $"ParkOrder_No in ('{string.Join("','", incarList.Select(x => x.InCar_ParkOrderNo))}') and ParkOrder_StatusNo='{Model.EnumParkOrderStatus.Est}'");
                        if (d != null)
                        {
                            d.ForEach(x => { x.ParkOrder_StatusNo = Model.EnumParkOrderStatus.Close; x.ParkOrder_Remark = $"重复识别,关闭预入场订单"; });
                            estOrderList.AddRange(d);
                        }
                    }
                }

                if (nowDetails != null && nowDetails.Count > 0)
                {
                    if (incarList != null && incarList.Count > 0 && nowOrders_cars.SequenceEqual(nowDetails_cars))
                    {
                        var d = BLL.OrderDetail._GetAllEntity(new Model.OrderDetail(), "OrderDetail_No", $" orderdetail_ParkOrderNo in ('{string.Join("','", incarList.Select(x => x.InCar_ParkOrderNo))}') and  OrderDetail_StatusNo='{Model.EnumParkOrderStatus.Est}'");
                        if (d != null)
                        {
                            d.ForEach(x => { x.OrderDetail_StatusNo = Model.EnumParkOrderStatus.Close; x.OrderDetail_Remark = $"重复识别,关闭预入场订单"; });
                            estDetailList.AddRange(d);
                        }
                    }
                    else
                    {
                        if (selIncar || !(nowOrders_cars?.SequenceEqual(nowDetails_cars) ?? true))
                        {
                            incarList = BLL.BaseBLL._GetAllEntity(new Model.InCar(), "InCar_ParkOrderNo", $"InCar_CarNo in ('{string.Join("','", nowDetails_cars)}') and  InCar_Status='{Model.EnumParkOrderStatus.Est}'");
                            if (nowOrders != null) incarList = incarList.Where(x => !nowOrders.Any(y => y.ParkOrder_No == x.InCar_ParkOrderNo)).ToList();
                            if (incarList != null && incarList.Count > 0)
                            {
                                var d = BLL.OrderDetail._GetAllEntity(new Model.OrderDetail(), "OrderDetail_No", $" orderdetail_ParkOrderNo in ('{string.Join("','", incarList.Select(x => x.InCar_ParkOrderNo))}') and  OrderDetail_StatusNo='{Model.EnumParkOrderStatus.Est}'");
                                if (d != null)
                                {
                                    d.ForEach(x => { x.OrderDetail_StatusNo = Model.EnumParkOrderStatus.Close; x.OrderDetail_Remark = $"重复识别,关闭预入场订单"; });
                                    estDetailList.AddRange(d);
                                }
                            }
                        }
                    }
                }
                #endregion
            }

            orders?.AddRange(estOrderList);
            details?.AddRange(estDetailList);
            orders = orders?.OrderByDescending(x => x.ParkOrder_StatusNo).ToList();

            var result = dal.CarInComplete(orders, details, coupons, reserves, incar, incarUpdate, couponPlanList, owners, cars, overdueBills: overdueBills);
            if (result > 0)
            {
                reserves?.ForEach(item =>
                {
                    AppBasicCache.AddOrUpdateElement(AppBasicCache.GetReserve, item.Reserve_No, item);
                });
                owners?.ForEach(item =>
                {
                    AppBasicCache.AddOrUpdateElement(AppBasicCache.GetOwner, item.Owner_No, item);
                });
                cars?.ForEach(item =>
                {
                    AppBasicCache.AddOrUpdateElement(AppBasicCache.GetCar, item.Car_CarNo, item);
                });
            }
            return result;
        }

        public static int UpdateAndCoupon(List<Model.ParkOrder> orders, List<Model.OrderDetail> details, List<Model.CouponRecord> coupons, Model.InCar incar = null)
        {
            return dal.CarInComplete(orders, details, coupons, null, incar, false);
        }

        /// <summary>
        /// 车牌号查询在场内的订单和明细
        /// </summary>
        /// <param name="parkno"></param>
        /// <param name="carno"></param>
        /// <returns></returns>
        public static (Model.ParkOrder, List<Model.OrderDetail>) GetOrderAndThatDetail(string parkno, string carno)
        {
            Model.ParkOrder d = BLL.ParkOrder.GetParkOrder("*", parkno, carno, null);
            //if (d != null)
            //{
            //    d.ParkOrder_EnterImgPath = Utils.RegReplaceIP(d.ParkOrder_EnterImgPath, Config.AppSettingConfig.SiteDomain_IPPort);
            //    d.ParkOrder_OutImgPath = Utils.RegReplaceIP(d.ParkOrder_OutImgPath, Config.AppSettingConfig.SiteDomain_IPPort);
            //}
            List<Model.OrderDetail> details = null;
            if (d != null)
                details = BLL.OrderDetail.GetAllEntity(d.ParkOrder_No);
            return (d, details);
        }

        /// <summary>
        /// 查询停车场【未出场】的停车订单和【未出场 或 未结算】的明细
        /// </summary>
        /// <param name="parkno">车场编号</param>
        /// <param name="carno">车牌号码</param>
        /// <param name="istatus">进出场状态</param>
        /// <returns></returns>
        public static (Model.ParkOrder, List<Model.OrderDetail>) GetOrderDetailByCarNo(string carno)
        {
            Model.InCar inModel = BLL.BaseBLL._GetEntityByWhere(new Model.InCar(), "InCar_ParkOrderNo", $"InCar_CarNo='{carno}' and InCar_Status in (200,199)");
            Model.ParkOrder parkOrder = inModel == null ? null : BLL.ParkOrder.GetEntity(inModel.InCar_ParkOrderNo);

            List<Model.OrderDetail> details = null;
            if (parkOrder != null)
            {
                //LogManagementMap.WriteToFile(LoggerEnum.MainInfoLog, "订单：" + TyziTools.Json.ToString(parkOrder));
                details = BLL.OrderDetail._GetAllEntity(new Model.OrderDetail(), "*", $" OrderDetail_ParkOrderNo='{parkOrder.ParkOrder_No}'");
                if (details != null && details.Count > 0)
                {
                    //LogManagementMap.WriteToFile(LoggerEnum.MainInfoLog, "明细：" + TyziTools.Json.ToString(details));
                    details = details.Where(x => x.OrderDetail_StatusNo == Model.EnumParkOrderStatus.In
                    || x.OrderDetail_StatusNo == Model.EnumParkOrderStatus.Est || x.OrderDetail_IsSettle == 0).OrderBy(x => x.OrderDetail_EnterTime).ToList();
                }
            }

            return (parkOrder, details);
        }


        /// <summary>
        /// 查询停车场未出场的停车订单和明细
        /// </summary>
        /// <param name="parkno">车场编号</param>
        /// <param name="carno">车牌号码</param>
        /// <param name="istatus">进出场状态</param>
        /// <returns></returns>
        public static (List<Model.ParkOrder>, List<Model.OrderDetail>) GetNoRecordOrderByCarNo(string carno, string entertime, int gate = 0)
        {
            List<Model.ParkOrder> parkOrderList = BLL.BaseBLL._GetAllEntity(new Model.ParkOrder(), "*", $"ParkOrder_CarNo='{carno}' and ParkOrder_EnterTime>='{entertime}' {(gate == 0 ? "and ParkOrder_IsNoInRecord=1" : "")} and ParkOrder_StatusNo in (200,199)");
            parkOrderList = parkOrderList.OrderBy(x => x.ParkOrder_EnterTime).ToList();
            List<Model.OrderDetail> details = null;
            Model.ParkOrder po = null;
            if (parkOrderList.Count > 0)
            {
                po = parkOrderList.Last();
                details = BLL.OrderDetail._GetAllEntity(new Model.OrderDetail(), "*", $" OrderDetail_ParkOrderNo='{po.ParkOrder_No}'");
                if (details != null && details.Count > 0)
                {
                    details = details.Where(x => x.OrderDetail_StatusNo == Model.EnumParkOrderStatus.In
                    || x.OrderDetail_StatusNo == Model.EnumParkOrderStatus.Est || x.OrderDetail_IsSettle == 0).OrderBy(x => x.OrderDetail_EnterTime).ToList();
                }
            }

            return (parkOrderList, details);
        }

        /// <summary>
        /// 查询停车场未出场的停车订单
        /// </summary>
        /// <param name="parkno"></param>
        /// <param name="carno"></param>
        /// <returns></returns>
        public static Model.ParkOrder GetParkOrderByCarNo(string carno)
        {
            Model.InCar inModel = BLL.BaseBLL._GetEntityByWhere(new Model.InCar(), "InCar_ParkOrderNo", $"InCar_CarNo='{carno}' and InCar_Status in (200,199)");
            Model.ParkOrder parkOrder = inModel == null ? null : BLL.ParkOrder.GetEntity(inModel.InCar_ParkOrderNo);
            return parkOrder;
        }

        /// <summary>
        /// 查询停车场未出场的停车订单
        /// </summary>
        /// <param name="parkno"></param>
        /// <param name="carno"></param>
        /// <returns></returns>
        public static Model.ParkOrder GetEntityByCarNo(string carno)
        {
            Model.InCar inModel = BLL.BaseBLL._GetEntityByWhere(new Model.InCar(), "InCar_ParkOrderNo", $"InCar_CarNo='{carno}'");
            Model.ParkOrder parkOrder = inModel == null ? null : BLL.ParkOrder.GetEntity(inModel.InCar_ParkOrderNo);
            return parkOrder;
        }

        /// <summary>
        /// 查询停车场未出场的停车订单
        /// </summary>
        /// <param name="parkno"></param>
        /// <param name="carno"></param>
        /// <returns></returns>
        public static List<Model.ParkOrder> GetInCarNo(string parkno, string carno, int limit = 5)
        {
            List<Model.ParkOrder> d = BLL.ParkOrder.GetSameParkOrder("ParkOrder_CarNo", parkno, carno, null, 200, limit);

            //d.ForEach(x =>
            //{
            //    x.ParkOrder_EnterImgPath = Utils.RegReplaceIP(x.ParkOrder_EnterImgPath, Config.AppSettingConfig.SiteDomain_IPPort);
            //    x.ParkOrder_OutImgPath = Utils.RegReplaceIP(x.ParkOrder_OutImgPath, Config.AppSettingConfig.SiteDomain_IPPort);
            //});
            return d;
        }

        public static List<Model.InCar> GetInCarNo(string carno, int limit = 5)
        {
            List<Model.InCar> d = BLL.ParkOrder.GetSameParkOrder("InCar_CarNo", carno, limit);

            //d.ForEach(x =>
            //{
            //    x.ParkOrder_EnterImgPath = Utils.RegReplaceIP(x.ParkOrder_EnterImgPath, Config.AppSettingConfig.SiteDomain_IPPort);
            //    x.ParkOrder_OutImgPath = Utils.RegReplaceIP(x.ParkOrder_OutImgPath, Config.AppSettingConfig.SiteDomain_IPPort);
            //});
            return d;
        }

        /// <summary>
        /// 查询停车场未出场的停车订单和已入场明细
        /// </summary>
        /// <param name="parkno"></param>
        /// <param name="carno"></param>
        /// <returns></returns>
        public static (Model.ParkOrder, List<Model.OrderDetail>) GetOrderByInCar(string parkno, string carno)
        {
            Model.ParkOrder d = BLL.ParkOrder.GetParkOrder("*", parkno, carno);

            //if (d != null)
            //{
            //    d.ParkOrder_EnterImgPath = Utils.RegReplaceIP(d.ParkOrder_EnterImgPath, Config.AppSettingConfig.SiteDomain_IPPort);
            //    d.ParkOrder_OutImgPath = Utils.RegReplaceIP(d.ParkOrder_OutImgPath, Config.AppSettingConfig.SiteDomain_IPPort);
            //}

            string selectWere = string.Empty;
            selectWere += $" OrderDetail_ParkNo='{parkno}'";
            selectWere += $" AND OrderDetail_CarNo='{carno}'";
            selectWere += $" AND (OrderDetail_StatusNo='{Model.EnumParkOrderStatus.In}')";
            if (d != null)
                selectWere += $" AND OrderDetail_ParkOrderNo='{d.ParkOrder_No}'";
            List<Model.OrderDetail> details = BLL.OrderDetail._GetAllEntity(new Model.OrderDetail(), "*", selectWere);

            return (d, details);
        }

        /// <summary>
        /// 查询停车场预入场定订单和明细
        /// </summary>
        /// <param name="parkno"></param>
        /// <param name="carno"></param>
        /// <returns></returns>
        public static (Model.ParkOrder, List<Model.OrderDetail>) GetOrderByCarNoWithEst(string parkno, string carno)
        {
            Model.ParkOrder d = BLL.ParkOrder.GetParkOrder("*", parkno, carno, 0, Model.EnumParkOrderStatus.Est);

            //if (d != null)
            //{
            //    d.ParkOrder_EnterImgPath = Utils.RegReplaceIP(d.ParkOrder_EnterImgPath, Config.AppSettingConfig.SiteDomain_IPPort);
            //    d.ParkOrder_OutImgPath = Utils.RegReplaceIP(d.ParkOrder_OutImgPath, Config.AppSettingConfig.SiteDomain_IPPort);
            //}
            string selectWere = string.Empty;
            selectWere += $" AND OrderDetail_ParkNo='{parkno}'";
            selectWere += $" AND OrderDetail_CarNo='{carno}'";
            selectWere += $" AND (OrderDetail_StatusNo='{Model.EnumParkOrderStatus.Est}')";
            List<Model.OrderDetail> details = BLL.OrderDetail._GetAllEntity(new Model.OrderDetail(), "*", selectWere);

            return (d, details);
        }

        /// <summary>
        /// 查询停车订单和停车明细（通过停车订单号）
        /// </summary>
        /// <param name="parkno"></param>
        /// <param name="orderNo"></param>
        /// <returns></returns>
        public static (Model.ParkOrder, List<Model.OrderDetail>) GetDetailByOrderNo(string orderNo)
        {
            Model.ParkOrder d = BLL.ParkOrder.GetEntity(orderNo);

            //if (d != null)
            //{
            //    d.ParkOrder_EnterImgPath = Utils.RegReplaceIP(d.ParkOrder_EnterImgPath, Config.AppSettingConfig.SiteDomain_IPPort);
            //    d.ParkOrder_OutImgPath = Utils.RegReplaceIP(d.ParkOrder_OutImgPath, Config.AppSettingConfig.SiteDomain_IPPort);
            //}
            List<Model.OrderDetail> details = null;
            if (d != null)
            {
                string selectWere = string.Empty;
                selectWere += $" OrderDetail_ParkNo='{d.ParkOrder_ParkNo}'";
                selectWere += $" AND OrderDetail_ParkOrderNo='{d.ParkOrder_No}'";
                details = BLL.OrderDetail._GetAllEntity(new Model.OrderDetail(), "*", selectWere);
            }
            return (d, details);
        }

        public delegate List<Model.ParkOrder> CallbackDelegate(Model.ParkOrder po, List<Model.ParkOrder> newLst);


        public static List<Model.ParkOrder> ChangeOrderLst(Model.ParkOrder po, List<Model.ParkOrder> newLst)
        {
            if (po == null) return newLst;

            if (newLst != null)
            {
                for (var i = 0; i < newLst.Count; i++)
                {
                    if (newLst[i].ParkOrder_No == po.ParkOrder_No)
                    {
                        po.ParkOrder_IsLift = newLst[i].ParkOrder_IsLift;
                        newLst.RemoveAt(i);
                    }
                }
                newLst.Insert(0, po);
                return newLst;
            }
            else
            {
                return new List<Model.ParkOrder>() { po };
            }
        }

        public static List<Model.OrderDetail> ChangeDetailLst(List<Model.OrderDetail> detailLst, List<Model.OrderDetail> newLst)
        {
            if (detailLst == null) return newLst;

            if (newLst != null)
            {
                for (var i = 0; i < newLst.Count; i++)
                {
                    var model = detailLst.Find(x => x.OrderDetail_No == newLst[i].OrderDetail_No);
                    if (model != null)
                    {
                        detailLst.Remove(model);
                        model.orderdetail_IsCharge = newLst[i].orderdetail_IsCharge;
                        model.OrderDetail_StatusNo = newLst[i].OrderDetail_StatusNo;
                        model.OrderDetail_OutTime = newLst[i].OrderDetail_OutTime;
                        model.OrderDetail_OutPasswayName = newLst[i].OrderDetail_OutPasswayName;
                        model.OrderDetail_Remark = newLst[i].OrderDetail_Remark;
                        newLst.RemoveAt(i);
                        detailLst.Add(model);
                    }
                }
                detailLst.AddRange(newLst);
                return detailLst;
            }
            else
            {
                return detailLst;
            }
        }

        /// <summary>
        /// 车辆出场（若区域内在场车辆数量 大于 拥有的车位数，则在该车辆出场时，对其它场内的某一车辆进行升降处理）
        /// </summary>
        /// <param name="parkOrder">当前停车订单</param>
        /// <param name="callback">回调函数(需要修改升降状态的停车订单、停车明细)</param>
        /// <returns></returns>
        public static void OutParkForMultiCar(Model.ParkOrder parkOrder, string inAreaNo, string outAreaNo, Action<List<Model.ParkOrder>, List<Model.OrderDetail>> callback)
        {
            List<Model.ParkOrder> parkOrderList = null;
            List<Model.OrderDetail> detailList = null;

            if (string.IsNullOrWhiteSpace(outAreaNo)) { callback(parkOrderList, detailList); return; }
            if (parkOrder == null) { callback(parkOrderList, detailList); return; }
            Model.CarCardType cartType = BLL.CarCardType.GetEntity(parkOrder.ParkOrder_CarCardType);
            if (cartType != null && cartType.CarCardType_Category != EnumCarType.Prepaid.ToString() && cartType.CarCardType_IsMoreCar == 1 && parkOrder.ParkOrder_IsLift > 0)//当前车辆属于多车多位类型（CarCardType_IsMoreCar），并且是免收费状态（ParkOrder_IsLift）
            {
                Model.ParkArea area = BLL.ParkArea.GetEntity(outAreaNo);
                var policyPassway = BLL.PolicyArea.GetEntity(outAreaNo);
                if (policyPassway.PolicyArea_MoreCar == 1)
                {
                    Model.Car car = BLL.Car.GetEntityByCarNo(parkOrder.ParkOrder_CarNo);//查询车辆信息
                    if (car == null) { callback(parkOrderList, detailList); return; }

                    Model.OrderDetail currOrderDetail = BLL.OrderDetail.GetAllEntity("*", $"OrderDetail_ParkOrderNo='{parkOrder.ParkOrder_No}'")?.Find(x => x.OrderDetail_StatusNo == Model.EnumParkOrderStatus.In);
                    if (currOrderDetail == null) { callback(parkOrderList, detailList); return; }//查询出场停车明细信息

                    Model.ParkArea currentArea = BLL.PasswayLink.GetCurrentParkArea(currOrderDetail.OrderDetail_OutPasswayNo);

                    Model.Owner owner = BLL.Owner.GetEntity(car.Car_OwnerNo);//查询车主信息
                    if (owner == null) { callback(parkOrderList, detailList); return; }

                    //查询一位多车信息（车位数，当前区域的与当前车主相关的场内车辆数）
                    string errmsg;
                    var result = BLL.Pass.IsSpaceCar(car, owner, cartType, true, inAreaNo, outAreaNo, out var outdata, out errmsg);
                    if (!result) { callback(parkOrderList, detailList); return; }
                    //if (outdata.expired) { callback(parkOrderList, detailList); return; }

                    bool moreSpace = false;
                    if (parkOrder.ParkOrder_IsLift > 0 && currOrderDetail.orderdetail_IsCharge == 1)//当前车辆占到车位
                    {
                        //入场区的区域无车位 或者 入场区的区域有车位并且不与出场区域共用的车位
                        if (outdata.spacenum == 0 || outdata.spacenum >= 1 && outdata.spacenum3 < outdata.spacenum)
                        {
                            //则需要把车位给别的车辆
                            moreSpace = true;
                        }
                    }

                    var currCarOrder = outdata.details2.Find(x => x.OrderDetail_CarNo == parkOrder.ParkOrder_CarNo);
                    //将出场区域(车主下的车辆已占用当前区域全部车位)
                    if (outdata.details2.Count > outdata.spacenum2 && outdata.spacenum2 > 0 && moreSpace)//|| (currCarOrder != null && outdata.details2.Count >= outdata.spacenum2))
                    {
                        if (currCarOrder != null && currCarOrder.orderdetail_IsCharge == 1)//当前将出场的车辆在该区域属于免费，其它某一车辆进行升降
                        {
                            var whData = outdata.details2.Where(x => x.OrderDetail_CarNo != parkOrder.ParkOrder_CarNo && x.orderdetail_IsCharge != 1)?.ToList();
                            if (whData != null && whData.Count > 0)
                            {
                                parkOrderList = new List<Model.ParkOrder>();
                                detailList = new List<Model.OrderDetail>();
                                Model.ParkOrder firstOrder = null; //将要智能升降的车辆订单
                                Model.OrderDetail firstDetail = null;//将要智能升降的订单明细
                                Model.OrderDetail newfirstDetail = null;//新建的订单明细

                                //筛选入场最早的车辆
                                var willCarOrder = whData.OrderBy(x => x.OrderDetail_EnterTime).ToList();
                                foreach (var item in willCarOrder)
                                {
                                    var policyare = AppBasicCache.GetElement(AppBasicCache.GetPolicyareaes, item.OrderDetail_ParkAreaNo);
                                    if (policyare?.PolicyArea_MoreCar == 1 && item.OrderDetail_ParkOrderNo != parkOrder.ParkOrder_No)
                                    {
                                        firstOrder = BLL.ParkOrder.GetEntity(item.OrderDetail_ParkOrderNo);
                                        if (firstOrder != null)
                                        {
                                            firstDetail = item;
                                            break;
                                        }
                                    }
                                }

                                //智能升降
                                if (firstOrder != null)
                                {
                                    firstOrder.ParkOrder_IsLift = 2;
                                    parkOrderList.Add(firstOrder);

                                    newfirstDetail = TyziTools.Json.ToModel<Model.OrderDetail>(TyziTools.Json.ToString(firstDetail));
                                    firstDetail.OrderDetail_StatusNo = (int)ParkOrderStatusEnum.Out;
                                    firstDetail.OrderDetail_OutTime = DateTimeHelper.GetNowTime();
                                    firstDetail.OrderDetail_OutPasswayName = "一位多车智能升降";
                                    firstDetail.orderdetail_IsCharge = 0;//需缴费
                                    detailList.Add(firstDetail);

                                    newfirstDetail.OrderDetail_No = "OC" + Utils.CreateNumber;
                                    newfirstDetail.OrderDetail_EnterTime = firstDetail.OrderDetail_OutTime;
                                    newfirstDetail.OrderDetail_IsSettle = 0;
                                    newfirstDetail.orderdetail_IsCharge = 1;//免费
                                    newfirstDetail.OrderDetail_Remark = $"{parkOrder.ParkOrder_CarNo}出场，由{newfirstDetail.OrderDetail_CarNo}占有车位";
                                    newfirstDetail.OrderDetail_OutTime = DateTime.Parse("1900-01-01 00:00:00");
                                    newfirstDetail.OrderDetail_StatusNo = (int)ParkOrderStatusEnum.Enter;
                                    detailList.Add(newfirstDetail);
                                }
                            }
                        }
                    }

                }
            }

            callback(parkOrderList, detailList);
        }

        /// <summary>
        /// 根据车牌号获取需要关闭的订单(重复入场时读取)
        /// </summary>
        /// <param name="CarNo"></param>
        /// <returns></returns>
        public static bool CloseParkOrder(string parkno, string CarNo, string orderno, out Model.ParkOrder parkOrder, out List<Model.OrderDetail> details)
        {
            parkOrder = null;
            var detailDatas = new List<Model.OrderDetail>();

            var order = BLL.ParkOrder.GetOrderDetailByCarNo(CarNo);
            if (order.Item1 != null && order.Item1.ParkOrder_No != orderno)
            {
                order.Item1.ParkOrder_StatusNo = Model.EnumParkOrderStatus.Close;
                order.Item1.ParkOrder_Remark += ".场内修改,重复入场,关闭订单.";
                parkOrder = order.Item1;
            }

            order.Item2?.ForEach(item =>
            {
                if (item != null && item.OrderDetail_ParkOrderNo != orderno)
                {
                    item.OrderDetail_StatusNo = Model.EnumParkOrderStatus.Close;
                    item.OrderDetail_Remark += ".场内修改,重复入场,关闭订单.";
                    detailDatas.Add(item);
                }
            });
            details = detailDatas;

            if (parkOrder != null || (details != null && details.Count > 0))
                return true;
            return false;
        }

        /// <summary>
        /// 自定义条件分页,获得实体列表(用于查询外部数据库)
        /// </summary>
        public static List<Model.ParkOrder> GetAllEntity(string showFields, string selectWhere, System.Data.IDbConnection dbConnection)
        {
            var d = dal.GetAllEntity(showFields, selectWhere, dbConnection);
            //d.ForEach(x =>
            //{
            //    x.ParkOrder_EnterImgPath = Utils.RegReplaceIP(x.ParkOrder_EnterImgPath, Config.AppSettingConfig.SiteDomain_IPPort);
            //    x.ParkOrder_OutImgPath = Utils.RegReplaceIP(x.ParkOrder_OutImgPath, Config.AppSettingConfig.SiteDomain_IPPort);
            //});
            return d;

        }

        #endregion

        /// <summary>
        /// 合并订单列表，数据冲突则以data2数据为最新
        /// </summary>
        /// <param name="data1"></param>
        /// <param name="data2"></param>
        /// <returns></returns>
        public static List<Model.ParkOrder> MergeList(List<Model.ParkOrder> data1, List<Model.ParkOrder> data2)
        {
            if (data1 == null) return data2;
            if (data2 == null) return data1;

            data2?.ForEach(item =>
            {
                data1?.RemoveAll(x => x.ParkOrder_No == item.ParkOrder_No);
            });

            data1?.AddRange(data2);
            return data1;
        }

        /// <summary>
        /// 岗亭上传出入场记录时判断场内是否已存在此车牌
        /// </summary>
        /// <param name="orders">停车订单</param>
        /// <param name="details">订单明细</param>
        public static void InOutRecordWithOrder(ref List<Model.ParkOrder> orders, ref List<Model.OrderDetail> details, List<Model.InCar> incarList = null,
            List<Model.ParkOrder> orderList = null, List<Model.OrderDetail> detailList = null)
        {
            orders?.Remove(null);
            details?.Remove(null);
            if (orders == null || orders.Count == 0) return;

            List<string> ingoreOrderList = new List<string>();
            List<string> ingoreDetailList = new List<string>();

            List<Model.ParkOrder> existOrderList = new List<Model.ParkOrder>();
            List<Model.OrderDetail> existDetailList = new List<Model.OrderDetail>();

            foreach (var item in orders)
            {
                if (item.ParkOrder_StatusNo == Model.EnumParkOrderStatus.In)
                {
                    #region 判断是否已存在场内记录
                    Model.ParkOrder Exist = null;
                    var incar = incarList != null ? incarList.Find(x => x.InCar_Status == 200 && x.InCar_CarNo == item.ParkOrder_CarNo && x.InCar_ParkAreaNo == item.ParkOrder_ParkAreaNo)
                        : BLL.BaseBLL._GetEntityByWhere(new Model.InCar(), "InCar_ParkOrderNo", $"InCar_Status='{Model.EnumParkOrderStatus.In}' and InCar_CarNo='{item.ParkOrder_CarNo}' and InCar_ParkAreaNo='{item.ParkOrder_ParkAreaNo}' ");
                    if (incar != null && incar.InCar_ParkOrderNo != item.ParkOrder_No)
                    {
                        Exist = orderList != null ? orderList.Find(x => x.ParkOrder_No == incar.InCar_ParkOrderNo) : BLL.ParkOrder.GetEntity("*", $"ParkOrder_No='{incar.InCar_ParkOrderNo}'");
                    }

                    if (Exist != null)
                    {
                        if (item.ParkOrder_EnterTime > Exist.ParkOrder_EnterTime)
                        {
                            Exist.ParkOrder_StatusNo = Model.EnumParkOrderStatus.Close;
                            Exist.ParkOrder_Remark = "重复入场,自动关闭";
                            existOrderList.Add(Exist);
                        }
                        else
                        {
                            ingoreOrderList.Add(item.ParkOrder_No);
                            var des = details?.FindAll(x => x.OrderDetail_ParkOrderNo == item.ParkOrder_No)?.Select(x => x.OrderDetail_No).ToList();
                            ingoreDetailList.AddRange(des ?? new List<string>());
                        }
                    }
                    #endregion
                }
            }

            if (existOrderList.Count > 0) orders.AddRange(existOrderList);
            orders.RemoveAll(x => ingoreOrderList.Contains(x.ParkOrder_No));
            details?.RemoveAll(x => ingoreDetailList.Contains(x.OrderDetail_No));

            if (details?.Count > 0)
            {
                foreach (var item in details)
                {
                    StringBuilder str = new StringBuilder();
                    str.Append($" OrderDetail_ParkOrderNo='{item.OrderDetail_ParkOrderNo}' ");
                    str.Append($" AND OrderDetail_ParkAreaNo='{item.OrderDetail_ParkAreaNo}' ");
                    var Exist = detailList != null ? detailList.FindAll(x => x.OrderDetail_ParkOrderNo == item.OrderDetail_ParkOrderNo && x.OrderDetail_ParkAreaNo == item.OrderDetail_ParkAreaNo)
                        : BLL.OrderDetail.GetAllEntity("*", str.ToString());

                    var ss = Exist.Find(x => x.OrderDetail_No == item.OrderDetail_No);
                    switch (item.OrderDetail_StatusNo)
                    {
                        case Model.EnumParkOrderStatus.In:
                            if (ss != null)
                            {
                                if (ss.OrderDetail_EnterTime >= item.OrderDetail_EnterTime)
                                    ingoreDetailList.Add(item.OrderDetail_No);
                            }
                            else
                            {
                                var ee = Exist.Find(x => x.OrderDetail_StatusNo == Model.EnumParkOrderStatus.In);
                                if (ee != null)
                                {
                                    if (ee.OrderDetail_EnterTime >= item.OrderDetail_EnterTime)
                                        ingoreDetailList.Add(item.OrderDetail_No);
                                    else
                                    {
                                        LogManagementMap.WriteToFile(LoggerEnum.WebAPILog, $"InOutRecordWithOrder：岗亭上传记录重复入场{TyziTools.Json.ToString(item)}", LogLevel.Debug);
                                        ee.OrderDetail_StatusNo = Model.EnumParkOrderStatus.Close;
                                        ee.OrderDetail_Remark = "重复入场,自动关闭";
                                        existDetailList.Add(ee);
                                    }
                                }

                                var et = Exist.FindAll(x => x.OrderDetail_StatusNo == Model.EnumParkOrderStatus.Out)?.OrderByDescending(x => x.OrderDetail_EnterTime).FirstOrDefault();
                                if (et != null)
                                {
                                    if (et.OrderDetail_OutTime > item.OrderDetail_EnterTime)
                                    {
                                        LogManagementMap.WriteToFile(LoggerEnum.WebAPILog, $"InOutRecordWithOrder：岗亭上传记录已出场{TyziTools.Json.ToString(item)}", LogLevel.Debug);
                                        item.OrderDetail_StatusNo = Model.EnumParkOrderStatus.Out;
                                    }
                                }
                            }
                            break;
                        default:
                            if (ss == null)
                            {
                                ingoreDetailList.Add(item.OrderDetail_No);
                            }
                            break;
                    }
                }
            }

            if (existDetailList.Count > 0) details?.AddRange(existDetailList);
            details?.RemoveAll(x => ingoreDetailList.Contains(x.OrderDetail_No));
        }

        /// <summary>
        /// 数据太多，每次最多发送50条
        /// </summary>
        /// <param name="carList"></param>
        /// <param name="ownerList"></param>
        /// <param name="stopList"></param>
        public static List<Model.API.PushResultParse.UpdateParkOrder> sendPush(List<Model.ParkOrder> parkOrders, List<Model.OrderDetail> details, int maxCount = 50)
        {
            List<Model.API.PushResultParse.UpdateParkOrder> pushDataList = new List<Model.API.PushResultParse.UpdateParkOrder>();
            Model.API.PushResultParse.UpdateParkOrder pushData = new Model.API.PushResultParse.UpdateParkOrder()
            {
                Item1 = new List<Model.ParkOrder>(),
                Item2 = new List<Model.OrderDetail>()
            };

            foreach (var item in parkOrders)
            {
                if (pushData.Item1.Count < maxCount)
                {
                    pushData.Item1.Add(item);
                    var item2 = details.Find(x => x.OrderDetail_ParkOrderNo == item.ParkOrder_No);
                    if (item2 != null)
                        pushData.Item2.Add(item2);
                }
                if (parkOrders.IndexOf(item) == parkOrders.Count - 1 || pushData.Item1.Count == maxCount)
                {
                    pushDataList.Add(pushData);

                    pushData = new Model.API.PushResultParse.UpdateParkOrder()
                    {
                        Item1 = new List<Model.ParkOrder>(),
                        Item2 = new List<Model.OrderDetail>()
                    };
                }
            }

            return pushDataList;
        }


        /// <summary>
        /// 注销车辆时，更新场内记录
        /// </summary>
        /// <param name="parkno"></param>
        /// <param name="orders"></param>
        /// <param name="details"></param>
        public static void UpdateByLogoutCar(string parkno, List<Model.ParkOrder> orders, out List<Model.ResBodyDataIn> datas)
        {
            datas = new List<Model.ResBodyDataIn>();
            DateTime curdt = DateTimeHelper.GetNowTime();
            if (orders != null && orders.Count > 0)
            {
                var carddef = AppBasicCache.GetCarcardTypes.Values.FirstOrDefault(x => x.CarCardType_Type == 1);
                string sqlOrderNo = string.Join("','", orders.Select(x => x.ParkOrder_No));
                var details = BLL.OrderDetail.GetAllEntity("*", $"OrderDetail_ParkOrderNo in ('{sqlOrderNo}')");
                foreach (var order in orders)
                {
                    DataCache.ParkOrder.Del(order.ParkOrder_No);

                    string cardno, cardname;
                    var p = AppBasicCache.GetAllPolicyPassway.Values.FirstOrDefault(x => x.PolicyPassway_PasswayNo == order.ParkOrder_EnterPasswayNo);
                    carddef = AppBasicCache.GetCarcardTypes.Values.FirstOrDefault(x => x.CarCardType_No == p?.PolicyPassway_DefaultCarCardType) ?? carddef;
                    cardno = carddef.CarCardType_No;
                    cardname = carddef.CarCardType_Name;

                    var PolicyPark_MonthlyToTempFee = AppBasicCache.GetPolicyPark?.PolicyPark_MonthlyToTempFee ?? 0;
                    if (PolicyPark_MonthlyToTempFee == 1 && (AppBasicCache.GetPolicyPark.PolicyPark_MonthlyToTempFeeTimeType ?? 0) != 0)
                    {
                        if (AppBasicCache.GetPolicyPark.PolicyPark_MonthlyToTempFeeTimeType == 1)
                        {
                            curdt = order.ParkOrder_EnterTime.Value;
                        }
                    }

                    order.ParkOrder_IsLift = order.ParkOrder_IsLift == 1 ? 2 : order.ParkOrder_IsLift;
                    order.ParkOrder_EnterTime = curdt;
                    order.ParkOrder_OutTime = DateTime.Parse("1900-01-01 00:00:00");
                    order.ParkOrder_OutType = 0;
                    order.ParkOrder_CarCardType = cardno;
                    order.ParkOrder_CarCardTypeName = cardname;

                    var d = details.FindAll(x => x.OrderDetail_ParkOrderNo == order.ParkOrder_No);
                    if (d != null && d.Count > 0)
                    {
                        d.ForEach(di =>
                        {
                            di.OrderDetail_IsSettle = 1;
                            di.OrderDetail_StatusNo = Model.EnumParkOrderStatus.InClose;
                            di.OrderDetail_Remark = "注销车辆关闭订单";
                        });

                        var din = d.OrderByDescending(x => x.OrderDetail_EnterTime).FirstOrDefault();
                        var nd = TyziTools.Json.ToObject<Model.OrderDetail>(TyziTools.Json.ToString(din));

                        nd.OrderDetail_No = Utils.CreateNumberWith("ZX");
                        nd.OrderDetail_EnterTime = curdt;
                        nd.OrderDetail_CarCardType = cardno;
                        nd.OrderDetail_CarCardTypeName = cardname;
                        nd.OrderDetail_StatusNo = Model.EnumParkOrderStatus.In;
                        nd.orderdetail_EnterRemark = "注销车辆自动登记";
                        nd.OrderDetail_Remark = "注销车辆自动登记";
                        nd.orderdetail_IsCharge = 0;//临时车收费
                        nd.OrderDetail_IsSettle = 0;
                        d.Add(nd);
                    }

                    var data = new Model.ResBodyDataIn(new List<Model.ParkOrder> { order }, d);
                    datas.Add(data);
                }

                datas?.ForEach(item =>
                {
                    BLL.ParkOrder.CarInComplete(item.Item1, item.Item2);
                });
            }
        }

        /// <summary>
        /// 车牌是否重点防疫地区
        /// </summary>
        /// <param name="parkOrder"></param>
        /// <param name="policyArea"></param>
        public static void EpParkOrder(ref Model.ParkOrder parkOrder, Model.PolicyArea policyArea = null)
        {
            if (policyArea == null)
                policyArea = BLL.PolicyArea.GetEntity(parkOrder.ParkOrder_ParkAreaNo);

            string carno = parkOrder.ParkOrder_CarNo;
            var carprefix = carno.Length > 2 ? carno.Substring(0, 2) : carno;
            #region 直辖市特殊处理
            if (carno.Contains("沪")) carprefix = "沪";
            else if (carno.Contains("渝")) carprefix = "渝";
            else if (carno.Contains("津")) carprefix = "津";
            else if (carno.Contains("京")) carprefix = "京";
            #endregion
            if (policyArea.PolicyArea_EPAddress != null && !string.IsNullOrWhiteSpace(carprefix) && policyArea.PolicyArea_EPAddress.Contains(carprefix))
            {
                parkOrder.ParkOrder_IsEpCar = 1;
            }
        }

        /// <summary>
        /// 修改车辆或入场信息，修正停车订单（是否收费）
        /// </summary>
        /// <param name="newCar">当前车辆</param>
        /// <param name="newOwner">当前车主</param>
        /// <param name="carCardType">车牌类型</param>
        /// <param name="parkorder">当前订单</param>
        public static void ModifyAnyToChangeOrder(Model.Car newCar, Model.Owner newOwner, Model.CarCardType carCardType, ref Model.ParkOrder parkorder, ref List<Model.OrderDetail> details,
            List<Model.InCar> allInCarList = null, List<Model.ParkOrder> allParkOrderList = null, List<Model.OrderDetail> allOrderDetailList = null,
            List<Model.StopSpace> allStopsapceList = null, List<Model.Car> allCarList = null, bool isAllCheck = false, bool CheckSpaceNumByEnterTime = true)
        {
            if (parkorder != null && carCardType != null && newOwner != null && newCar != null
              && carCardType.CarCardType_Type != 2 && carCardType.CarCardType_IsMoreCar == 1 && (isAllCheck || parkorder.ParkOrder_IsLift == 0 || parkorder.ParkOrder_IsLift == 2))
            {
                parkorder.ParkOrder_IsLift = 0;
                List<Model.StopSpace> stopsapce = null;
                if (allStopsapceList == null) stopsapce = BLL.BaseBLL._GetAllEntity(new Model.StopSpace(), "*", $"StopSpace_OwnerNo='{newOwner.Owner_No}'");
                else { stopsapce = allStopsapceList.Where(x => x.StopSpace_OwnerNo == newOwner.Owner_No).ToList(); }

                if (stopsapce == null || stopsapce.Count == 0) return;

                List<Model.Car> carLsit = null;
                if (allCarList == null) carLsit = BLL.Car.GetAllEntity("*", $"Car_OwnerNo='{newOwner.Owner_No}'");
                else carLsit = allCarList.Where(x => x.Car_OwnerNo == newOwner.Owner_No).ToList();

                //车主只有一辆车
                if (carLsit.Count == 1)
                {
                    ChangeOrderByCarSpace(stopsapce, ref parkorder, ref details);//订单多车多位状态修改
                }
                //车主有多辆车
                else
                {
                    List<Model.InCar> inCarList = null;
                    if (allInCarList == null) inCarList = BLL.BaseBLL._GetAllEntity(new Model.InCar(), "InCar_ParkOrderNo", $"InCar_Status=200 and InCar_CarNo in ('{string.Join("','", carLsit.Select(x => x.Car_CarNo))}')");
                    else { var carnoList = carLsit.Select(x => x.Car_CarNo).ToList(); inCarList = allInCarList.Where(x => carnoList.Contains(x.InCar_CarNo)).ToList(); }

                    var parkorder_no = parkorder.ParkOrder_No;
                    inCarList.RemoveAll(x => x.InCar_ParkOrderNo == parkorder_no);
                    //场内无其它车辆
                    if (inCarList.Count == 0)
                    {
                        ChangeOrderByCarSpace(stopsapce, ref parkorder, ref details); //订单多车多位状态修改
                    }
                    //场内有其它车辆
                    else
                    {
                        List<Model.ParkOrder> inParkOrderList = null;
                        if (allParkOrderList == null) inParkOrderList = BLL.ParkOrder.GetAllEntity("ParkOrder_IsLift", $"ParkOrder_No in ('{string.Join("','", inCarList.Select(x => x.InCar_ParkOrderNo))}')");
                        else { var nolist = inCarList.Select(x => x.InCar_ParkOrderNo).ToList(); inParkOrderList = allParkOrderList.Where(x => nolist.Contains(x.ParkOrder_No)).ToList(); }

                        //没车占车位
                        var notUseSpace = inParkOrderList.Find(x => x.ParkOrder_IsLift != 0 && x.ParkOrder_IsLift != null) == null;

                        if (inParkOrderList == null || inParkOrderList.Count == 0 || notUseSpace)
                        {
                            ChangeOrderByCarSpace(stopsapce, ref parkorder, ref details); //订单多车多位状态修改
                        }
                        else//有车占车位
                        {
                            List<Model.OrderDetail> inOrderDetailList = null;
                            if (allOrderDetailList == null) inOrderDetailList = BLL.OrderDetail.GetAllEntity("OrderDetail_No,OrderDetail_CarNo,OrderDetail_StatusNo,OrderDetail_ParkAreaNo,orderdetail_IsCharge,OrderDetail_EnterTime", $"OrderDetail_ParkOrderNo in ('{string.Join("','", inCarList.Select(x => x.InCar_ParkOrderNo))}')");
                            else { var nolist = inCarList.Select(x => x.InCar_ParkOrderNo).ToList(); inOrderDetailList = allOrderDetailList.Where(x => nolist.Contains(x.OrderDetail_ParkOrderNo)).ToList(); }

                            inOrderDetailList = inOrderDetailList.OrderByDescending(x => x.OrderDetail_EnterTime).ToList();
                            var allTypeSpaceCount = stopsapce.FindAll(x => x.StopSpace_Type == 0)?.Sum(x => x.StopSpace_Number) ?? 0;
                            int parkOrderIsLift = 0;

                            List<Model.OrderDetail> orderDetails_insert = new List<Model.OrderDetail>();

                            details = details.OrderBy(x => x.OrderDetail_EnterTime).ToList();
                            var details2 = details.Copy();
                            //车位占有判断(内外场来回多次进出,不做升降处理，只判断谁先入场谁占车位)
                            details2.ForEach(item =>
                            {
                                //当前订单明细
                                var inParkDetailSub = allOrderDetailList?.Find(x => x.OrderDetail_No == item.OrderDetail_No);

                                List<Model.OrderDetail> nowOrderDetail = new List<Model.OrderDetail>();
                                inOrderDetailList.ForEach(x =>
                                {
                                    if (x.OrderDetail_ParkAreaNo == item.OrderDetail_ParkAreaNo && nowOrderDetail.Find(m => m.OrderDetail_CarNo == x.OrderDetail_CarNo) == null)
                                        nowOrderDetail.Add(x);
                                });
                                //特定区域其它车辆占据车位数
                                var inAreaUseCount = nowOrderDetail.FindAll(x => x.OrderDetail_ParkAreaNo == item.OrderDetail_ParkAreaNo && x.orderdetail_IsCharge > 0 && x.OrderDetail_CarNo != item.OrderDetail_CarNo
                                && (CheckSpaceNumByEnterTime && x.OrderDetail_EnterTime < item.OrderDetail_EnterTime || !CheckSpaceNumByEnterTime))?.GroupBy(x => x.OrderDetail_CarNo)?.ToList()?.Count ?? 0;
                                //全部区域其它车辆占据车位数
                                var inAllAreaUseCount = nowOrderDetail.FindAll(x => x.orderdetail_IsCharge > 0 && x.OrderDetail_CarNo != item.OrderDetail_CarNo && (CheckSpaceNumByEnterTime && x.OrderDetail_EnterTime < item.OrderDetail_EnterTime || !CheckSpaceNumByEnterTime))?
                                .GroupBy(x => x.OrderDetail_CarNo)?.ToList()?.Count ?? 0;
                                //特定区域车位数
                                var assignTypeSpace = stopsapce?.FindAll(x => x.StopSpace_Type == 1 && x.StopSpace_AreaNo.Contains(item.OrderDetail_ParkAreaNo))?.Sum(x => x.StopSpace_Number) ?? 0;
                                //特定区域并且单区域的车位数
                                var singleElementTypeSpace = stopsapce?.FindAll(x => x.StopSpace_Type == 1 && x.StopSpace_AreaNo.Contains(item.OrderDetail_ParkAreaNo) && x.StopSpace_AreaNo.Split(',').Length == 1)?.Sum(x => x.StopSpace_Number) ?? 0;

                                //特定区域已用完车位
                                if (inAreaUseCount >= assignTypeSpace)
                                {
                                    //全部区域未用完车位
                                    var nowSpaceCount = allTypeSpaceCount + assignTypeSpace - inAllAreaUseCount;
                                    if (nowSpaceCount > 0)
                                    {
                                        item.orderdetail_IsCharge = 1;
                                        if (inParkDetailSub != null) inParkDetailSub.orderdetail_IsCharge = 1;
                                        allTypeSpaceCount = allTypeSpaceCount - (inAreaUseCount - assignTypeSpace) - 1;
                                        parkOrderIsLift = 1;
                                    }
                                    else
                                    {
                                        item.orderdetail_IsCharge = 0;
                                        if (inParkDetailSub != null) inParkDetailSub.orderdetail_IsCharge = 0;
                                        if (nowSpaceCount == 0)
                                        {
                                            var poliyPark = BLL.PolicyArea.GetEntity(item.OrderDetail_ParkAreaNo);
                                            if (poliyPark?.PolicyArea_MoreCar == 1)
                                            {
                                                //判断有一辆车和当前车辆在同一个区域，且在当前车辆入场之前入场，且在当前车辆出当前区域之前出场，则对当前车辆进行升降处理
                                                var inParkDetail = nowOrderDetail.Find(x => x.OrderDetail_CarNo != item.OrderDetail_CarNo && x.OrderDetail_ParkAreaNo == item.OrderDetail_ParkAreaNo
                                                && (CheckSpaceNumByEnterTime && x.OrderDetail_EnterTime < item.OrderDetail_EnterTime || !CheckSpaceNumByEnterTime) && x.OrderDetail_StatusNo >= 201 && (item.OrderDetail_OutTime != null && x.OrderDetail_OutTime < item.OrderDetail_OutTime || item.OrderDetail_OutTime == null));
                                                if (inParkDetail != null)
                                                {
                                                    var newOrderDetail = TyziTools.Json.ToModel<Model.OrderDetail>(TyziTools.Json.ToString(item));
                                                    newOrderDetail.OrderDetail_No = "OC" + Utils.CreateNumber;
                                                    newOrderDetail.OrderDetail_EnterTime = inParkDetail.OrderDetail_EnterTime;
                                                    newOrderDetail.orderdetail_IsCharge = 1;
                                                    newOrderDetail.OrderDetail_Remark = $"{inParkDetail.OrderDetail_CarNo}出场，由{item.OrderDetail_CarNo}占有车位";
                                                    newOrderDetail.OrderDetail_StatusNo = item.OrderDetail_StatusNo;
                                                    orderDetails_insert.Add(newOrderDetail);

                                                    item.OrderDetail_OutTime = newOrderDetail.OrderDetail_EnterTime;
                                                    if (item.OrderDetail_StatusNo < 201) item.OrderDetail_StatusNo = 201;
                                                    if (inParkDetailSub != null)
                                                    {
                                                        inParkDetailSub.OrderDetail_OutTime = newOrderDetail.OrderDetail_EnterTime;
                                                        if (inParkDetailSub.OrderDetail_StatusNo < 201) inParkDetailSub.OrderDetail_StatusNo = 201;
                                                    }
                                                    parkOrderIsLift = 2;
                                                }
                                            }
                                        }
                                    }
                                }
                                else
                                {
                                    if (assignTypeSpace > 0)
                                    {
                                        //车内车辆数比单区域的车位数小
                                        if (inAreaUseCount < singleElementTypeSpace)
                                        {
                                            item.orderdetail_IsCharge = 1;
                                            parkOrderIsLift = 1;
                                            if (inParkDetailSub != null) inParkDetailSub.orderdetail_IsCharge = 1;
                                        }
                                        else
                                        {
                                            var inParkDetailCount = nowOrderDetail?.FindAll(x => x.OrderDetail_CarNo != item.OrderDetail_CarNo && (CheckSpaceNumByEnterTime && x.OrderDetail_EnterTime < item.OrderDetail_EnterTime || !CheckSpaceNumByEnterTime) && (x.OrderDetail_OutTime == null || x.OrderDetail_OutTime > item.OrderDetail_EnterTime || !CheckSpaceNumByEnterTime))?.Count() ?? 0;
                                            if (assignTypeSpace > inParkDetailCount)
                                            {
                                                item.orderdetail_IsCharge = 1;
                                                parkOrderIsLift = 1;
                                                if (inParkDetailSub != null) inParkDetailSub.orderdetail_IsCharge = 1;
                                            }
                                            else
                                            {
                                                item.orderdetail_IsCharge = 0;
                                                if (inParkDetailSub != null) inParkDetailSub.orderdetail_IsCharge = 0;
                                            }
                                        }
                                    }
                                }

                            });

                            details = details2;
                            details.AddRange(orderDetails_insert);

                            if (parkOrderIsLift > 0) parkorder.ParkOrder_IsLift = parkOrderIsLift;
                        }
                    }
                }
            }
            else
            {
                if (parkorder != null && parkorder.ParkOrder_IsLift > 0 && (carCardType != null && carCardType.CarCardType_IsMoreCar != 1 || newOwner == null || carCardType?.CarCardType_Type == 2))
                {
                    parkorder.ParkOrder_IsLift = 0;
                    details.ForEach(item => { item.orderdetail_IsCharge = 0; });
                }
            }
        }

        /// <summary>
        /// 导入车辆，修正停车订单（是否收费）
        /// </summary>
        /// <param name="carList">车辆信息</param>
        /// <param name="ownerList">车主信息</param>
        /// <param name="stopSpaceList">车位关联信息</param>
        /// <param name="carCardTypeList">车牌类型</param>
        /// <param name="parkOrderList">修正后的停车订单</param>
        /// <param name="orderDetailList">修正后的停车明细</param>
        public static void ImportCarToChangeOrder(List<Model.Car> carList, List<Model.Owner> ownerList, List<Model.StopSpace> stopSpaceList, List<Model.CarCardType> carCardTypeList,
            out List<Model.ParkOrder> parkOrderList, out List<Model.OrderDetail> orderDetailList)
        {

            parkOrderList = new List<Model.ParkOrder>();
            orderDetailList = new List<Model.OrderDetail>();

            if (carList == null || carList.Count == 0 || ownerList == null || ownerList.Count == 0) return;

            List<Model.InCar> inCarList = null;

            if (carList.Count < 1000)
                inCarList = BLL.BaseBLL._GetAllEntity(new Model.InCar(), "InCar_ParkOrderNo,InCar_CarNo", $"InCar_Status=200 and InCar_CarNo in ('{string.Join("','", carList.Select(x => x.Car_CarNo))}')");
            else
            {
                inCarList = BLL.BaseBLL._GetAllEntity(new Model.InCar(), "InCar_ParkOrderNo,InCar_CarNo", $"InCar_Status=200");
                var nolist = carList.Select(x => x.Car_CarNo).ToList();
                inCarList = inCarList.Where(x => nolist.Contains(x.InCar_CarNo)).ToList();
            }

            List<Model.ParkOrder> inParkOrderList = inCarList.Count > 0 ? BLL.ParkOrder.GetAllEntity("*", $"ParkOrder_No in ('{string.Join("','", inCarList.Select(x => x.InCar_ParkOrderNo))}')") : new List<Model.ParkOrder>();

            List<Model.OrderDetail> detailList = inCarList.Count > 0 ? BLL.OrderDetail.GetAllEntity("*", $"orderdetail_ParkOrderNo in ('{string.Join("','", inCarList.Select(x => x.InCar_ParkOrderNo))}')") : new List<Model.OrderDetail>();

            Model.ParkOrder order = null;
            List<Model.OrderDetail> detail = null;
            Model.Owner owner = null;
            Model.CarCardType carCardType = null;
            foreach (var car in carList)
            {
                detail = null;

                owner = ownerList.Find(x => x.Owner_No == car.Car_OwnerNo);
                if (owner == null) continue;

                carCardType = carCardTypeList.Find(x => x.CarCardType_No == owner.Owner_CardTypeNo);
                if (carCardType == null) continue;

                order = inParkOrderList.Find(x => x.ParkOrder_CarNo == car.Car_CarNo);
                if (order != null) detail = detailList.FindAll(x => x.OrderDetail_ParkOrderNo == order.ParkOrder_No)?.ToList();


                ModifyAnyToChangeOrder(car, owner, carCardType, ref order, ref detail, inCarList, inParkOrderList, detailList, stopSpaceList, carList);

                if (order != null) parkOrderList.Add(order);
                if (detail != null && detail.Count > 0) orderDetailList.AddRange(detail);

            }
            ;
        }

        public static void ImportCarToChangeOrder(List<Model.Car> carList, List<Model.Owner> ownerList, List<Model.StopSpace> stopSpaceList, List<Model.CarCardType> carCardTypeList,
          List<Model.ParkOrder> inParkOrderList, List<Model.OrderDetail> detailList, List<Model.InCar> inCarList, out List<Model.ParkOrder> parkOrderList, out List<Model.OrderDetail> orderDetailList)
        {
            parkOrderList = new List<Model.ParkOrder>();
            orderDetailList = new List<Model.OrderDetail>();

            if (carList == null || carList.Count == 0 || ownerList == null || ownerList.Count == 0) return;

            Model.ParkOrder order = null;
            List<Model.OrderDetail> detail = null;
            Model.Owner owner = null;
            Model.CarCardType carCardType = null;
            foreach (var car in carList)
            {
                detail = null;

                owner = ownerList.Find(x => x.Owner_No == car.Car_OwnerNo);
                if (owner == null) continue;

                carCardType = carCardTypeList.Find(x => x.CarCardType_No == owner.Owner_CardTypeNo);
                if (carCardType == null) continue;

                order = inParkOrderList.Find(x => x.ParkOrder_CarNo == car.Car_CarNo);
                if (order != null) detail = detailList.FindAll(x => x.OrderDetail_ParkOrderNo == order.ParkOrder_No)?.ToList();


                ModifyAnyToChangeOrder(car, owner, carCardType, ref order, ref detail, inCarList, inParkOrderList, detailList, stopSpaceList, carList);

                if (order != null) parkOrderList.Add(order);
                if (detail != null && detail.Count > 0) orderDetailList.AddRange(detail);

            }
            ;
        }

        /// <summary>
        /// 导入停车订单，修正停车订单（是否收费）
        /// </summary>
        /// <param name="carList">车辆信息</param>
        /// <param name="ownerList">车主信息</param>
        /// <param name="stopSpaceList">车位关联信息</param>
        /// <param name="carCardTypeList">车牌类型</param>
        /// <param name="parkOrderList">修正后的停车订单</param>
        /// <param name="orderDetailList">修正后的停车明细</param>
        public static void ImportRecordToChangeOrder(List<Model.Car> carList, List<Model.Owner> ownerList, List<Model.StopSpace> stopSpaceList, List<Model.CarCardType> carCardTypeList,
            ref List<Model.ParkOrder> parkOrderList, ref List<Model.OrderDetail> orderDetailList, List<Model.InCar> inCarList = null, List<Model.ParkOrder> inParkOrderList = null,
            List<Model.OrderDetail> detailList = null)
        {

            if (parkOrderList == null || orderDetailList == null) return;

            if (carList == null || carList.Count == 0 || ownerList == null || ownerList.Count == 0) return;

            if (carCardTypeList == null) carCardTypeList = BLL.CarCardType.GetAllEntity("*", "");

            //所有入场车辆
            if (inCarList == null) inCarList = BLL.BaseBLL._GetAllEntity(new Model.InCar(), "InCar_ParkOrderNo,InCar_CarNo,InCar_EnterTime,InCar_ParkAreaNo,InCar_Status,InCar_CarCardTypeNo", $"InCar_Status=200");
            var carnoes = carList.Select(x => x.Car_CarNo).ToList();
            inCarList = inCarList.Where(x => carnoes.Contains(x.InCar_CarNo)).ToList();
            //将要导入的入场记录也合并到Incar表
            parkOrderList.ForEach(item =>
            {
                inCarList.Add(new Model.InCar() { InCar_CarCardTypeNo = item.ParkOrder_CarCardType, InCar_Status = 200, InCar_ParkOrderNo = item.ParkOrder_No, InCar_CarNo = item.ParkOrder_CarNo, InCar_EnterTime = item.ParkOrder_EnterTime, InCar_ParkAreaNo = item.ParkOrder_ParkAreaNo });
            });
            inCarList = inCarList.Distinct().ToList();

            //所有入场订单
            if (inParkOrderList == null) inParkOrderList = inCarList.Count > 0 ? BLL.ParkOrder.GetAllEntity("*", $"ParkOrder_No in ('{string.Join("','", inCarList.Select(x => x.InCar_ParkOrderNo))}')") : new List<Model.ParkOrder>();
            if (detailList == null) detailList = inCarList.Count > 0 ? BLL.OrderDetail.GetAllEntity("*", $"orderdetail_ParkOrderNo in ('{string.Join("','", inCarList.Select(x => x.InCar_ParkOrderNo))}')") : new List<Model.OrderDetail>();

            Model.Owner owner = null;
            Model.CarCardType carCardType = null;
            Model.Car car = null;
            List<Model.ParkOrder> eachIndex_parkOrderList = null;
            List<Model.OrderDetail> eachIndex_detailList = null;
            List<Model.InCar> eachIndex_incarList = null;
            List<Model.OrderDetail> detail = null;

            //导入的订单合并当前场内的订单
            var allImportOrderNo = parkOrderList.Select(x => x.ParkOrder_CarNo).ToList();
            var newOrderList = parkOrderList;
            newOrderList.AddRange(inParkOrderList.Where(x => !allImportOrderNo.Contains(x.ParkOrder_CarNo)).ToList());
            //按入场时间排序
            newOrderList = newOrderList.OrderBy(x => x.ParkOrder_EnterTime).ToList();
            var newDetailList = orderDetailList;
            newDetailList.AddRange(detailList.Where(x => !allImportOrderNo.Contains(x.OrderDetail_CarNo)).ToList());

            for (var i = 0; i < newOrderList.Count; i++)
            {
                var item = newOrderList[i];

                //当前订单
                Model.ParkOrder order_copy = item.Copy();
                detail = newDetailList.Where(x => x.OrderDetail_ParkOrderNo == item.ParkOrder_No).ToList();
                List<Model.OrderDetail> detail_copy = detail.Copy();

                //当前订单入场前的其它订单
                eachIndex_incarList = inCarList.Where(x => x.InCar_EnterTime <= order_copy.ParkOrder_EnterTime).ToList();
                eachIndex_parkOrderList = newOrderList.Where(x => x.ParkOrder_EnterTime <= order_copy.ParkOrder_EnterTime).ToList();
                eachIndex_detailList = newDetailList.Where(x => x.OrderDetail_EnterTime <= order_copy.ParkOrder_EnterTime).ToList();

                //当前车主车辆信息
                car = carList.Find(x => x.Car_CarNo == item.ParkOrder_CarNo);
                if (car != null)
                {
                    owner = ownerList.Find(x => x.Owner_No == car.Car_OwnerNo);
                    if (owner != null) carCardType = carCardTypeList.Find(x => x.CarCardType_No == owner.Owner_CardTypeNo);
                }

                item.ParkOrder_OwnerNo = owner?.Owner_No;
                order_copy.ParkOrder_OwnerNo = owner?.Owner_No;
                //修改订单
                ModifyAnyToChangeOrder(car, owner, carCardType, ref order_copy, ref detail_copy, eachIndex_incarList, eachIndex_parkOrderList, eachIndex_detailList, stopSpaceList, carList, true);

                //判断订单是否修改过
                if (!Utils.ArePropertiesEqual(item, order_copy) || detail_copy.SequenceEqual(detail))
                {
                    var oModel = parkOrderList.Find(x => x.ParkOrder_No == item.ParkOrder_No);
                    if (oModel != null)
                    {
                        parkOrderList.Remove(item);
                    }
                    parkOrderList.Add(order_copy);
                    newOrderList[i] = order_copy;

                    var dModel = orderDetailList.FindAll(x => x.OrderDetail_ParkOrderNo == item.ParkOrder_No);
                    if (dModel != null)
                    {
                        orderDetailList = orderDetailList.Except(dModel).ToList();
                        newDetailList = newDetailList.Except(dModel).ToList();
                    }
                    orderDetailList.AddRange(detail_copy);
                    newDetailList.AddRange(detail_copy);
                }
            }
        }

        /// <summary>
        /// 修改场内停车订单信息（改车牌、场内状态、备注、车牌类型）
        /// </summary>
        /// <param name="model">场内停车订单</param>
        /// <param name="NewCarNo">新车牌</param>
        /// <param name="NewCarCardType">新车牌类型</param>
        /// <param name="NewEnterTime">新入场时间</param>
        /// <param name="ParkOrder_StatusNo">新订单状态</param>
        /// <param name="ParkOrder_Remark">新订单备注</param>
        /// <param name="UploadMsg">是否执行信息上传分发</param>
        /// <param name="newCartypeNo">车牌辆类型编号</param>
        /// <param name="CheckSpaceNumByEnterTime">是否按入场时间检查车位数量</param>
        /// <param name="lgAdmins">管理员信息</param>
        /// <param name="isSentrymodeify">是否为哨兵模式修改</param>
        /// <returns>Result</returns>

        public static Model.Result UpdateOrder(Model.ParkOrder model, string NewCarNo = null, string NewCarCardType = null, DateTime? NewEnterTime = null, int? ParkOrder_StatusNo = null, string ParkOrder_Remark = null, bool UploadMsg = false, string newCartypeNo = null, bool CheckSpaceNumByEnterTime = true, Model.AdminSession lgAdmins = null, bool isSentrymodeify = false)
        {
            Model.Result rs = new Model.Result();
            rs.code = 0;
            var oldCarNo = model.ParkOrder_CarNo;
            var oldCarCardType = model.ParkOrder_CarCardType;
            string logtxt = string.Empty;

            if ((NewCarNo == null || model.ParkOrder_CarNo == NewCarNo)
                   && (NewCarCardType == null || model.ParkOrder_CarCardType == NewCarCardType)
                   && (NewEnterTime == null || model.ParkOrder_EnterTime == NewEnterTime)
                   && (ParkOrder_StatusNo == null || model.ParkOrder_StatusNo == ParkOrder_StatusNo)
                   && (ParkOrder_Remark == null || model.ParkOrder_Remark == ParkOrder_Remark)) { rs.msg = "订单信息没有任何变更"; return rs; }


            Model.InCar incar = null;
            Model.Car newCar = BLL.Car.GetEntityByCarNo(model.ParkOrder_CarNo);
            Model.Owner newOwner = newCar != null ? BLL.Owner.GetEntity(newCar?.Car_OwnerNo) : null;
            Model.CarCardType carCardType = BLL.CarCardType.GetEntity(model.ParkOrder_CarCardType);

            logtxt += $"修改订单，订单编号：{model.ParkOrder_No};";
            if (!string.IsNullOrEmpty(NewCarNo) && model.ParkOrder_CarNo != NewCarNo) { logtxt += $"车牌号：{model.ParkOrder_CarNo}->{NewCarNo};"; }
            if (!string.IsNullOrEmpty(NewCarCardType) && model.ParkOrder_CarCardType != NewCarCardType) { var newcct = AppBasicCache.GetElement(AppBasicCache.GetCarcardTypes, NewCarCardType); logtxt += $"车牌类型：{carCardType.CarCardType_Name}->{newcct?.CarCardType_Name};"; }
            if (NewEnterTime != null && model.ParkOrder_EnterTime != NewEnterTime) { logtxt += $"入场时间：{model.ParkOrder_EnterTime}->{NewEnterTime.Value.ToString("yyyy-MM-dd HH:mm:ss")};"; }
            if (ParkOrder_StatusNo != null && model.ParkOrder_StatusNo != ParkOrder_StatusNo) { logtxt += $"订单状态：{Common.EnumHelper.GetParkOrderStatusName(model.ParkOrder_StatusNo)}->{Common.EnumHelper.GetParkOrderStatusName(ParkOrder_StatusNo)};"; }
            if (!string.IsNullOrEmpty(ParkOrder_Remark) && model.ParkOrder_Remark != ParkOrder_Remark) { logtxt += $"订单备注：{model.ParkOrder_Remark}->{ParkOrder_Remark};"; }
            BLL.UserLogs.AddLog(lgAdmins, isSentrymodeify ? LogEnum.Sentry : LogEnum.Backend, SecondOption.Update, logtxt, isSentrymodeify ? SecondIndex.Monitoring : SecondIndex.InParkRecord);

            bool isEditCarNo = false;//是否修改了车牌号码
            if (!string.IsNullOrWhiteSpace(NewCarNo) && model.ParkOrder_CarNo != NewCarNo)
            {
                Model.InCar inModel = BLL.BaseBLL._GetEntityByWhere(new Model.InCar(), "*", $"InCar_CarNo='{NewCarNo}'");
                if (inModel != null && inModel.InCar_Status == 200) { BLL.UserLogs.AddLog(lgAdmins, LogEnum.Backend, SecondOption.Update, $"车牌号[{NewCarNo}]已在场内", SecondIndex.InParkRecord); rs.msg = $"车牌号[{NewCarNo}]已在场内"; return rs; }

                incar = new InCar();
                incar.InCar_CarNo = model.ParkOrder_CarNo;
                incar.InCar_Status = Model.EnumParkOrderStatus.Close;
                incar.InCar_EnterTime = model.ParkOrder_EnterTime;
                incar.InCar_CarCardTypeNo = model.ParkOrder_CarCardType;
                incar.InCar_ParkAreaNo = model.ParkOrder_ParkAreaNo;

                isEditCarNo = true;
                model.ParkOrder_CarNo = NewCarNo;
                model.ParkOrder_IsModify = 1;

                newCar = BLL.Car.GetEntityByCarNo(NewCarNo);
                newOwner = BLL.Owner.GetEntity(newCar?.Car_OwnerNo);
            }

            if (string.IsNullOrEmpty(NewCarCardType))
            {
                if (newOwner != null) NewCarCardType = newOwner.Owner_CardTypeNo;
                else NewCarCardType = carCardType.CarCardType_No;
            }
            else
            {
                if (newOwner != null) NewCarCardType = newOwner.Owner_CardTypeNo;
            }

            if (!string.IsNullOrWhiteSpace(NewCarCardType) && model.ParkOrder_CarCardType != NewCarCardType)
            {
                carCardType = BLL.CarCardType.GetEntity(NewCarCardType);
                if (carCardType == null) { BLL.UserLogs.AddLog(lgAdmins, LogEnum.Backend, SecondOption.Update, $"车牌类型错误:{NewCarCardType}", SecondIndex.InParkRecord); rs.msg = $"车牌类型错误"; return rs; }
                model.ParkOrder_CarCardType = carCardType.CarCardType_No;
                model.ParkOrder_CarCardTypeName = carCardType.CarCardType_Name;
                model.ParkOrder_IsModify = 1;
            }

            if (!string.IsNullOrWhiteSpace(newCartypeNo) && model.ParkOrder_CarType != newCartypeNo)
            {
                var carTypeNew = BLL.CarType.GetEntity(newCartypeNo);
                model.ParkOrder_CarType = carTypeNew.CarType_No;
                model.ParkOrder_CarTypeName = carTypeNew.CarType_Name;
                model.ParkOrder_IsModify = 1;
            }

            if (ParkOrder_Remark != null) model.ParkOrder_Remark = ParkOrder_Remark;

            var details = BLL.OrderDetail.GetAllEntity(model.ParkOrder_No);
            if (details != null)
            {
                details.ForEach(x =>
                {
                    x.OrderDetail_CarNo = model.ParkOrder_CarNo;
                    x.OrderDetail_CarCardType = model.ParkOrder_CarCardType;
                    x.OrderDetail_CarCardTypeName = model.ParkOrder_CarCardTypeName;
                    x.OrderDetail_CarType = model.ParkOrder_CarType;
                    x.OrderDetail_CarTypeName = model.ParkOrder_CarTypeName;
                    x.Orderdetail_IsModify = 1;
                });
            }

            if (NewEnterTime != null && model.ParkOrder_EnterTime != NewEnterTime)
            {
                model.ParkOrder_EnterTime = NewEnterTime;
                model.ParkOrder_IsModify = 1;
                if (details != null && details.Count > 0)
                {
                    details.OrderBy(x => x.OrderDetail_EnterTime).First().OrderDetail_EnterTime = NewEnterTime;
                    details.OrderBy(x => x.OrderDetail_EnterTime).First().Orderdetail_IsModify = 1;
                }
            }

            bool isEditStatusNo = false;
            int? oldStatusNo = model.ParkOrder_StatusNo;
            if (ParkOrder_StatusNo != null && model.ParkOrder_StatusNo != ParkOrder_StatusNo)
            {
                isEditStatusNo = true;
                model.ParkOrder_IsModify = 1;
                model.ParkOrder_StatusNo = ParkOrder_StatusNo;
                if (ParkOrder_StatusNo == EnumParkOrderStatus.Out) model.ParkOrder_OutTime = DateTime.Now;
                if (details != null && details.Count > 0)
                {
                    details.OrderBy(x => x.OrderDetail_EnterTime).Last().OrderDetail_StatusNo = ParkOrder_StatusNo;
                    details.OrderBy(x => x.OrderDetail_EnterTime).Last().Orderdetail_IsModify = 1;
                }
            }

            List<Model.ParkOrder> orders = null;
            List<Model.ParkOrder> otherOrders = null;
            List<Model.OrderDetail> otherDetails = null;

            //多车多位处理 场内
            if (model.ParkOrder_StatusNo == 200)
            {
                //旧车牌修改成别的车牌后，对一个车主下的车位其它车辆的影响
                if (isEditCarNo)
                {
                    var oldCar = AppBasicCache.GetElement(AppBasicCache.GetCar, oldCarNo);
                    if (oldCar == null || oldCar.Car_OwnerNo != newCar?.Car_OwnerNo)
                    {
                        var currOrder = model.Copy();
                        currOrder.ParkOrder_CarNo = oldCarNo;
                        currOrder.ParkOrder_CarCardType = oldCarCardType;
                        currOrder.ParkOrder_StatusNo = 201;

                        var resultItem = ChangeOutForOtherCar(isEditCarNo, oldCarNo, currOrder);
                        otherOrders = resultItem.orders;
                        otherDetails = resultItem.details;
                    }
                }

                //对当前车牌记录的影响
                BLL.ParkOrder.ModifyAnyToChangeOrder(newCar, newOwner, carCardType, ref model, ref details, CheckSpaceNumByEnterTime: CheckSpaceNumByEnterTime);

            }
            //多车多位处理 出场
            else if (isEditStatusNo && model.ParkOrder_StatusNo == 201)
            {
                var currOrder = model.Copy();
                if (isEditCarNo) { currOrder.ParkOrder_CarNo = oldCarNo; }

                var resultItem = ChangeOutForOtherCar(isEditCarNo, oldCarNo, currOrder);
                if (isEditCarNo)
                {
                    otherOrders = resultItem.orders;
                    otherDetails = resultItem.details;
                }
                else
                {
                    if (resultItem.orders?.Count > 0)
                    {
                        orders = resultItem.orders;
                    }
                    if (resultItem.details?.Count > 0)
                    {
                        details = details == null ? new List<Model.OrderDetail>() : details;
                        details.AddRange(resultItem.details);
                    }
                }
            }

            orders = orders == null ? new List<Model.ParkOrder>() : orders;
            orders.Add(model);

            if (isEditCarNo)
            {
                if (otherOrders?.Count > 0)
                {
                    orders.AddRange(otherOrders);
                }

                if (otherDetails?.Count > 0)
                {
                    details = details == null ? new List<Model.OrderDetail>() : details;
                    details.AddRange(otherDetails);
                }

            }

            //人工修改车牌导致重复入场,则将前一条入场记录作自动关闭处理
            var gg = BLL.ParkOrder.CloseParkOrder(model.ParkOrder_ParkNo, model.ParkOrder_CarNo, model.ParkOrder_No, out var closeOrder, out var closeDetails);
            if (gg)
            {
                orders.Add(closeOrder);
                if (closeDetails != null)
                    details?.AddRange(closeDetails);
            }

            List<Model.CouponRecord> couponList = new List<Model.CouponRecord>();
            if (orders != null)
            {
                couponList = BLL.CouponRecord._GetAllEntity(new Model.CouponRecord(), "*", $" CouponRecord_ParkOrderNo in('{model.ParkOrder_No}') ");
            }

            couponList?.ForEach(item =>
            {
                if (item != null)
                    item.CouponRecord_IssueCarNo = orders.Where(a => a.ParkOrder_No == item.CouponRecord_ParkOrderNo).FirstOrDefault().ParkOrder_CarNo;
            });

            var res = BLL.ParkOrder.UpdateAndCoupon(orders, details, couponList, incar);
            if (res > 0)
            {
                rs.code = 1;
                rs.data = (orders, details, couponList, incar);

                if (UploadMsg)
                {
                    _ = CustomThreadPool.SyncTaskPool?.QueueTask(null, () =>
                     {
                         try
                         {
                             if (orders != null)
                             {
                                 BLL.MiddlewareApi.CarIn((orders, details), AppBasicCache.GetParking.Parking_No);

                                 if (couponList?.Count > 0)
                                     BLL.MiddlewareApi.CouponRecord(couponList, AppBasicCache.GetParking.Parking_No);

                                 if (!string.IsNullOrWhiteSpace(NewCarNo) && oldCarNo != NewCarNo)
                                     BLL.PushEvent.UpdateCar(AppBasicCache.GetParking.Parking_Key, model.ParkOrder_No, model.ParkOrder_CarNo);

                                 if (isEditStatusNo && ParkOrder_StatusNo == EnumParkOrderStatus.Out)
                                     BLL.PushEvent.CloseCar(AppBasicCache.GetParking.Parking_Key, orders.LastOrDefault(), "场内修改自动离场");
                             }
                         }
                         catch (Exception ex)
                         {
                             BLL.UserLogs.AddLog(lgAdmins, LogEnum.Backend, SecondOption.Update, $"修改车牌异常【PullUpdateCarNo】[{model.ParkOrder_No}][{NewCarNo}][{NewCarCardType}]：" + ex.ToString(), SecondIndex.InParkRecord);
                             LogManagementMap.WriteToFile(LoggerEnum.MainErrorLog, $"修改车牌异常【PullUpdateCarNo】[{model.ParkOrder_No}][{NewCarNo}][{NewCarCardType}]：" + ex.ToString());
                         }
                         return Task.CompletedTask;
                     });
                }
            }
            else
            {
                BLL.UserLogs.AddLog(lgAdmins, LogEnum.Backend, SecondOption.Update, $"执行SQL错误，请重试", SecondIndex.InParkRecord);
                rs.msg = "执行SQL错误，请重试";
            }
            return rs;
        }

        /// <summary>
        /// 改车牌后，原车辆车主的其它车辆多车多位智能升降处理
        /// </summary>
        /// <param name="isEditCarNo"></param>
        /// <param name="oldCarNo"></param>
        /// <param name="currOrder"></param>
        /// <param name="orders"></param>
        /// <param name="details"></param>
        private static (List<Model.ParkOrder> orders, List<Model.OrderDetail> details) ChangeOutForOtherCar(bool isEditCarNo, string oldCarNo, Model.ParkOrder currOrder)
        {
            List<Model.ParkOrder> neworders = null;
            List<Model.OrderDetail> newDetails = null;
            string outAreaNo = "";
            if (!string.IsNullOrEmpty(currOrder.ParkOrder_EnterPasswayNo))
            {
                var inPss = AppBasicCache.GetElement(AppBasicCache.GetAllPassway, currOrder.ParkOrder_EnterPasswayNo);
                if (inPss != null)
                {
                    var inPassLink = AppBasicCache.GetAllPasswayLink.Values.Where(x => x.PasswayLink_PasswayNo == inPss.Passway_No && x.PasswayLink_GateType == 1).FirstOrDefault();
                    if (inPassLink != null)
                    {
                        var inArea = AppBasicCache.GetElement(AppBasicCache.GetParkAreas, inPassLink.PasswayLink_ParkAreaNo);
                        if (inArea != null)
                        {
                            if (inArea.ParkArea_Level == 0)
                            {
                                outAreaNo = inArea.ParkArea_No;
                            }
                        }
                    }
                }


                if (string.IsNullOrEmpty(outAreaNo))
                {
                    var outArea = AppBasicCache.GetParkAreas.Values.Where(x => x.ParkArea_Level == 0).FirstOrDefault();
                    if (outArea != null)
                    {
                        outAreaNo = outArea.ParkArea_No;
                    }
                }
            }

            if (!string.IsNullOrEmpty(outAreaNo))
            {
                //一位多车处理
                BLL.ParkOrder.OutParkForMultiCar(currOrder, null, outAreaNo, (pLst, dLst) =>
                {
                    neworders = BLL.ParkOrder.ChangeOrderLst(currOrder, pLst);
                    newDetails = BLL.ParkOrder.ChangeDetailLst(newDetails, dLst);

                    if (isEditCarNo)
                    {
                        neworders = neworders.Where(x => x.ParkOrder_CarNo != oldCarNo).ToList();
                        newDetails = newDetails?.Where(x => x.OrderDetail_CarNo != oldCarNo).ToList();
                    }
                });
            }

            return (neworders, newDetails);
        }

        /// <summary>
        /// 订单多车多位状态修改
        /// </summary>
        private static void ChangeOrderByCarSpace(List<Model.StopSpace> stopsapce, ref Model.ParkOrder parkorder, ref List<Model.OrderDetail> details)
        {
            parkorder.ParkOrder_IsLift = 1;//订单标识多位多车

            //车主车位类型属于所有区域
            if (stopsapce.Find(x => x.StopSpace_Type == 0 && x.StopSpace_Number > 0) != null)
            {
                details.ForEach(x => { x.orderdetail_IsCharge = 1; });
            }
            //车位类型特定区域
            else
            {
                details.ForEach(item =>
                {
                    //特定区域有车位
                    if (stopsapce.Find(x => x.StopSpace_Type == 1 && x.StopSpace_Number > 0 && x.StopSpace_AreaNo.IndexOf(item.OrderDetail_ParkAreaNo) != -1) != null)
                    {
                        item.orderdetail_IsCharge = 1;
                    }
                    //特定区域无车位
                    else
                    {
                        item.orderdetail_IsCharge = 0;
                    }
                });
            }
        }

        /// <summary>
        /// 用于弹窗取消后，修改订单金额
        /// </summary>
        /// <param name="orderNo"></param>
        /// <param name="po">数据库查询不到订单，才会用到</param>
        /// <param name="writeDB"></param>
        /// <param name="isSyncSentry"></param>
        /// <returns></returns>
        public static Model.ParkOrder UpdatePayMoney(string orderNo, Model.ParkOrder po = null, bool writeDB = true, bool isSyncSentry = false, bool updateOutType = false)
        {
            try
            {
                if (string.IsNullOrEmpty(orderNo)) return po;

                var oldPo = BLL.ParkOrder.GetEntity(orderNo);
                if (oldPo == null) oldPo = po;
                if (oldPo == null) return po;

                List<Model.PayOrder> payorderList = BLL.PayOrder.GetAllEntity("PayOrder_Money,PayOrder_PayedMoney", $"PayOrder_ParkOrderNo='{oldPo.ParkOrder_No}' and PayOrder_Status=1");

                var newPo = oldPo.Copy();
                newPo.ParkOrder_TotalAmount = payorderList.Sum(x => x.PayOrder_Money) ?? 0;
                newPo.ParkOrder_TotalPayed = payorderList.Sum(x => x.PayOrder_PayedMoney) ?? 0;
                if (writeDB && (newPo.ParkOrder_TotalAmount != oldPo.ParkOrder_TotalAmount || newPo.ParkOrder_TotalPayed != oldPo.ParkOrder_TotalPayed))
                {
                    var sql = $"UPDATE parkorder SET ParkOrder_TotalAmount={newPo.ParkOrder_TotalAmount}" +
                            $",ParkOrder_TotalPayed={newPo.ParkOrder_TotalPayed} ";
                    if (updateOutType) sql += $",ParkOrder_OutType='{po.ParkOrder_OutType}' ";
                    sql += $" WHERE ParkOrder_No='{newPo.ParkOrder_No}'";
                    var result = BLL.BaseBLL._ExceteBySql(sql);//BLL.ParkOrder._UpdateByModelByNo(newPo);
                    if (result > 0)
                    {
                        //if (isSyncSentry) BLL.MiddlewareApi.UpdateOrder(newPo);
                        return newPo;
                    }
                    else
                    {
                        LogManagementMap.WriteToFile(LoggerEnum.MainErrorLog, $"[{orderNo}]更新停车订单金额失败");
                    }
                }
                else
                {
                    return newPo;
                }
            }
            catch (Exception ex)
            {
                LogManagementMap.WriteToFile(LoggerEnum.MainErrorLog, $"[{orderNo}]更新停车订单金额异常：" + ex.ToString());
            }

            return po;
        }

        /// <summary>
        /// 添加开闸缓存（记录有开闸，后面接收到缴费，就获取当前的开闸缓存的通行结果进行出场操作）
        /// </summary>
        /// <param name="sOrderNo"></param>
        /// <param name="result"></param>
        public static void AddOpenOutGateCache(string sOrderNo, Model.ResultPass result)
        {
            if (string.IsNullOrEmpty(sOrderNo)) return;

            AppBasicCache.AddOrUpdateElement(AppBasicCache.GetOutParkOrder, sOrderNo, new Tuple<DateTime, ResultPass>(DateTime.Now, result));
            AppBasicCache.GetOutParkOrder.AsParallel().ForAll(m =>
            {
                if (m.Value.Item1 < DateTime.Now.AddMinutes(-30))
                {
                    AppBasicCache.GetOutParkOrder.TryRemove(m.Key, out var v);
                }
            });
            if (AppBasicCache.GetOutParkOrder.Count > 100)
            {
                var oldestKey = AppBasicCache.GetOutParkOrder.OrderBy(kvp => Utils.ObjectToDecimal(kvp.Key.Split('-')[0], 0)).FirstOrDefault().Key;
                if (!string.IsNullOrEmpty(oldestKey)) AppBasicCache.GetOutParkOrder.TryRemove(oldestKey, out var removedValue);
            }
        }

        public static int Update(Model.ParkOrder model)
        {
            return dal.Update(model);
        }
    }
}
